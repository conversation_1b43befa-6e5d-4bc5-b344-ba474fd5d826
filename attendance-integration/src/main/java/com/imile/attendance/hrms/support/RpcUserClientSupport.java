package com.imile.attendance.hrms.support;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.hrms.dto.UserDynamicInfoRpcDTO;
import com.imile.attendance.hrms.mapstruct.RpcUserClientMapstruct;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> chen
 * @Date 2025/08/05
 * @Time 19:11
 * @Description
 */
@Component
public class RpcUserClientSupport {

    @Resource
    private RpcUserClient rpcUserClient;

    @Cached(name = Constants.CacheKey.RPC_USER_ID_CACHE_KEY,
            key = "#userId",
            cacheType = CacheType.REMOTE,
            expire = 15,
            timeUnit = TimeUnit.MINUTES
    )
    public UserDynamicInfoRpcDTO getUserDynamicInfoById(Long userId) {
        if (userId == null) {
            return null;
        }
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = listUserDynamicInfoById(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userDynamicInfoRpcDTOList)) {
            return null;
        }
        return userDynamicInfoRpcDTOList.get(0);
    }


    @Cached(name = Constants.CacheKey.RPC_USER_CODE_CACHE_KEY,
            key = "#userCode",
            cacheType = CacheType.REMOTE,
            expire = 15,
            timeUnit = TimeUnit.MINUTES
    )
    public UserDynamicInfoRpcDTO getUserDynamicInfoByCode(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = listUserDynamicInfo(Collections.singletonList(userCode));
        if (CollectionUtils.isEmpty(userDynamicInfoRpcDTOList)) {
            return null;
        }
        return userDynamicInfoRpcDTOList.get(0);
    }


    /**
     * 获取人员企微ID映射
     *
     * @param userCode 人员编码
     * @return Map
     */
    @Cached(name = Constants.CacheKey.HRMS_USER_WECOM_CACHE_KEY,
            key = "#userCode",
            cacheType = CacheType.REMOTE,
            expire = 15,
            timeUnit = TimeUnit.MINUTES
    )
    public String getUserWecomId(String userCode) {
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        Map<String, String> userWecomIdMap = rpcUserClient.getUserWecomIdMap(Collections.singletonList(userCode));
        if (MapUtils.isNotEmpty(userWecomIdMap)) {
            return userWecomIdMap.get(userCode);
        }
        return null;
    }

    /**
     * 获取人员企微ID映射
     *
     * @param userCodes 人员编码列表
     * @return Map
     */
    public Map<String, String> getUserWecomIdMap(List<String> userCodes) {
        return rpcUserClient.getUserWecomIdMap(userCodes);
    }


    public List<UserDynamicInfoRpcDTO> listUserDynamicInfo(List<String> userCodeList) {
        List<UserDynamicInfoDTO> userDynamicInfoDTOS = rpcUserClient.listUserDynamicInfo(userCodeList,
                Arrays.asList(UserDynamicFieldEnum.LOCATION_COUNTRY,
                        UserDynamicFieldEnum.LOCATION_PROVINCE,
                        UserDynamicFieldEnum.LOCATION_CITY,
                        UserDynamicFieldEnum.DEPT_CODE));
        return RpcUserClientMapstruct.INSTANCE.mapToUserDynamicInfoRpcDTO(userDynamicInfoDTOS);
    }


    /**
     * 根据人员ID批量获取人员动态业务信息
     *
     * @param idList 人员ID列表（一次性不能超过1000，自行分批）
     * @return List<UserDynamicInfoDTO>
     */
    public List<UserDynamicInfoRpcDTO> listUserDynamicInfoById(List<Long> idList) {
        List<UserDynamicInfoDTO> userDynamicInfoDTOList = rpcUserClient.listUserDynamicInfoById(idList, Arrays.asList(UserDynamicFieldEnum.LOCATION_COUNTRY,
                UserDynamicFieldEnum.LOCATION_PROVINCE,
                UserDynamicFieldEnum.LOCATION_CITY,
                UserDynamicFieldEnum.DEPT_CODE));
        return RpcUserClientMapstruct.INSTANCE.mapToUserDynamicInfoRpcDTO(userDynamicInfoDTOList);
    }

}
