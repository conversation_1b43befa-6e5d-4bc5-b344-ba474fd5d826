package com.imile.attendance.hrms.support;

import com.imile.attendance.hrms.RpcPlatformRelationClient;
import com.imile.hrms.api.platform.PlatformRelationApiQuery;
import com.imile.hrms.api.platform.dto.PlatformRelationApiDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/29
 */
@Deprecated
@Component
public class RpcPlatformRelationClientSupport {

    @Resource
    private RpcPlatformRelationClient platformRelationClient;

    public List<PlatformRelationApiDTO> listPlatFormRelation(List<String> bizIds, String bizType, List<String> relationIds, String platformType) {
        PlatformRelationApiQuery query = new PlatformRelationApiQuery();
        query.setBizIds(bizIds);
        query.setBizType(bizType);
        query.setRelationIds(relationIds);
        query.setPlatFormType(platformType);
        return platformRelationClient.listPlatFormRelation(query);
    }
}
