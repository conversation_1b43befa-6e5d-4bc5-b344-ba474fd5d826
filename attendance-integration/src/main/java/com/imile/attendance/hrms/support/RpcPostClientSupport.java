package com.imile.attendance.hrms.support;

import com.imile.attendance.hrms.RpcPostClient;
import com.imile.common.enums.StatusEnum;
import com.imile.hrms.api.base.param.PostConditionParam;
import com.imile.hrms.api.base.result.PostDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Component
public class RpcPostClientSupport {

    @Resource
    private RpcPostClient rpcPostClient;

    /**
     * 根据岗位ID列表查询岗位信息
     *
     * @param postIds 岗位ID列表
     * @param status  岗位状态
     * @return 岗位信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<PostDTO> listByPostList(List<Long> postIds, String status) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        // 去重
        List<String> postCodes = postIds.stream()
                .filter(Objects::nonNull)
                .distinct()
                .map(String::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(postCodes)) {
            return Collections.emptyList();
        }
        PostConditionParam postConditionParam = new PostConditionParam();
        postConditionParam.setPostCodeList(postCodes);
        postConditionParam.setStatus(status);
        return rpcPostClient.listPostByCondition(postConditionParam);
    }
}
