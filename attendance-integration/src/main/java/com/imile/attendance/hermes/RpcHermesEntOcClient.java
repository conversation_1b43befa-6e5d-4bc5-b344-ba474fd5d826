package com.imile.attendance.hermes;

import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.hermes.enterprise.dto.EntOcQueryApiDTO;
import com.imile.rpc.common.RpcResult;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
public interface RpcHermesEntOcClient {

    /**
     * 根据网点编码查询网点信息
     */
    EntOcApiDTO getOcByCode(Long orgId, String ocCode);


    /**
     * 根据网点编码查询网点信息
     */
    List<EntOcApiDTO> getOcByCodes(Long orgId, List<String> ocCodes);


    /**
     * 根据网点ID查询网点信息
     */
    EntOcApiDTO getOcById(Long orgId, Long ocId);

    /**
     * 根据网点批量查询网点信息
     */
    List<EntOcApiDTO> getOcByIds(Long orgId, List<Long> ocIds);

    /**
     * 根据网点编码查询网点信息
     */
    List<EntOcApiDTO> getOcList(Long orgId, EntOcQueryApiDTO param);
}
