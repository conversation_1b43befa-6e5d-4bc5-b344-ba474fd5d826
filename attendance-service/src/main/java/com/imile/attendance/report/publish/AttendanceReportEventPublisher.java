package com.imile.attendance.report.publish;

import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.event.AttendanceReportRegisterEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/4
 * @Description
 */
@Service
public class AttendanceReportEventPublisher {

    @Resource
    private ApplicationEventPublisher publisher;

    public void sendReportEvent(List<DayReportJobParam> param) {
        publisher.publishEvent(new AttendanceReportRegisterEvent(this, param));
    }
}
