package com.imile.attendance.abnormal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.abnormal.dto.AbnormalAttendanceCountDTO;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AbnormalOperationRecordDTO;
import com.imile.attendance.abnormal.dto.EmployeeAbnormalAttendanceQueryDTO;
import com.imile.attendance.abnormal.param.AbnormalAttendanceBatchUpdateParam;
import com.imile.attendance.abnormal.param.AbnormalAttendanceDayReminderParam;
import com.imile.attendance.abnormal.param.AbnormalAttendanceReminderParam;
import com.imile.attendance.abnormal.param.AbnormalAttendanceSingleUpdateParam;
import com.imile.attendance.abnormal.param.DayAttendanceTimeParam;
import com.imile.attendance.abnormal.service.AttendanceAbnormalRemindService;
import com.imile.attendance.abnormal.vo.AbnormalDetailVO;
import com.imile.attendance.abnormal.vo.DayAttendanceTimeVO;
import com.imile.attendance.abnormal.vo.EmployeeBaseInfoVO;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.AttendanceDataSourceEnum;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseAbnormalStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseAttendanceStatusEnum;
import com.imile.attendance.enums.warehouse.WarehouseStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.RpcHermesVendorClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormAttrDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormRelationDao;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseRecordDao;
import com.imile.attendance.infrastructure.repository.warehouse.dto.WarehouseDetailAbnormalDTO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.param.WarehouseDetailAbnormalParam;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.publish.AttendanceReportEventPublisher;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.user.dto.AttachmentDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.warehouse.WarehouseAttendanceDurationCalculateService;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.attendance.warehouse.vo.WarehouseAbnormalDetailVO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.vendor.dto.VendorInfoSimpleApiDTO;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/13
 * @Description 异常处理相关服务
 */
@Slf4j
@Service
public class EmployeeAbnormalAttendanceService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private AttendanceDeptService attendanceDeptService;
    @Resource
    private AttendancePostService attendancePostService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceAbnormalRemindService attendanceAbnormalRemindService;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private AttendanceFormAttrDao attendanceFormAttrDao;
    @Resource
    private AttendanceFormRelationDao attendanceFormRelationDao;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private RpcBpmApprovalClient rpcBpmApprovalClient;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private AttendanceReportEventPublisher publisher;
    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;
    @Resource
    private WarehouseAttendanceDurationCalculateService attendanceDurationCalculateService;
    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseRecordDao warehouseRecordDao;
    @Resource
    private WarehouseUserService warehouseUserService;
    @Resource
    private RpcHermesVendorClient vendorClient;
    @Resource
    private AttendanceDeptService deptService;

    //仓内来源
    private static final Integer WAREHOUSE_SOURCE = 1;


    /**
     * 异常分页
     */
    public PaginationResult<EmployeeAbnormalAttendanceDTO> page(EmployeeAbnormalAttendanceQueryDTO queryDTO) {
        EmployeeAbnormalAttendancePageQuery query = BeanUtils.convert(queryDTO, EmployeeAbnormalAttendancePageQuery.class);
        query.setLocationCountry(queryDTO.getCountry());
        // 权限封装
        PaginationResult<EmployeeAbnormalAttendanceDTO> pageQuery = buildAuthQuery(query);
        if (pageQuery != null) {
            return pageQuery;
        }
        // 数据库查找记录
        PageInfo<EmployeeAbnormalAttendanceDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0)
                .doSelectPageInfo(() -> employeeAbnormalAttendanceDao.list(query));
        List<EmployeeAbnormalAttendanceDTO> abnormalAttendanceDTOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(abnormalAttendanceDTOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        Set<Long> deptIdList = new HashSet<>();
        deptIdList.addAll(abnormalAttendanceDTOList
                .stream()
                .map(EmployeeAbnormalAttendanceDTO::getDeptId)
                .distinct()
                .collect(Collectors.toList()));

        List<Long> abnormalIdList = abnormalAttendanceDTOList.stream().map(EmployeeAbnormalAttendanceDTO::getId).collect(Collectors.toList());
        Map<Long, VendorInfoSimpleApiDTO> vendorInfoMap = new HashMap<>();
        Map<Long, String> punchClassConfigMap = new HashMap<>();
        Map<Long, WarehouseDetailAbnormalDTO> warehouseDetailAbnormalMap = new HashMap<>();
        if (Objects.nonNull(queryDTO.getSource()) && Objects.equals(WAREHOUSE_SOURCE, query.getSource())) {
            List<Long> classIdList = abnormalAttendanceDTOList.stream().map(EmployeeAbnormalAttendanceDTO::getPunchClassConfigId).distinct().collect(Collectors.toList());
            List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(classIdList);
            if (CollectionUtils.isNotEmpty(punchClassConfigDOList)) {
                punchClassConfigMap = punchClassConfigDOList.stream().collect(Collectors.toMap(PunchClassConfigDO::getId, PunchClassConfigDO::getClassName));
            }

            WarehouseDetailAbnormalParam param = new WarehouseDetailAbnormalParam();
            param.setAbnormalIdList(abnormalIdList);
            List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalDTOS = warehouseDetailAbnormalDao.selectJoinWarehouseDetailList(param);
            if (CollectionUtils.isNotEmpty(warehouseDetailAbnormalDTOS)) {
                warehouseDetailAbnormalMap = warehouseDetailAbnormalDTOS.stream().collect(Collectors.toMap(WarehouseDetailAbnormalDTO::getAbnormalId, Function.identity(), (o1, o2) -> o1));
                //查询部门信息
                Set<Long> deptIds = warehouseDetailAbnormalDTOS
                        .stream()
                        .map(WarehouseDetailAbnormalDTO::getOcId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                deptIdList.addAll(deptIds);

                List<Long> vendorIds = warehouseDetailAbnormalDTOS
                        .stream()
                        .map(WarehouseDetailAbnormalDTO::getVendorId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                List<VendorInfoSimpleApiDTO> vendorInfoSimpleApiDTOS = vendorClient.selectVendorList(vendorIds);
                vendorInfoMap = vendorInfoSimpleApiDTOS.stream().collect(Collectors.toMap(VendorInfoSimpleApiDTO::getVendorId, o -> o, (v1, v2) -> v1));
            }
        }


        // 获取处理信息
        Map<Long, AttendanceDept> attendanceDeptMap = attendanceDeptService.listByDeptIdsAndStatus(new ArrayList<>(deptIdList), BusinessConstant.ALL_STATUS_LIST)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity(), (v1, v2) -> v1));
        Map<Long, WarehouseDetailAbnormalDTO> finalWarehouseDetailAbnormalMap = warehouseDetailAbnormalMap;
        Map<Long, VendorInfoSimpleApiDTO> finalVendorInfoMap = vendorInfoMap;
        Map<Long, String> finalPunchClassConfigMap = punchClassConfigMap;
        abnormalAttendanceDTOList.forEach(o -> {
            AttendanceDept deptDO = attendanceDeptMap.get(o.getDeptId());
            if (Objects.nonNull(deptDO)) {
                o.setDeptName(RequestInfoHolder.isChinese() ? deptDO.getDeptNameCn() : deptDO.getDeptNameEn());
            }

            WarehouseDetailAbnormalDTO warehouseDetailAbnormal = finalWarehouseDetailAbnormalMap.get(o.getId());
            if (Objects.nonNull(warehouseDetailAbnormal)) {
                AttendanceDept ocInfo = attendanceDeptMap.getOrDefault(warehouseDetailAbnormal.getOcId(), new AttendanceDept());
                o.setOcName(RequestInfoHolder.isChinese() ? ocInfo.getDeptNameCn() : ocInfo.getDeptNameEn());

                VendorInfoSimpleApiDTO vendorInfo = finalVendorInfoMap.getOrDefault(warehouseDetailAbnormal.getVendorId(), new VendorInfoSimpleApiDTO());
                o.setVendorName(vendorInfo.getVendorName());

                //用工形式
                o.setEmploymentForm(warehouseDetailAbnormal.getEmploymentForm());
            }
            o.setClassName(finalPunchClassConfigMap.get(o.getPunchClassConfigId()));

            if (StringUtils.isNotBlank(o.getAbnormalType())) {
                AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(o.getAbnormalType());
                if (Objects.nonNull(abnormalTypeEnum)) {
                    o.setAbnormalTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getDescEn());
                }
            }

            AbnormalAttendanceStatusEnum statusEnum = AbnormalAttendanceStatusEnum.getInstanceByCode(o.getStatus());
            if (Objects.nonNull(statusEnum)) {
                o.setStatusDesc(RequestInfoHolder.isChinese() ? statusEnum.getDesc() : statusEnum.getDescEn());
            }
        });
        return PageUtil.getPageResult(abnormalAttendanceDTOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 异常明细
     */
    public AbnormalDetailVO getAbnormalDetail(Long abnormalId) {
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = employeeAbnormalAttendanceDao.selectById(abnormalId);
        if (abnormalAttendanceDO == null) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        AbnormalDetailVO result = new AbnormalDetailVO();
        //员工基本信息
        EmployeeBaseInfoVO employeeBaseInfoVO = buildEmployeeBaseInfo(abnormalAttendanceDO, userInfoDO, false, null);
        result.setEmployeeBaseInfo(employeeBaseInfoVO);
        //考勤配置信息
        buildAttendanceRuleConfigInfo(abnormalAttendanceDO, userInfoDO, result);
        //考勤异常打卡信息
        buildAttendanceAbnormalInfo(abnormalAttendanceDO, result);
        //异常操作记录
        List<AbnormalOperationRecordDTO> abnormalOperationRecordList = buildAbnormalOperationRecordDTO(abnormalAttendanceDO);
        result.setAbnormalOperationRecordDTOList(abnormalOperationRecordList);
        return result;
    }

    /**
     * 仓内异常明细
     */
    public WarehouseAbnormalDetailVO getWarehouseAbnormalDetail(Long abnormalId) {
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = employeeAbnormalAttendanceDao.selectById(abnormalId);
        if (abnormalAttendanceDO == null) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        WarehouseAbnormalDetailVO result = new WarehouseAbnormalDetailVO();
        WarehouseDetailAbnormalParam param = new WarehouseDetailAbnormalParam();
        param.setAbnormalIdList(Collections.singletonList(abnormalAttendanceDO.getId()));
        List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectJoinWarehouseDetailList(param);
        //员工基本信息
        EmployeeBaseInfoVO employeeBaseInfoVO = buildEmployeeBaseInfo(abnormalAttendanceDO, userInfoDO, true, warehouseDetailAbnormalList);
        result.setEmployeeBaseInfo(employeeBaseInfoVO);
        //异常操作记录
        buildWarehouseAbnormalOperationRecordDTO(abnormalAttendanceDO, result);
        //打卡记录
        List<WarehouseAbnormalDetailVO.WarehouseRecordDTO> warehouseRecordVOList = buildWarehouseRecordVO(warehouseDetailAbnormalList);
        result.setWarehouseRecordList(warehouseRecordVOList);
        //考勤异常打卡信息
        buildAttendanceAbnormalInfo(abnormalAttendanceDO, warehouseDetailAbnormalList, result);
        return result;
    }

    private void buildWarehouseAbnormalOperationRecordDTO(EmployeeAbnormalAttendanceDO abnormalAttendanceDO, WarehouseAbnormalDetailVO result) {
        List<AbnormalOperationRecordDTO> abnormalOperationRecordDTOList = new ArrayList<>();
        List<WarehouseAbnormalDetailVO.UserDayFormDTO> userDayFormDTOList = new ArrayList<>();
        result.setAbnormalOperationRecordDTOList(abnormalOperationRecordDTOList);
        result.setUserDayFormDTOList(userDayFormDTOList);
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDao.selectByAbnormalList(Collections.singletonList(abnormalAttendanceDO.getId()))
                .stream()
                .sorted(Comparator.comparing(EmployeeAbnormalOperationRecordDO::getId).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalOperationRecordList)) {
            return;
        }
        List<Long> formIdList = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getFormId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, List<AttendanceFormDO>> formMap = attendanceFormDao.selectByIds(formIdList).stream().collect(Collectors.groupingBy(AttendanceFormDO::getId));
        Map<Long, List<AttendanceFormAttrDO>> attrMap = attendanceFormAttrDao.selectFormAttrByFormIdList(formIdList)
                .stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        for (EmployeeAbnormalOperationRecordDO recordDO : abnormalOperationRecordList) {
            AbnormalOperationRecordDTO dto = new AbnormalOperationRecordDTO();
            dto.setLastUpdDate(recordDO.getLastUpdDate());
            dto.setLastUpdUserCode(recordDO.getLastUpdUserCode());
            dto.setLastUpdUserName(recordDO.getLastUpdUserName());
            if (RequestInfoHolder.isChinese() && Objects.equals(BusinessConstant.SUPPLIER, recordDO.getLastUpdUserName())) {
                dto.setLastUpdUserName("供应商");
            }
            dto.setOperationType(recordDO.getOperationType());
            //异常确认
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.OFF.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.PH.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.P.getCode())) {
                abnormalOperationRecordDTOList.add(dto);
                dto.setOperationStatus(FormStatusEnum.PASS.getCode());
                // 批量异常处理为正常的设置原因到处理内容上
                dto.setOperationContent(recordDO.getReason());
                if (ObjectUtil.isNotNull(recordDO.getAttachment())) {
                    List<AttachmentDTO> attachmentInfoList = JSONObject.parseArray(recordDO.getAttachment(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
                if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.P.getCode())
                        && Objects.equals(AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(), abnormalAttendanceDO.getAbnormalType())
                        && StringUtils.isBlank(recordDO.getReason())) {
                    List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.getAttendanceEmployeeDetailDO(abnormalAttendanceDO.getUserId(), abnormalAttendanceDO.getDayId());
                    if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
                        continue;
                    }
                    AttendanceEmployeeDetailDO attendanceEmployeeDetailDO = attendanceEmployeeDetailDOList
                            .stream()
                            .filter(item -> Objects.equals(item.getConcreteType(), AbnormalOperationTypeEnum.P.getCode()))
                            .findFirst()
                            .orElse(new AttendanceEmployeeDetailDO());
                    String operationContent;
                    if (RequestInfoHolder.isChinese()) {
                        operationContent = String.format(BusinessConstant.CONFIRM_P_OPERATION_CONTENT_CN, attendanceEmployeeDetailDO.getLegalWorkingHours(), attendanceEmployeeDetailDO.getAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                    } else {
                        operationContent = String.format(BusinessConstant.CONFIRM_P_OPERATION_CONTENT_EN, attendanceEmployeeDetailDO.getLegalWorkingHours(), attendanceEmployeeDetailDO.getAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                    }
                    dto.setOperationContent(operationContent);
                }
                continue;
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode())) {
                dto.setOperationStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
                abnormalOperationRecordDTOList.add(dto);
                continue;
            }
            List<AttendanceFormDO> applicationFormDOList = formMap.get(recordDO.getFormId());
            if (CollectionUtils.isEmpty(applicationFormDOList)) {
                continue;
            }
            AttendanceFormDO attendanceFormDO = applicationFormDOList.get(0);
            List<AttendanceFormAttrDO> applicationFormAttrList = attrMap.get(attendanceFormDO.getId());
            if (CollectionUtils.isEmpty(applicationFormAttrList)) {
                continue;
            }
            dto.setApplicationCode(applicationFormDOList.get(0).getApplicationCode());
            dto.setOperationStatus(applicationFormDOList.get(0).getFormStatus());
            Map<String, AttendanceFormAttrDO> formAttrMap = applicationFormAttrList.stream().collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));

            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.LEAVE.getCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (RequestInfoHolder.isChinese()) {
                    stringBuilder.append("请假时间: ");
                } else {
                    stringBuilder.append("leave date: ");
                }
                AttendanceFormAttrDO leaveStartDate = formAttrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
                if (leaveStartDate != null) {
                    stringBuilder.append(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDate.getAttrValue()));
                }
                AttendanceFormAttrDO leaveEndDate = formAttrMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());
                if (leaveEndDate != null) {
                    stringBuilder.append(" ").append("-").append(" ").append(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDate.getAttrValue()));
                }
                dto.setOperationContent(stringBuilder.toString());

                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }

                if (Objects.equals(FormStatusEnum.PASS.getCode(), attendanceFormDO.getFormStatus())) {
                    WarehouseAbnormalDetailVO.UserDayFormDTO userDayFormDTO = new WarehouseAbnormalDetailVO.UserDayFormDTO();
                    userDayFormDTO.setFormId(attendanceFormDO.getId());
                    userDayFormDTO.setFormType(attendanceFormDO.getFormType());
                    userDayFormDTO.setFormStatus(attendanceFormDO.getFormStatus());
                    if (leaveStartDate != null) {
                        userDayFormDTO.setStartDate(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDate.getAttrValue()));
                    }
                    if (leaveEndDate != null) {
                        userDayFormDTO.setEndDate(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDate.getAttrValue()));
                    }
                    userDayFormDTOList.add(userDayFormDTO);
                }
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.REISSUE_CARD.getCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (RequestInfoHolder.isChinese()) {
                    stringBuilder.append("补卡时间: ");
                } else {
                    stringBuilder.append("reissue card date: ");
                }
                AttendanceFormAttrDO correctPunchTime = formAttrMap.get(ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode());
                if (correctPunchTime != null) {
                    stringBuilder.append(DateHelper.parseYYYYMMDDHHMMSS(correctPunchTime.getAttrValue()));

                }
                dto.setOperationContent(stringBuilder.toString());
                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.ADD_DURATION.getCode())) {
                AttendanceFormAttrDO actualAttendanceTime = formAttrMap.get(ApplicationFormAttrKeyEnum.actualAttendanceTime.getLowerCode());
                AttendanceFormAttrDO newActualAttendanceTime = formAttrMap.get(ApplicationFormAttrKeyEnum.newActualAttendanceTime.getLowerCode());

                AttendanceFormAttrDO actualWorkingHours = formAttrMap.get(ApplicationFormAttrKeyEnum.actualWorkingHours.getLowerCode());
                AttendanceFormAttrDO newActualWorkingHours = formAttrMap.get(ApplicationFormAttrKeyEnum.newActualWorkingHours.getLowerCode());

                String operationContent;
                if (Objects.nonNull(actualAttendanceTime) && Objects.nonNull(actualWorkingHours)) {
                    if (RequestInfoHolder.isChinese()) {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_WORKING_CONTENT_CN, actualWorkingHours.getAttrValue(), newActualWorkingHours.getAttrValue()) + "; " +
                                String.format(BusinessConstant.ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_CN, actualAttendanceTime.getAttrValue(), newActualAttendanceTime.getAttrValue());
                    } else {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_WORKING_CONTENT_EN, actualWorkingHours.getAttrValue(), newActualWorkingHours.getAttrValue()) + "; " +
                                String.format(BusinessConstant.ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_EN, actualAttendanceTime.getAttrValue(), newActualAttendanceTime.getAttrValue());
                    }
                } else if (Objects.nonNull(actualAttendanceTime)) {
                    if (RequestInfoHolder.isChinese()) {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_CN, actualAttendanceTime.getAttrValue(), newActualAttendanceTime.getAttrValue());
                    } else {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_EN, actualAttendanceTime.getAttrValue(), newActualAttendanceTime.getAttrValue());
                    }
                } else {
                    if (RequestInfoHolder.isChinese()) {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_WORKING_CONTENT_CN, actualWorkingHours.getAttrValue(), newActualWorkingHours.getAttrValue());
                    } else {
                        operationContent = String.format(BusinessConstant.ADD_DURATION_ACTUAL_WORKING_CONTENT_EN, actualWorkingHours.getAttrValue(), newActualWorkingHours.getAttrValue());
                    }
                }

                dto.setOperationContent(operationContent);

                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
            }
            abnormalOperationRecordDTOList.add(dto);
        }
    }

    private void buildAttendanceAbnormalInfo(EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                             List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalList,
                                             WarehouseAbnormalDetailVO result) {
        WarehouseAbnormalDetailVO.AttendanceAbnormalInfo abnormalInfo = new WarehouseAbnormalDetailVO.AttendanceAbnormalInfo();
        result.setAttendanceAbnormalInfo(abnormalInfo);
        abnormalInfo.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalInfo.setAttendanceDate(abnormalAttendanceDO.getDate());
        abnormalInfo.setAbnormalType(abnormalAttendanceDO.getAbnormalType());
        abnormalInfo.setAttendanceStatus(abnormalAttendanceDO.getStatus());
        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
        if (abnormalTypeEnum != null) {
            abnormalInfo.setAbnormalTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getCode());
        }
        if (Objects.nonNull(abnormalAttendanceDO.getPunchClassItemConfigId())) {
            PunchClassItemConfigDO punchClassItemConfigDO = punchClassItemConfigDao.selectById(abnormalAttendanceDO.getPunchClassItemConfigId());
            abnormalInfo.setItemConfigInfo(PunchClassConfigApiMapstruct.INSTANCE.toItemVO(punchClassItemConfigDO));
            abnormalInfo.setAttendanceHours(punchClassItemConfigDO.getAttendanceHours());
            abnormalInfo.setLegalWorkingHours(punchClassItemConfigDO.getLegalWorkingHours());
        }
        if (StringUtils.isNotEmpty(abnormalAttendanceDO.getExtend())) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
            if (abnormalExtendDTO.getActualPunchTime() != null) {
                abnormalInfo.setPunchTimeList(Collections.singletonList(abnormalExtendDTO.getActualPunchTime()));
            }
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(), abnormalAttendanceDO.getAbnormalType())
                && CollectionUtils.isEmpty(result.getWarehouseRecordList())) {
            abnormalInfo.setPunchTimeList(result.getWarehouseRecordList().stream().map(WarehouseAbnormalDetailVO.WarehouseRecordDTO::getWarehouseTime).collect(Collectors.toList()));
        }
        //时长异常
        if (Objects.equals(AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode(), abnormalAttendanceDO.getAbnormalType()) && CollectionUtils.isNotEmpty(warehouseDetailAbnormalList)) {
            WarehouseDetailAbnormalDTO warehouseDetailAbnormalDTO = warehouseDetailAbnormalList.get(0);
            abnormalInfo.setActualAttendanceTime(warehouseDetailAbnormalDTO.getActualAttendanceTime());
            abnormalInfo.setActualWorkingHours(warehouseDetailAbnormalDTO.getActualWorkingHours());
        }

        if (AbnormalAttendanceStatusEnum.TYPE_OF_UN_PROCESSED_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
            return;
        }

        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDao.selectByAbnormalList(Collections.singletonList(abnormalAttendanceDO.getId()));
        if (CollectionUtils.isEmpty(abnormalOperationRecordList)) {
            return;
        }
        EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = abnormalOperationRecordList.get(0);
        abnormalInfo.setOperationType(abnormalOperationRecordDO.getOperationType());
        if (Objects.isNull(abnormalOperationRecordDO.getFormId())) {
            return;
        }

        List<AttendanceFormDO> attendanceFormDOList = attendanceFormDao.selectByIds(Collections.singletonList(abnormalOperationRecordDO.getFormId()));
        if (CollectionUtils.isEmpty(attendanceFormDOList)) {
            return;
        }
        abnormalInfo.setApprovalId(attendanceFormDOList.get(0).getApprovalId());
    }

    /**
     * 查询异常数量
     */
    public AbnormalAttendanceCountDTO selectCount(EmployeeAbnormalAttendanceQueryDTO queryDTO) {
        AbnormalAttendanceCountDTO countDTO = new AbnormalAttendanceCountDTO();
        EmployeeAbnormalAttendancePageQuery query = BeanUtils.convert(queryDTO, EmployeeAbnormalAttendancePageQuery.class);
        PaginationResult<EmployeeAbnormalAttendanceDTO> pageQuery = buildAuthQuery(query);
        if (pageQuery != null) {
            return countDTO;
        }
        query.setLocationCountry(queryDTO.getCountry());
        query.setStatusList(Arrays.asList(AbnormalAttendanceStatusEnum.IN_REVIEW.getCode(), AbnormalAttendanceStatusEnum.REJECT.getCode(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode()));
        query.setStaffTypes(Collections.singletonList("warehouse"));

        query.setEmployeeTypeList(EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        Integer warehouseCount = employeeAbnormalAttendanceDao.selectCount(query);
        countDTO.setWarehouseCount(warehouseCount);

        query.setStatusList(Collections.singletonList(AbnormalAttendanceStatusEnum.PASS.getCode()));
        Integer processWarehouseCount = employeeAbnormalAttendanceDao.selectCount(query);
        countDTO.setProcessWarehouseCount(processWarehouseCount);

        query.setStatusList(Collections.singletonList(AbnormalAttendanceStatusEnum.EXPIRED.getCode()));
        Integer expiredWarehouseCount = employeeAbnormalAttendanceDao.selectCount(query);
        countDTO.setExpiredWarehouseCount(expiredWarehouseCount);

        return countDTO;
    }


    /**
     * 单个处理异常
     */
    public void singleUpdateAbnormalAttendance(AbnormalAttendanceSingleUpdateParam param) {
        if (StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode())) {
            singleAbnormalConfirmHandler(param);
            return;
        }
        if (StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.OFF.getCode())
                || StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.PH.getCode())) {
            singleAbnormalOffHandler(param);
            return;
        }
        if (StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.P.getCode())) {
            singleAbnormalPresentHandler(param);
        }
    }

    /**
     * 批量处理异常
     */
    public void batchUpdateAbnormalAttendance(AbnormalAttendanceBatchUpdateParam param) {
        if (StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode())) {
            batchAbnormalConfirmHandler(param);
            return;
        }
        if (StringUtils.equalsIgnoreCase(param.getUpdateType(), AbnormalOperationTypeEnum.P.getCode())) {
            batchAbnormalPresentHandler(param);
        }
    }

    /**
     * 未排班异常-获取用户当天可输入的应出勤时间最大值
     */
    public DayAttendanceTimeVO getDayAttendanceHours(DayAttendanceTimeParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        BigDecimal dayUsedAttendanceMinutes = BigDecimal.ZERO;
        //当天存在的考勤数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.selectAttendanceByDayIdList(abnormalAttendanceDO.getUserId(), Collections.singletonList(abnormalAttendanceDO.getDayId()));
        for (AttendanceEmployeeDetailDO detailDO : attendanceEmployeeDetailDOList) {
            if (detailDO.getFormId() == null) {
                continue;
            }
            if (detailDO.getAttendanceMinutes() != null) {
                dayUsedAttendanceMinutes = dayUsedAttendanceMinutes.add(detailDO.getAttendanceMinutes());
            }
            if (detailDO.getLeaveMinutes() != null) {
                dayUsedAttendanceMinutes = dayUsedAttendanceMinutes.add(detailDO.getLeaveMinutes());
            }
        }
        DayAttendanceTimeVO dayAttendanceTimeVO = new DayAttendanceTimeVO();
        dayAttendanceTimeVO.setDayUsedAttendanceMinutes(dayUsedAttendanceMinutes);
        return dayAttendanceTimeVO;
    }

    /**
     * 考勤异常员工-发送企业微信提醒
     */
    public void sendAbnormalAttendanceReminder(List<AbnormalAttendanceReminderParam> param) {
        if (CollUtil.isEmpty(param)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        // 1. 只有待处理的考勤异常数据才发送企业微信通知
        List<AbnormalAttendanceReminderParam> targetParam = param.stream().
                filter(e -> ObjectUtil.equal(AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode(), e.getStatus())).
                collect(Collectors.toList());
        // 根据国家过滤
        // 2. 将目标数据按照country,userCode,dayId分组
        Map<String, AbnormalAttendanceReminderParam> mergeMap = targetParam.stream().collect(Collectors.toMap(
                item -> item.getCountry() + item.getUserCode() + item.getDayId()
                , item -> item
                , (existing, replacement) -> existing)
        );
        List<AbnormalAttendanceReminderParam> sendParamList = new ArrayList<>(mergeMap.values());
        if (CollectionUtils.isEmpty(sendParamList)) {
            return;
        }
        for (AbnormalAttendanceReminderParam reminderParam : sendParamList) {
            AbnormalAttendanceDayReminderParam userReminderParam = AbnormalAttendanceDayReminderParam.builder()
                    .countryList(reminderParam.getCountry())
                    .userCodeList(reminderParam.getUserCode())
                    .dayId(reminderParam.getDayId())
                    .sendType(BusinessConstant.Y)
                    .build();
            // 发送异常考勤提醒
            attendanceAbnormalRemindService.sendRemind(userReminderParam);
        }
    }


    /**
     * 根据主键查询 (原有manage方法)
     *
     * @param idList
     * @return
     */
    public List<EmployeeAbnormalAttendanceDO> selectByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return employeeAbnormalAttendanceDao.listByIds(idList);
    }

    /**
     * 查询用户在某几天的所有的异常
     *
     * @param userIdList
     * @param dayIdList
     * @return
     */
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdList(List<Long> userIdList,
                                                                         List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList)
                || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        return employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList);
    }

    /**
     * 异常过滤
     */
    public List<EmployeeAbnormalAttendanceDO> filterAbnormalAttendance(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceList,
                                                                       List<PunchClassItemConfigDO> punchClassItemConfigDOList,
                                                                       Date dateTime) {
        Map<Long, PunchClassItemConfigDO> itemConfigMap = punchClassItemConfigDOList.stream()
                .collect(Collectors.toMap(PunchClassItemConfigDO::getId, Function.identity(), (oldVal, newVal) -> oldVal));
        if (CollectionUtils.isEmpty(abnormalAttendanceList)) {
            return Collections.emptyList();
        }
        Iterator<EmployeeAbnormalAttendanceDO> iterator = abnormalAttendanceList.iterator();
        while (iterator.hasNext()) {
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = iterator.next();
            if (!AttendanceAbnormalTypeEnum.getLackCodeList().contains(abnormalAttendanceDO.getAbnormalType())) {
                continue;
            }
            Long punchClassItemConfigId = abnormalAttendanceDO.getPunchClassItemConfigId();
            if (Objects.isNull(punchClassItemConfigId) || punchClassItemConfigId <= 0) {
                continue;
            }
            PunchClassItemConfigDO itemConfigDO = itemConfigMap.get(punchClassItemConfigId);
            if (Objects.isNull(itemConfigDO)) {
                continue;
            }
            // 判断当前时间是否大于最晚下班打卡时间+3小时，大于即展示下班缺卡+早退异常
            BigDecimal elasticTime = itemConfigDO.getElasticTime();
            if (Objects.isNull(elasticTime)) {
                elasticTime = BigDecimal.valueOf(0);
            }
            if (Objects.isNull(itemConfigDO.getLatestPunchOutTime())) {
                continue;
            }
            DateTime abnormalPunchOutTime = DateUtil.offsetMinute(itemConfigDO.getLatestPunchOutTime(),
                    BigDecimal.valueOf(3).multiply(new BigDecimal(60)).intValue());
            if (dateTime.before(abnormalPunchOutTime) &&
                    AttendanceAbnormalTypeEnum.getOutLackCodeList().contains(abnormalAttendanceDO.getAbnormalType())) {
                iterator.remove();
                continue;
            }
            if (Objects.isNull(itemConfigDO.getPunchInTime())) {
                continue;
            }
            // 判断当前时间是否大于上班时间+弹性时间，大于即展示上班缺卡异常
            DateTime abnormalPunchInTime = DateUtil.offsetMinute(itemConfigDO.getPunchInTime(), elasticTime.multiply(new BigDecimal(60)).intValue());
            if (dateTime.before(abnormalPunchInTime) &&
                    AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                iterator.remove();
            }
        }
        return abnormalAttendanceList;
    }


    /**
     * 单个确认异常
     */
    private void singleAbnormalConfirmHandler(AbnormalAttendanceSingleUpdateParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
        //添加对异常的操作记录
        EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        abnormalOperationRecordDO.setId(defaultIdWorker.nextId());
        abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode());
        BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);
        abnormalAttendanceManage.abnormalConfirmSave(Collections.singletonList(abnormalAttendanceDO), Collections.singletonList(abnormalOperationRecordDO));
        //仓内考勤确认异常逻辑处理
        warehouseAttendanceHandlerService.warehouseConfirmExceptionHandler(param.getAbnormalId(), userInfoDO.getId(), abnormalAttendanceDO.getDayId());
        // 更新日报
        this.sendDayReportJob(userInfoDO, abnormalAttendanceDO.getDayId());
    }


    /**
     * 单个异常确认为周末或法假
     */
    private void singleAbnormalOffHandler(AbnormalAttendanceSingleUpdateParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        CalendarConfigDateQuery configDateQuery = new CalendarConfigDateQuery();
        configDateQuery.setUserIds(Collections.singletonList(userInfoDO.getId()));
        configDateQuery.setEndDate(DateUtil.endOfDay(abnormalAttendanceDO.getDate()));
        List<CalendarConfigRangeDO> attendanceConfigRangeDOList = calendarConfigRangeDao.selectCalendarConfigByDate(configDateQuery);
        if (CollectionUtils.isEmpty(attendanceConfigRangeDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_CALENDAR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_CALENDAR.getDesc()));
        }

        List<CalendarConfigDetailDO> attendanceConfigDetailDOList = calendarManage.selectCalendarDetailsByConfigIds(Collections.singletonList(attendanceConfigRangeDOList.get(0).getAttendanceConfigId()))
                .stream()
                .filter(item -> item.getDayId().equals(abnormalAttendanceDO.getDayId()))
                .collect(Collectors.toList());

        String dayType = CollectionUtils.isNotEmpty(attendanceConfigDetailDOList) ? attendanceConfigDetailDOList.get(0).getDayType() : null;

        abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);

        //添加对异常的操作记录
        EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
        abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalOperationRecordDO.setOperationType(param.getUpdateType());
        BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);

        //正常考勤表落入数据
        AttendanceEmployeeDetailDO employeeDetailDO = buildAttendanceEmployeeDetailDO(abnormalAttendanceDO.getDayId(), abnormalAttendanceDO.getDate(), AttendanceDataSourceEnum.ABNORMAL_SINGLE_HANDLER.getCode(),
                param.getUpdateType(), dayType, userInfoDO, BigDecimal.ZERO, BigDecimal.ZERO);

        // 这里需要增加落仓内考勤统计的数据
        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = null;
        WarehouseDetailDO warehouseDetailDO = null;
        if (warehouseUserService.isWarehouseSupportUser(userInfoDO)) {
            warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());
                warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.NORMAL.getCode());
                warehouseDetailDO.setLegalWorkingHours(BigDecimal.ZERO);
                warehouseDetailDO.setRequiredAttendanceTime(BigDecimal.ZERO);
                warehouseDetailDO.setActualAttendanceTime(BigDecimal.ZERO);
                warehouseDetailDO.setActualWorkingHours(BigDecimal.ZERO);
                warehouseDetailDO.setAbsenceTime(BigDecimal.ZERO);
                warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                BaseDOUtil.fillDOUpdate(warehouseDetailDO);
                //设置异常处理结果
                warehouseDetailAbnormalDO.setProcessed(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(warehouseDetailAbnormalDO);
                //计算仓内实际入离仓出勤时长
                attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, true);
            }
        }

        abnormalAttendanceManage.abnormalOffSave(employeeDetailDO, abnormalAttendanceDO, abnormalOperationRecordDO, null, warehouseDetailAbnormalDO, warehouseDetailDO);
        // 更新日报
        this.sendDayReportJob(userInfoDO, abnormalAttendanceDO.getDayId());
    }

    /**
     * 单个异常确认为工作日
     */
    private void singleAbnormalPresentHandler(AbnormalAttendanceSingleUpdateParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(Collections.singletonList(param.getAbnormalId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        AttendanceUser userInfoDO = userService.getByUserId(abnormalAttendanceDO.getUserId());
        if (userInfoDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        CalendarConfigDateQuery configDateQuery = new CalendarConfigDateQuery();
        configDateQuery.setUserIds(Collections.singletonList(userInfoDO.getId()));
        configDateQuery.setEndDate(DateUtil.endOfDay(abnormalAttendanceDO.getDate()));
        List<CalendarConfigRangeDO> attendanceConfigRangeDOList = calendarConfigRangeDao.selectCalendarConfigByDate(configDateQuery);
        if (CollectionUtils.isEmpty(attendanceConfigRangeDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_CALENDAR.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_CALENDAR.getDesc()));
        }

        List<CalendarConfigDetailDO> attendanceConfigDetailDOList = calendarManage.selectCalendarDetailsByConfigIds(Collections.singletonList(attendanceConfigRangeDOList.get(0).getAttendanceConfigId()))
                .stream()
                .filter(item -> item.getDayId().equals(abnormalAttendanceDO.getDayId()))
                .collect(Collectors.toList());
        String dayType = CollectionUtils.isNotEmpty(attendanceConfigDetailDOList) ? attendanceConfigDetailDOList.get(0).getDayType() : null;

        abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
        //添加对异常的操作记录
        EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
        abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
        abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalOperationRecordDO.setOperationType(param.getUpdateType());
        BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);

        List<AttendanceEmployeeDetailDO> existPresentEmployeeDetailDOList = attendanceEmployeeDetailDao.selectAttendanceByDayIdList(userInfoDO.getId(), Collections.singletonList(abnormalAttendanceDO.getDayId()))
                .stream()
                .filter(item -> item.getFormId() == null).collect(Collectors.toList());

        //正常考勤表落入数据
        AttendanceEmployeeDetailDO employeeDetailDO = buildAttendanceEmployeeDetailDO(abnormalAttendanceDO.getDayId(), abnormalAttendanceDO.getDate(), AttendanceDataSourceEnum.ABNORMAL_SINGLE_HANDLER.getCode(),
                param.getUpdateType(), dayType, userInfoDO, param.getAttendanceHours().multiply(BusinessConstant.MINUTES), param.getLegalWorkingHours());
        WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = null;
        WarehouseDetailDO warehouseDetailDO = null;
        if (warehouseUserService.isWarehouseSupportUser(userInfoDO)) {
            warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
            if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());
                warehouseDetailDO.setAttendanceStatus(WarehouseAttendanceStatusEnum.NORMAL.getCode());
                warehouseDetailDO.setLegalWorkingHours(param.getLegalWorkingHours());
                warehouseDetailDO.setRequiredAttendanceTime(param.getLegalWorkingHours());
                warehouseDetailDO.setActualAttendanceTime(param.getAttendanceHours());
                warehouseDetailDO.setActualWorkingHours(param.getLegalWorkingHours());
                warehouseDetailDO.setAbsenceTime(param.getLegalWorkingHours().subtract(param.getAttendanceHours()));
                warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                BaseDOUtil.fillDOUpdate(warehouseDetailDO);
                //设置异常逻辑删除
                warehouseDetailAbnormalDO.setProcessed(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(warehouseDetailAbnormalDO);
                attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, true);
            }
        }

        abnormalAttendanceManage.abnormalOffSave(employeeDetailDO, abnormalAttendanceDO, abnormalOperationRecordDO, existPresentEmployeeDetailDOList, warehouseDetailAbnormalDO, warehouseDetailDO);
        // 更新日报
        this.sendDayReportJob(userInfoDO, abnormalAttendanceDO.getDayId());
    }


    private void batchAbnormalConfirmHandler(AbnormalAttendanceBatchUpdateParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(param.getAbnormalIdList());
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = new ArrayList<>();
        abnormalAttendanceDOList.forEach(item -> {
            item.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);

            //添加对异常的操作记录
            EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
            abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            abnormalOperationRecordDO.setAbnormalId(item.getId());
            abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode());
            BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);
            abnormalOperationRecordDOList.add(abnormalOperationRecordDO);
        });
        abnormalAttendanceManage.abnormalConfirmSave(abnormalAttendanceDOList, abnormalOperationRecordDOList);
        //仓内考勤确认异常处理
        abnormalAttendanceDOList.forEach(abnormal -> warehouseAttendanceHandlerService.warehouseConfirmExceptionHandler(abnormal.getId(), abnormal.getUserId(), abnormal.getDayId()));
        // 生成日报
        this.batchSendDayReportJob(abnormalAttendanceDOList);
    }

    private void batchAbnormalPresentHandler(AbnormalAttendanceBatchUpdateParam param) {
        log.info("batch handler P param:{}", JSON.toJSONString(param));
        if (ObjectUtil.isNull(param) || ObjectUtil.isEmpty(param.getReason())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.BATCH_HANDLER_P_ERROR);
        }
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(param.getAbnormalIdList());
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        List<Long> dayIdList = abnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getDayId).sorted().collect(Collectors.toList());
        Long minDayId = dayIdList.get(0);
        Long maxDayId = dayIdList.get(dayIdList.size() - 1);
        List<Long> userIdList = abnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getUserId).collect(Collectors.toList());
        List<AttendanceUser> userInfoDOList = userService.listUsersByIds(userIdList);

        //查询用户在这些天的所有异常（需要一起删除，用户一天可能存在多个异常，这里哪怕删除了一个，另一个也要自动删除）
        //有多余的
        List<EmployeeAbnormalAttendanceDO> allAbnormalList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, dayIdList)
                .stream()
                .filter(item -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(item.getStatus()))
                .collect(Collectors.toList());
        //查询用户当天存在的历史考勤,有多余的
        List<AttendanceEmployeeDetailDO> employeeDetailDOList = attendanceEmployeeDetailDao.selectAttendanceByCycleDay(userIdList, minDayId, maxDayId);

        //查询这样用户的日历和明细
        CalendarConfigDateQuery configDateQuery = new CalendarConfigDateQuery();
        configDateQuery.setUserIds(userIdList);
        List<CalendarConfigRangeDO> attendanceConfigRangeDOList = calendarConfigRangeDao.selectCalendarConfigByDate(configDateQuery);

        List<Long> attendanceConfigIdList = attendanceConfigRangeDOList.stream().map(CalendarConfigRangeDO::getAttendanceConfigId).collect(Collectors.toList());
        Map<Long, List<CalendarConfigDetailDO>> attendanceConfigDetailMap = calendarManage.selectCalendarDetailsByConfigIds(attendanceConfigIdList)
                .stream().collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));

        //查询用户当天的排班，需要班次的法定工作时长
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectRecordByUserIdList(userIdList, minDayId, maxDayId);
        List<Long> classIdList = userShiftConfigDOList.stream().map(UserShiftConfigDO::getPunchClassConfigId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(classIdList);
        Map<Long, List<PunchClassConfigDO>> classConfigMap = punchClassConfigDOList.stream().collect(Collectors.groupingBy(PunchClassConfigDO::getId));

        //查询这些用户所有审批中的单据(请假/外勤/补卡)
        ApplicationFormQuery formQuery = new ApplicationFormQuery();
        formQuery.setUserIdList(userIdList);
        formQuery.setFromTypeList(Arrays.asList(FormTypeEnum.LEAVE.getCode(), FormTypeEnum.LEAVE_REVOKE.getCode(),
                FormTypeEnum.OUT_OF_OFFICE.getCode(), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode(),
                FormTypeEnum.REISSUE_CARD.getCode(), FormTypeEnum.REISSUE_CARD_REVOKE.getCode()));
        formQuery.setStatusList(Arrays.asList(FormStatusEnum.IN_REVIEW.getCode(), FormStatusEnum.REJECT.getCode()));

        List<AttendanceFormDO> applicationFormDOList = attendanceFormDao.selectForm(formQuery);
        List<Long> formIdList = applicationFormDOList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        List<AttendanceFormRelationDO> relationDOList = attendanceFormRelationDao.selectRelationByFormIdList(formIdList).stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).collect(Collectors.toList());

        List<EmployeeAbnormalAttendanceDO> updateAbnormalList = new ArrayList<>();
        List<AttendanceFormDO> updateFormList = new ArrayList<>();
        List<AttendanceEmployeeDetailDO> addEmployeeDetailList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> addOperationList = new ArrayList<>();
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailList = new ArrayList<>();
        List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS = new ArrayList<>();
        List<WarehouseDetailDO> warehouseDetailDOS = new ArrayList<>();
        for (AttendanceUser userInfoDO : userInfoDOList) {
            List<EmployeeAbnormalAttendanceDO> userAbnormalList = abnormalAttendanceDOList.stream().filter(item -> item.getUserId().equals(userInfoDO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userAbnormalList)) {
                continue;
            }
            List<Long> userDayIdList = userAbnormalList.stream().map(EmployeeAbnormalAttendanceDO::getDayId).distinct().collect(Collectors.toList());
            // 获取该人员的页面选中的所有的异常数据
            List<EmployeeAbnormalAttendanceDO> userAllAbnormalList = allAbnormalList.stream()
                    .filter(item -> item.getUserId().equals(userInfoDO.getId())
                            && userDayIdList.contains(item.getDayId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userAllAbnormalList)) {
                continue;
            }
            //以每个用户每天为纬度进行处理
            Map<Long, List<EmployeeAbnormalAttendanceDO>> userDayAbnormalMap = userAllAbnormalList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getDayId));
            for (Map.Entry<Long, List<EmployeeAbnormalAttendanceDO>> entry : userDayAbnormalMap.entrySet()) {
                // 获取每一天的异常数据集合
                List<EmployeeAbnormalAttendanceDO> userDayAbnormalList = entry.getValue();
                List<Long> userDayAbnormalIdList = userDayAbnormalList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                Date endDayDate = DateHelper.endOfDay(DateHelper.transferDayIdToDate(entry.getKey()));

                //获取用户当天的日历明细
                List<CalendarConfigRangeDO> userDayAttendanceConfigList = attendanceConfigRangeDOList.stream()
                        .filter(item -> item.getBizId().equals(userInfoDO.getId())
                                && item.getStartDate().compareTo(endDayDate) < 1
                                && item.getEndDate().compareTo(endDayDate) > -1)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userDayAttendanceConfigList)) {
                    continue;
                }
                //可以为空，表示工作日
                List<CalendarConfigDetailDO> userDayAttendanceConfigDetailList = attendanceConfigDetailMap.get(userDayAttendanceConfigList.get(0).getAttendanceConfigId());
                if (CollectionUtils.isEmpty(userDayAttendanceConfigDetailList)) {
                    continue;
                }
                //为空，就是工作日（这边获取该用户该天的日历情况）
                List<CalendarConfigDetailDO> userDayType = userDayAttendanceConfigDetailList.stream().filter(item -> item.getDayId().equals(entry.getKey())).collect(Collectors.toList());
                String dayType = CollectionUtils.isNotEmpty(userDayType) ? userDayType.get(0).getDayType() : null;


                //获取用户当天的排班,可以为空，表示为排班
                List<UserShiftConfigDO> userDayEmployeeConfigList = userShiftConfigDOList.stream()
                        .filter(item -> item.getUserId().equals(userInfoDO.getId())
                                && item.getDayId().equals(entry.getKey()))
                        .collect(Collectors.toList());
                //法定工作时长
                BigDecimal legalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
                //出勤时长
                BigDecimal attendanceHours = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(userDayEmployeeConfigList)) {
                    List<PunchClassConfigDO> userDayClassList = classConfigMap.get(userDayEmployeeConfigList.get(0).getPunchClassConfigId());
                    if (CollectionUtils.isNotEmpty(userDayClassList)) {
                        legalWorkingHours = userDayClassList.get(0).getLegalWorkingHours();
                        attendanceHours = userDayClassList.get(0).getAttendanceHours();
                    }
                }

                //获取当天的历史正常考勤数据
                List<AttendanceEmployeeDetailDO> userEmployeeDetailList = employeeDetailDOList.stream()
                        .filter(item -> item.getUserId().equals(userInfoDO.getId()) && item.getDayId().equals(entry.getKey()))
                        .collect(Collectors.toList());
                //当天已经使用的分钟
                BigDecimal dayUsedAttendanceMinutes = BigDecimal.ZERO;
                for (AttendanceEmployeeDetailDO employeeDetailDO : userEmployeeDetailList) {
                    if (employeeDetailDO.getFormId() == null) {
                        employeeDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                        BaseDOUtil.fillDOUpdateByUserOrSystem(employeeDetailDO);
                        updateEmployeeDetailList.add(employeeDetailDO);
                    }
                    if (employeeDetailDO.getAttendanceMinutes() != null) {
                        dayUsedAttendanceMinutes = dayUsedAttendanceMinutes.add(employeeDetailDO.getAttendanceMinutes());
                    }
                    if (employeeDetailDO.getLeaveMinutes() != null) {
                        dayUsedAttendanceMinutes = dayUsedAttendanceMinutes.add(employeeDetailDO.getLeaveMinutes());
                    }
                }

                //看看用户有没有申请中的单据关联当天所有的异常
                List<Long> cancelFormIdList = relationDOList.stream()
                        .filter(item -> userDayAbnormalIdList.contains(item.getRelationId())).map(AttendanceFormRelationDO::getFormId)
                        .collect(Collectors.toList());
                List<AttendanceFormDO> cancelFormList = applicationFormDOList.stream().filter(item -> cancelFormIdList.contains(item.getId())).collect(Collectors.toList());
                cancelFormList.forEach(item -> {
                    item.setFormStatus(FormStatusEnum.CANCEL.getCode());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(item);
                });
                updateFormList.addAll(cancelFormList);

                for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : userDayAbnormalList) {
                    abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
                    updateAbnormalList.add(abnormalAttendanceDO);

                    //添加对异常的操作记录
                    EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
                    abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
                    abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
                    abnormalOperationRecordDO.setOperationType(param.getUpdateType());
                    if (ObjectUtil.isNotEmpty(param.getReason())) {
                        abnormalOperationRecordDO.setReason(param.getReason());
                    }
                    if (ObjectUtil.isNotEmpty(param.getAttachmentList())) {
                        abnormalOperationRecordDO.setAttachment(JSON.toJSONString(param.getAttachmentList()));
                    }
                    BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);
                    addOperationList.add(abnormalOperationRecordDO);
                }

                //正常考勤表落入数据
                AttendanceEmployeeDetailDO employeeDetailDO = buildAttendanceEmployeeDetailDO(entry.getKey(), endDayDate, AttendanceDataSourceEnum.ABNORMAL_BATCH_HANDLER.getCode(),
                        param.getUpdateType(), dayType, userInfoDO, legalWorkingHours.multiply(BusinessConstant.MINUTES).subtract(dayUsedAttendanceMinutes), legalWorkingHours);
                addEmployeeDetailList.add(employeeDetailDO);

                //仓内考勤统计数据处理
                warehouseAttendanceBatchConfirmNormal(warehouseDetailAbnormalDOS, warehouseDetailDOS, userInfoDO, entry, legalWorkingHours, attendanceHours);
            }
        }

        abnormalAttendanceManage.batchAbnormalPresentUpdate(updateAbnormalList, updateFormList, addEmployeeDetailList, addOperationList, updateEmployeeDetailList);
        //调用BPM接口，进行取消
        for (AttendanceFormDO formDO : updateFormList) {
            try {
                rpcBpmApprovalClient.backApply(formDO.getApprovalId());
            } catch (Exception e) {
                e.getStackTrace();
                log.info("批量出勤处理调用取消中的单据出错，单据号:{},异常信息为:{}", formDO.getApplicationCode(), e.getMessage());
            }
        }
        // 生成日报
        this.batchSendDayReportJob(abnormalAttendanceDOList);
    }

    private void warehouseAttendanceBatchConfirmNormal(List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOS,
                                                       List<WarehouseDetailDO> warehouseDetailDOS,
                                                       AttendanceUser userInfoDO,
                                                       Map.Entry<Long, List<EmployeeAbnormalAttendanceDO>> entry,
                                                       BigDecimal legalWorkingHours,
                                                       BigDecimal attendanceHours) {
        if (warehouseUserService.isWarehouseSupportUser(userInfoDO)) {
            List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = entry.getValue();
            List<Long> userDayAbnormalIdList = employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
            List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectByAbnormalIdList(userDayAbnormalIdList);
            if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
                return;
            }
            Map<Long, List<WarehouseDetailAbnormalDO>> warehouseDetailAbnormalMap = warehouseDetailAbnormalList.stream().collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));
            Set<Long> warehouseDetailIds = warehouseDetailAbnormalMap.keySet();
            List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByIds(warehouseDetailIds);

            Map<Long, List<WarehouseDetailAbnormalDO>> allWarehouseAbnormalMap = warehouseDetailAbnormalDao.selectByWarehouseDetailIds(new ArrayList<>(warehouseDetailIds))
                    .stream().collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getWarehouseDetailId));

            warehouseDetailDOList.forEach(warehouseDetailDO -> {

                List<WarehouseDetailAbnormalDO> warehouseDetailAbnormalDOList = warehouseDetailAbnormalMap.get(warehouseDetailDO.getId());
                warehouseDetailAbnormalDOList.forEach(abnormal -> abnormal.setProcessed(BusinessConstant.Y));
                warehouseDetailAbnormalDOS.addAll(warehouseDetailAbnormalDOList);

                boolean isAbnormal = judgeAbnormalStatus(allWarehouseAbnormalMap, warehouseDetailAbnormalDOList, warehouseDetailDO.getId());

                if (attendanceHours.compareTo(BigDecimal.ZERO) > 0) {
                    warehouseDetailDO.setActualAttendanceTime(attendanceHours);
                } else {
                    warehouseDetailDO.setActualAttendanceTime(legalWorkingHours);
                }
                warehouseDetailDO.setLegalWorkingHours(legalWorkingHours);
                warehouseDetailDO.setActualWorkingHours(legalWorkingHours);
                warehouseDetailDO.setRequiredAttendanceTime(warehouseDetailDO.getActualAttendanceTime());
                warehouseDetailDO.setAbsenceTime(BigDecimal.ZERO);
                warehouseDetailDO.setOvertimeHours(BigDecimal.ZERO);
                warehouseDetailDO.setAttendanceStatus(isAbnormal ? WarehouseAttendanceStatusEnum.ABNORMAL.getCode() : WarehouseAttendanceStatusEnum.NORMAL.getCode());
                if (!isAbnormal) {
                    warehouseDetailDO.setWarehouseStatus(WarehouseStatusEnum.OUT.getCode());
                }
                BaseDOUtil.fillDOUpdate(warehouseDetailDO);
                //计算仓内实际入离仓考勤时长
                attendanceDurationCalculateService.calculateAttendanceHours(warehouseDetailDO, true);
                warehouseDetailDOS.add(warehouseDetailDO);
            });
        }
    }

    private boolean judgeAbnormalStatus(Map<Long, List<WarehouseDetailAbnormalDO>> allWarehouseAbnormalMap,
                                        List<WarehouseDetailAbnormalDO> currentWarehouseAbnormalDOList,
                                        Long warehouseDetailId) {
        List<Long> abnormalIds = currentWarehouseAbnormalDOList.stream().map(WarehouseDetailAbnormalDO::getAbnormalId).collect(Collectors.toList());
        List<WarehouseDetailAbnormalDO> allAbnormalList = allWarehouseAbnormalMap.get(warehouseDetailId);
        List<WarehouseDetailAbnormalDO> filterAbnormalList = allAbnormalList.stream().filter(abnormal -> !abnormalIds.contains(abnormal.getAbnormalId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterAbnormalList)) {
            return false;
        }

        Map<Integer, List<Long>> processedMap = filterAbnormalList.stream()
                .filter(abnormal -> Objects.nonNull(abnormal.getProcessed()))
                .collect(Collectors.groupingBy(WarehouseDetailAbnormalDO::getProcessed, Collectors.mapping(WarehouseDetailAbnormalDO::getAbnormalId, Collectors.toList())));
        if (CollectionUtils.isNotEmpty(processedMap.get(WarehouseAbnormalStatusEnum.PENDING_PROCESSING.getCode()))) {
            return true;
        }
        List<Long> processedAbnormalIdList = processedMap.get(WarehouseAbnormalStatusEnum.PROCESSED.getCode());
        if (CollectionUtils.isEmpty(processedAbnormalIdList)) {
            return false;
        }
        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOS = employeeAbnormalOperationRecordDao.selectByAbnormalList(processedAbnormalIdList);
        Optional<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordOptional = employeeAbnormalOperationRecordDOS.stream()
                .filter(abnormalOperation -> AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode().equals(abnormalOperation.getOperationType())).findFirst();
        return abnormalOperationRecordOptional.isPresent();
    }

    @NotNull
    private AttendanceEmployeeDetailDO buildAttendanceEmployeeDetailDO(Long dayId,
                                                                       Date endDayDate,
                                                                       String dataSource,
                                                                       String concreteType,
                                                                       String dayType,
                                                                       AttendanceUser userInfoDO,
                                                                       BigDecimal attendanceMinutes,
                                                                       BigDecimal legalWorkingHours) {
        AttendanceEmployeeDetailDO employeeDetailDO = new AttendanceEmployeeDetailDO();
        employeeDetailDO.setId(defaultIdWorker.nextId());
        employeeDetailDO.setUserId(userInfoDO.getId());
        employeeDetailDO.setLocationCountry(userInfoDO.getLocationCountry());
        employeeDetailDO.setYear((long) DateUtil.year(endDayDate));
        employeeDetailDO.setMonth((long) (DateUtil.month(endDayDate) + 1));
        employeeDetailDO.setDay(DateUtil.dayOfMonth(endDayDate));
        employeeDetailDO.setDate(endDayDate);
        employeeDetailDO.setDayId(dayId);
        employeeDetailDO.setDataSource(dataSource);
        employeeDetailDO.setAttendanceType(AttendanceDayTypeEnum.PRESENT.name());
        if (StringUtils.isNotBlank(dayType)) {
            employeeDetailDO.setAttendanceType(dayType);
        }
        employeeDetailDO.setConcreteType(concreteType);
        employeeDetailDO.setIsAttendance(BusinessConstant.Y);
        employeeDetailDO.setDeptId(userInfoDO.getDeptId());
        employeeDetailDO.setPostId(userInfoDO.getPostId());
        employeeDetailDO.setLeaveMinutes(BigDecimal.ZERO);
        employeeDetailDO.setOvertimeMinutes(BigDecimal.ZERO);
        employeeDetailDO.setAttendanceMinutes(attendanceMinutes);
        employeeDetailDO.setLegalWorkingHours(legalWorkingHours);
        BaseDOUtil.fillDOInsertByUsrOrSystem(employeeDetailDO);
        return employeeDetailDO;
    }


    private EmployeeBaseInfoVO buildEmployeeBaseInfo(EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                                     AttendanceUser userInfoDO,
                                                     boolean isWarehouse,
                                                     List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalList) {
        EmployeeBaseInfoVO employeeBaseInfo = new EmployeeBaseInfoVO();
        employeeBaseInfo.setUserId(userInfoDO.getId());
        employeeBaseInfo.setUserCode(userInfoDO.getUserCode());
        employeeBaseInfo.setUserName(userInfoDO.getUserName());
        employeeBaseInfo.setEmployeeType(abnormalAttendanceDO.getEmployeeType());
        employeeBaseInfo.setStatus(userInfoDO.getStatus());
        employeeBaseInfo.setWorkStatus(userInfoDO.getWorkStatus());
        employeeBaseInfo.setIsGlobalRelocation(userInfoDO.getIsGlobalRelocation());
        employeeBaseInfo.setLocationCountry(userInfoDO.getLocationCountry());
        employeeBaseInfo.setLocationProvince(userInfoDO.getLocationProvince());
        employeeBaseInfo.setLocationCity(userInfoDO.getLocationCity());
        employeeBaseInfo.setDeptId(userInfoDO.getDeptId());
        employeeBaseInfo.setEmploymentForm(userInfoDO.getEmployeeForm());

        if (Objects.nonNull(userInfoDO.getDeptId())) {
            List<AttendanceDept> deptDOList = attendanceDeptService.listByDeptIds(Collections.singletonList(userInfoDO.getDeptId()));
            if (CollectionUtils.isNotEmpty(deptDOList)) {
                employeeBaseInfo.setDeptName(RequestInfoHolder.isChinese() ? deptDOList.get(0).getDeptNameCn() : deptDOList.get(0).getDeptNameEn());
            }
        }

        if (Objects.nonNull(userInfoDO.getPostId())) {
            List<AttendancePost> attendancePostList = attendancePostService.listByPostList(Collections.singletonList(userInfoDO.getPostId()));
            if (CollectionUtils.isNotEmpty(attendancePostList)) {
                employeeBaseInfo.setPostName(RequestInfoHolder.isChinese() ? attendancePostList.get(0).getPostNameCn() : attendancePostList.get(0).getPostNameEn());
            }
        }

        List<UserEntryRecordDO> userEntryRecordList = userEntryRecordDao.listByUserIds(Collections.singletonList(userInfoDO.getId()))
                .stream()
                .filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userEntryRecordList)) {
            employeeBaseInfo.setEntryDate(userEntryRecordList.get(0).getConfirmDate());
        }

        if (Objects.equals(WorkStatusEnum.DIMISSION.getCode(), userInfoDO.getWorkStatus())) {
            List<UserDimissionRecordDO> userDimissionRecordList = userDimissionRecordDao.listByUserIds(Collections.singletonList(userInfoDO.getId()))
                    .stream()
                    .filter(item -> Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), item.getDimissionStatus()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userDimissionRecordList)) {
                employeeBaseInfo.setDimissionDate(userDimissionRecordList.get(0).getActualDimissionDate());
            }
        }

        if (isWarehouse && CollectionUtils.isNotEmpty(warehouseDetailAbnormalList)) {
            //工作网点/供应商
            WarehouseDetailAbnormalDTO warehouseDetailAbnormalDTO = warehouseDetailAbnormalList.get(0);
            AttendanceDept attendanceDept = deptService.getByDeptId(warehouseDetailAbnormalDTO.getOcId());
            if (Objects.nonNull(attendanceDept)) {
                employeeBaseInfo.setOcId(attendanceDept.getId());
                employeeBaseInfo.setOcName(attendanceDept.getDeptNameEn());
            }
            List<VendorInfoSimpleApiDTO> vendorInfoList = vendorClient.selectVendorList(Collections.singletonList(warehouseDetailAbnormalDTO.getVendorId()));
            if (CollectionUtils.isNotEmpty(vendorInfoList)) {
                employeeBaseInfo.setVendorId(vendorInfoList.get(0).getVendorId());
                employeeBaseInfo.setVendorName(vendorInfoList.get(0).getVendorName());
            }
        }
        return employeeBaseInfo;
    }

    private void buildAttendanceAbnormalInfo(EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                             AbnormalDetailVO result) {
        AbnormalDetailVO.AttendanceAbnormalInfo abnormalInfo = new AbnormalDetailVO.AttendanceAbnormalInfo();
        result.setAttendanceAbnormalInfo(abnormalInfo);
        abnormalInfo.setAbnormalId(abnormalAttendanceDO.getId());
        abnormalInfo.setAttendanceDate(abnormalAttendanceDO.getDate());
        abnormalInfo.setAbnormalType(abnormalAttendanceDO.getAbnormalType());
        AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
        if (abnormalTypeEnum != null) {
            abnormalInfo.setAbnormalTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getCode());
        }

        if (Objects.nonNull(abnormalAttendanceDO.getPunchClassItemConfigId())) {
            PunchClassItemConfigDO punchClassItemConfigDO = punchClassItemConfigDao.selectById(abnormalAttendanceDO.getPunchClassItemConfigId());
            abnormalInfo.setItemConfigInfo(PunchClassConfigApiMapstruct.INSTANCE.toItemVO(punchClassItemConfigDO));
        }

        if (StringUtils.isNotEmpty(abnormalAttendanceDO.getExtend())) {
            AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
            if (abnormalExtendDTO.getActualPunchTime() != null) {
                abnormalInfo.setPunchTimeList(Collections.singletonList(abnormalExtendDTO.getActualPunchTime()));
            }
        }

        if (Objects.equals(AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(), abnormalAttendanceDO.getAbnormalType())) {
            EmployeePunchCardRecordQuery recordQuery = EmployeePunchCardRecordQuery.builder()
                    .dayId(abnormalAttendanceDO.getDayId().toString())
                    .userCode(result.getEmployeeBaseInfo().getUserCode())
                    .build();
            List<EmployeePunchRecordDO> employeePunchRecordDOList = employeePunchRecordDao.listRecords(recordQuery);
            if (CollectionUtils.isNotEmpty(employeePunchRecordDOList)) {
                abnormalInfo.setPunchTimeList(employeePunchRecordDOList.stream().map(EmployeePunchRecordDO::getPunchTime).collect(Collectors.toList()));
            }
        }

        abnormalInfo.setAttendanceStatus(abnormalAttendanceDO.getStatus());
        if (AbnormalAttendanceStatusEnum.TYPE_OF_UN_PROCESSED_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
            return;
        }

        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDao.selectByAbnormalList(Collections.singletonList(abnormalAttendanceDO.getId()));
        if (CollectionUtils.isEmpty(abnormalOperationRecordList)) {
            return;
        }
        EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = abnormalOperationRecordList.get(abnormalOperationRecordList.size() - 1);
        abnormalInfo.setOperationType(abnormalOperationRecordDO.getOperationType());
        if (Objects.isNull(abnormalOperationRecordDO.getFormId())) {
            return;
        }

        List<AttendanceFormDO> attendanceFormDOList = attendanceFormDao.selectByIds(Collections.singletonList(abnormalOperationRecordDO.getFormId()));
        if (CollectionUtils.isEmpty(attendanceFormDOList)) {
            return;
        }
        abnormalInfo.setApprovalId(attendanceFormDOList.get(0).getApprovalId());
    }

    private void buildAttendanceRuleConfigInfo(EmployeeAbnormalAttendanceDO abnormalAttendanceDO, AttendanceUser userInfoDO, AbnormalDetailVO result) {
        AbnormalDetailVO.AttendanceRuleConfigVO ruleConfig = new AbnormalDetailVO.AttendanceRuleConfigVO();
        result.setAttendanceRuleConfigInfo(ruleConfig);
        ruleConfig.setClassNature(userInfoDO.getClassNature());
        //查询排班当天排班信息
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectUserShiftByDayIds(userInfoDO.getId(), Collections.singletonList(abnormalAttendanceDO.getDayId()));
        if (CollectionUtils.isNotEmpty(userShiftConfigDOList)) {
            UserShiftConfigDO userShiftConfigDO = userShiftConfigDOList.get(0);
            ruleConfig.setUserShiftConfigId(userShiftConfigDO.getId());
            ruleConfig.setDayShiftRule(userShiftConfigDO.getDayShiftRule());
            if (Objects.nonNull(userShiftConfigDO.getPunchClassConfigId()) && userShiftConfigDO.getPunchClassConfigId() > 0) {
                PunchClassConfigDO punchClassConfigDO = punchClassConfigDao.selectById(userShiftConfigDO.getPunchClassConfigId());
                if (Objects.nonNull(punchClassConfigDO)) {
                    ruleConfig.setClassId(punchClassConfigDO.getId());
                    ruleConfig.setClassName(punchClassConfigDO.getClassName());
                }
            }
        }

        //日历名称
        List<Long> calendarConfigIdList = calendarConfigRangeDao.selectConfigRange(Collections.singletonList(userInfoDO.getId()))
                .stream().map(CalendarConfigRangeDO::getAttendanceConfigId).collect(Collectors.toList());
        List<CalendarConfigDO> calendarConfigDOList = calendarConfigDao.getByCalendarConfigIds(calendarConfigIdList)
                .stream()
                .filter(calendarConfigDO -> Objects.equals(BusinessConstant.Y, calendarConfigDO.getIsLatest())
                        && Objects.equals(StatusEnum.ACTIVE.getCode(), calendarConfigDO.getStatus()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(calendarConfigDOList)) {
            ruleConfig.setCalendarId(calendarConfigDOList.get(0).getId());
            ruleConfig.setCalendarName(calendarConfigDOList.get(0).getAttendanceConfigName());
        }

        //打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.getConfigMapByUserIdList(Collections.singletonList(userInfoDO.getId()));
        if (MapUtils.isNotEmpty(punchConfigMap)) {
            PunchConfigDO punchConfigDO = punchConfigMap.getOrDefault(userInfoDO.getId(), new PunchConfigDO());
            ruleConfig.setPunchConfigId(punchConfigDO.getId());
            ruleConfig.setPunchConfigName(punchConfigDO.getConfigName());
            ruleConfig.setPunchConfigType(punchConfigDO.getConfigType());
        }

        //加班规则
        Map<Long, OverTimeConfigDO> overTimeConfigMap = overTimeConfigManage.getConfigMapByUserIdList(Collections.singletonList(userInfoDO.getId()));
        if (MapUtils.isNotEmpty(overTimeConfigMap)) {
            OverTimeConfigDO overTimeConfigDO = overTimeConfigMap.getOrDefault(userInfoDO.getId(), new OverTimeConfigDO());
            ruleConfig.setOverTimeConfigId(overTimeConfigDO.getId());
            ruleConfig.setOverTimeConfigName(overTimeConfigDO.getConfigName());
        }

        //补卡规则
        Map<Long, ReissueCardConfigDO> reissueCardConfigMap = reissueCardConfigManage.getConfigMapByUserIdList(Collections.singletonList(userInfoDO.getId()));
        if (MapUtils.isNotEmpty(reissueCardConfigMap)) {
            ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigMap.getOrDefault(userInfoDO.getId(), new ReissueCardConfigDO());
            ruleConfig.setReissueCardConfigId(reissueCardConfigDO.getId());
            ruleConfig.setReissueCardConfigName(reissueCardConfigDO.getConfigName());
        }
    }

    private List<AbnormalOperationRecordDTO> buildAbnormalOperationRecordDTO(EmployeeAbnormalAttendanceDO abnormalAttendanceDO) {
        List<AbnormalOperationRecordDTO> abnormalOperationRecordDTOList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDao.selectByAbnormalList(Collections.singletonList(abnormalAttendanceDO.getId()))
                .stream()
                .sorted(Comparator.comparing(EmployeeAbnormalOperationRecordDO::getId).reversed())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalOperationRecordList)) {
            return abnormalOperationRecordDTOList;
        }
        List<Long> formIdList = abnormalOperationRecordList.stream().map(EmployeeAbnormalOperationRecordDO::getFormId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, List<AttendanceFormDO>> formMap = attendanceFormDao.selectByIds(formIdList).stream().collect(Collectors.groupingBy(AttendanceFormDO::getId));
        Map<Long, List<AttendanceFormAttrDO>> attrMap = attendanceFormAttrDao.selectFormAttrByFormIdList(formIdList)
                .stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));
        for (EmployeeAbnormalOperationRecordDO recordDO : abnormalOperationRecordList) {
            AbnormalOperationRecordDTO dto = new AbnormalOperationRecordDTO();
            dto.setLastUpdDate(recordDO.getLastUpdDate());
            dto.setLastUpdUserCode(recordDO.getLastUpdUserCode());
            dto.setLastUpdUserName(recordDO.getLastUpdUserName());
            dto.setOperationType(recordDO.getOperationType());
            //异常确认
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.OFF.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.PH.getCode())
                    || StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.P.getCode())) {
                abnormalOperationRecordDTOList.add(dto);
                dto.setOperationStatus(FormStatusEnum.PASS.getCode());
                // 批量异常处理为正常的设置原因到处理内容上
                dto.setOperationContent(recordDO.getReason());
                if (ObjectUtil.isNotNull(recordDO.getAttachment())) {
                    List<AttachmentDTO> attachmentInfoList = JSONObject.parseArray(recordDO.getAttachment(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
                if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.P.getCode())
                        && Objects.equals(AttendanceAbnormalTypeEnum.NO_SCHEDULING_PLAN.getCode(), abnormalAttendanceDO.getAbnormalType())
                        && StringUtils.isBlank(recordDO.getReason())) {
                    List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.getAttendanceEmployeeDetailDO(abnormalAttendanceDO.getUserId(), abnormalAttendanceDO.getDayId());
                    if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
                        continue;
                    }
                    AttendanceEmployeeDetailDO attendanceEmployeeDetailDO = attendanceEmployeeDetailDOList
                            .stream()
                            .filter(item -> Objects.equals(item.getConcreteType(), AbnormalOperationTypeEnum.P.getCode()))
                            .findFirst()
                            .orElse(new AttendanceEmployeeDetailDO());
                    String operationContent;
                    if (RequestInfoHolder.isChinese()) {
                        operationContent = String.format(BusinessConstant.CONFIRM_P_OPERATION_CONTENT_CN, attendanceEmployeeDetailDO.getLegalWorkingHours(), attendanceEmployeeDetailDO.getAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                    } else {
                        operationContent = String.format(BusinessConstant.CONFIRM_P_OPERATION_CONTENT_EN, attendanceEmployeeDetailDO.getLegalWorkingHours(), attendanceEmployeeDetailDO.getAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
                    }
                    dto.setOperationContent(operationContent);
                }
                continue;
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode())) {
                dto.setOperationStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
                abnormalOperationRecordDTOList.add(dto);
                continue;
            }
            List<AttendanceFormDO> applicationFormDOList = formMap.get(recordDO.getFormId());
            if (CollectionUtils.isEmpty(applicationFormDOList)) {
                continue;
            }
            List<AttendanceFormAttrDO> applicationFormAttrList = attrMap.get(applicationFormDOList.get(0).getId());
            if (CollectionUtils.isEmpty(applicationFormAttrList)) {
                continue;
            }
            dto.setApplicationCode(applicationFormDOList.get(0).getApplicationCode());
            dto.setOperationStatus(applicationFormDOList.get(0).getFormStatus());
            Map<String, AttendanceFormAttrDO> formAttrMap = applicationFormAttrList.stream().collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));

            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.LEAVE.getCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (RequestInfoHolder.isChinese()) {
                    stringBuilder.append("请假时间: ");
                } else {
                    stringBuilder.append("leave date: ");
                }
                AttendanceFormAttrDO leaveStartDate = formAttrMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
                if (leaveStartDate != null) {
                    stringBuilder.append(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDate.getAttrValue()));
                }
                AttendanceFormAttrDO leaveEndDate = formAttrMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());
                if (leaveEndDate != null) {
                    stringBuilder.append(" ").append("-").append(" ").append(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDate.getAttrValue()));
                }
                dto.setOperationContent(stringBuilder.toString());

                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.OUT_OF_OFFICE.getCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (RequestInfoHolder.isChinese()) {
                    stringBuilder.append("外勤时间: ");
                } else {
                    stringBuilder.append("out of office date: ");
                }
                AttendanceFormAttrDO outOfOfficeStartDate = formAttrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
                if (outOfOfficeStartDate != null) {
                    stringBuilder.append(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDate.getAttrValue()));
                }
                AttendanceFormAttrDO outOfOfficeEndDate = formAttrMap.get(ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
                if (outOfOfficeEndDate != null) {
                    stringBuilder.append(" ").append("-").append(" ").append(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDate.getAttrValue()));
                }
                dto.setOperationContent(stringBuilder.toString());
                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
            }
            if (StringUtils.equalsIgnoreCase(recordDO.getOperationType(), AbnormalOperationTypeEnum.REISSUE_CARD.getCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (RequestInfoHolder.isChinese()) {
                    stringBuilder.append("补卡时间: ");
                } else {
                    stringBuilder.append("reissue card date: ");
                }
                AttendanceFormAttrDO correctPunchTime = formAttrMap.get(ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode());
                if (correctPunchTime != null) {
                    stringBuilder.append(DateHelper.parseYYYYMMDDHHMMSS(correctPunchTime.getAttrValue()));

                }
                dto.setOperationContent(stringBuilder.toString());
                AttendanceFormAttrDO attachmentList = formAttrMap.get(ApplicationFormAttrKeyEnum.attachmentList.getLowerCode());
                if (ObjectUtil.isNotNull(attachmentList)) {
                    List<AttachmentDTO> attachmentInfoList = JSON.parseArray(attachmentList.getAttrValue(), AttachmentDTO.class);
                    dto.setAttachmentList(attachmentInfoList);
                }
            }
            abnormalOperationRecordDTOList.add(dto);
        }
        return abnormalOperationRecordDTOList;
    }

    private List<WarehouseAbnormalDetailVO.WarehouseRecordDTO> buildWarehouseRecordVO(List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalList) {
        if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
            return Collections.emptyList();
        }
        WarehouseDetailAbnormalDTO warehouseDetailAbnormalDTO = warehouseDetailAbnormalList.get(0);
        List<WarehouseRecordDO> warehouseRecordDOList = warehouseRecordDao.selectByWarehouseDetailIds(Collections.singletonList(warehouseDetailAbnormalDTO.getWarehouseDetailId()));
        if (CollectionUtils.isEmpty(warehouseRecordDOList)) {
            return Collections.emptyList();
        }
        return warehouseRecordDOList.stream().map(record -> {
            WarehouseAbnormalDetailVO.WarehouseRecordDTO warehouseRecord = new WarehouseAbnormalDetailVO.WarehouseRecordDTO();
            warehouseRecord.setId(record.getId());
            warehouseRecord.setRecordType(record.getRecordType());
            warehouseRecord.setWarehouseTime(record.getWarehouseTime());
            return warehouseRecord;
        }).collect(Collectors.toList());
    }

    @Nullable
    private PaginationResult<EmployeeAbnormalAttendanceDTO> buildAuthQuery(EmployeeAbnormalAttendancePageQuery queryDTO) {
        List<Long> authDeptIdList = attendancePermissionService.getUserDeptPermission();
        if (CollectionUtils.isNotEmpty(queryDTO.getDeptIds())) {
            authDeptIdList = authDeptIdList.stream()
                    .filter(deptId -> queryDTO.getDeptIds().contains(deptId))
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<String> authLocationCountryList = attendancePermissionService.getUserLocationCountryPermission();
        if (StringUtils.isNotBlank(queryDTO.getLocationCountry())) {
            authLocationCountryList = authLocationCountryList.stream()
                    .filter(country -> Objects.equals(country, queryDTO.getLocationCountry()))
                    .distinct()
                    .collect(Collectors.toList());
        }

        if ((CollectionUtils.isEmpty(authLocationCountryList)
                && CollectionUtils.isEmpty(authDeptIdList))) {
            return PaginationResult.get(Collections.emptyList(), queryDTO);
        }

        queryDTO.setAuthLocationCountryList(authLocationCountryList);
        queryDTO.setAuthDeptIdList(authDeptIdList);

        if (Objects.nonNull(queryDTO.getStartDate())) {
            queryDTO.setStartDayId(DateHelper.getDayId(queryDTO.getStartDate()));
        }
        if (Objects.nonNull(queryDTO.getEndDate())) {
            queryDTO.setEndDayId(DateHelper.getDayId(queryDTO.getEndDate()));
        }

        List<Long> abnormalIds = null;
        boolean queryWarehouse = false;

        //仓内来源
        if (Objects.nonNull(queryDTO.getSource()) && Objects.equals(WAREHOUSE_SOURCE, queryDTO.getSource())) {
            if (CollectionUtils.isNotEmpty(queryDTO.getOcIdList())
                    || CollectionUtils.isNotEmpty(queryDTO.getVendorIdList())
                    || StringUtils.isNotEmpty(queryDTO.getEmploymentForm())) {
                WarehouseDetailAbnormalParam param = new WarehouseDetailAbnormalParam();
                param.setOcIdList(queryDTO.getOcIdList());
                param.setVendorIdList(queryDTO.getVendorIdList());
                param.setEmploymentForm(queryDTO.getEmploymentForm());
                param.setCountry(queryDTO.getLocationCountry());
                if (Objects.nonNull(queryDTO.getStartDayId())) {
                    param.setStartDate(DateUtil.beginOfDay(queryDTO.getStartDate()));
                }
                if (Objects.nonNull(queryDTO.getEndDate())) {
                    param.setEndDate(DateUtil.endOfDay(queryDTO.getEndDate()));
                }
                List<WarehouseDetailAbnormalDTO> warehouseDetailAbnormalList = warehouseDetailAbnormalDao.selectJoinWarehouseDetailList(param);
                if (CollectionUtils.isEmpty(warehouseDetailAbnormalList)) {
                    return PaginationResult.get(Collections.emptyList(), queryDTO);
                }
                abnormalIds = warehouseDetailAbnormalList.stream().map(WarehouseDetailAbnormalDTO::getAbnormalId).distinct().collect(Collectors.toList());
            }

            //与仓内推广国家取交集
            List<String> wpmApplicationCountryList = warehouseUserService.getWpmApplicationCountry();
            if (CollectionUtils.isNotEmpty(authLocationCountryList)) {
                Collection<String> intersectionCountry = CollectionUtils.intersection(wpmApplicationCountryList, authLocationCountryList);
                if (CollectionUtils.isNotEmpty(intersectionCountry)) {
                    queryDTO.setAuthLocationCountryList(new ArrayList<>(intersectionCountry));
                } else {
                    queryDTO.setAuthLocationCountryList(new ArrayList<>());
                }
            }
            if (CollectionUtils.isEmpty(queryDTO.getEmployeeTypeList())) {
                queryDTO.setEmployeeTypeList(EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
            }
            if (CollectionUtils.isEmpty(queryDTO.getStaffTypes())) {
                queryDTO.setStaffTypes(Collections.singletonList("warehouse"));
            }
            queryDTO.setWpmCountryList(warehouseUserService.getWpmApplicationCountry());
            queryWarehouse = true;
        } else {
            queryDTO.setWpmCountryList(warehouseUserService.getWpmApplicationCountry());
            queryDTO.setWpmEmployeeTypeList(EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE);
        }

        if (CollectionUtils.isEmpty(queryDTO.getAuthDeptIdList()) && CollectionUtils.isEmpty(queryDTO.getAuthLocationCountryList())) {
            return PaginationResult.get(Collections.emptyList(), queryDTO);
        }
        queryDTO.setAbnormalIds(abnormalIds);
        queryDTO.setQueryWarehouse(queryWarehouse);
        return null;
    }

    /**
     * 单个处理异常生成日报
     *
     * @param userInfoDO
     * @param dayId
     */
    private void sendDayReportJob(AttendanceUser userInfoDO, Long dayId) {
        // 日报生成
        DayReportJobParam param = DayReportJobParam.builder()
                .userCodeList(Collections.singletonList(userInfoDO.getUserCode()))
                .employeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                .countryArrayList(Collections.singletonList(userInfoDO.getLocationCountry()))
                .isUseCustomLocalTime(true)
                .localDate(DateHelper.getString(dayId))
                .build();
        publisher.sendReportEvent(Collections.singletonList(param));
    }

    /**
     * 批量处理异常生成日报
     *
     * @param abnormalAttendanceDOList
     */
    private void batchSendDayReportJob(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList) {
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            log.info("batchSendDayReportJob is empty");
            return;
        }
        List<Long> userIds = abnormalAttendanceDOList
                .stream()
                .filter(item -> Objects.nonNull(item.getUserId()) && Objects.nonNull(item.getDayId()))
                .map(EmployeeAbnormalAttendanceDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<AttendanceUser>> userInfoMap = Optional.ofNullable(userService.listUsersByIds(userIds))
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(AttendanceUser::getId));
        Map<Long, List<AttendanceUser>> dayMap = new HashMap<>();
        for (EmployeeAbnormalAttendanceDO userAbnormalAttendance : abnormalAttendanceDOList) {
            Long userId = userAbnormalAttendance.getUserId();
            List<AttendanceUser> userInfo = userInfoMap.get(userId);
            if (CollectionUtils.isEmpty(userInfo)) {
                continue;
            }
            Long dayId = userAbnormalAttendance.getDayId();
            if (Objects.isNull(dayId)) {
                continue;
            }
            List<AttendanceUser> userInfoList = Optional.ofNullable(dayMap.get(dayId)).orElse(new ArrayList<>());
            boolean exist = userInfoList.stream().anyMatch(item -> userId.equals(item.getId()));
            if (exist) {
                continue;
            }
            userInfoList.add(userInfo.get(0));
            dayMap.put(dayId, userInfoList);
        }
        if (dayMap.isEmpty()) {
            log.info("batchSendDayReportJob dayMap is empty");
            return;
        }

        List<DayReportJobParam> paramList = new ArrayList<>();
        for (Long dayId : dayMap.keySet()) {
            List<AttendanceUser> userInfoList = dayMap.get(dayId);
            if (CollectionUtils.isEmpty(userInfoList)) {
                continue;
            }
            List<String> userCodes = userInfoList.stream()
                    .map(AttendanceUser::getUserCode)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            List<String> locationCountryList = userInfoList.stream()
                    .map(AttendanceUser::getLocationCountry)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            DayReportJobParam param = DayReportJobParam.builder()
                    .userCodeList(userCodes)
                    .employeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                    .countryArrayList(locationCountryList)
                    .isUseCustomLocalTime(true)
                    .localDate(DateHelper.getString(dayId))
                    .build();
            paramList.add(param);
        }
        if (CollectionUtils.isNotEmpty(paramList)) {
            log.info("batchSendDayReportJob paramList:{}" + JSON.toJSONString(paramList));
            // 异步生成日报
            publisher.sendReportEvent(paramList);
        }
    }

    /**
     * 供应商批量确认异常
     */
    public void vendorBatchAbnormalConfirmHandler(AbnormalAttendanceBatchUpdateParam param) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalByIdList(param.getAbnormalIdList());
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = new ArrayList<>();
        abnormalAttendanceDOList.forEach(item -> {
            item.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
            item.setLastUpdDate(param.getConfirmTime());
            item.setLastUpdUserCode(BusinessConstant.SUPPLIER);
            item.setLastUpdUserName(BusinessConstant.SUPPLIER);

            //添加对异常的操作记录
            EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
            abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            abnormalOperationRecordDO.setAbnormalId(item.getId());
            abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode());
            abnormalOperationRecordDO.setCreateDate(param.getConfirmTime());
            abnormalOperationRecordDO.setCreateUserCode(BusinessConstant.SUPPLIER);
            abnormalOperationRecordDO.setCreateUserName(BusinessConstant.SUPPLIER);
            abnormalOperationRecordDO.setLastUpdDate(abnormalOperationRecordDO.getCreateDate());
            abnormalOperationRecordDO.setLastUpdUserCode(BusinessConstant.SUPPLIER);
            abnormalOperationRecordDO.setLastUpdUserName(BusinessConstant.SUPPLIER);
            abnormalOperationRecordDO.setIsDelete(IsDeleteEnum.NO.getCode());
            abnormalOperationRecordDO.setRecordVersion(1L);
            abnormalOperationRecordDOList.add(abnormalOperationRecordDO);
        });
        abnormalAttendanceManage.abnormalConfirmSave(abnormalAttendanceDOList, abnormalOperationRecordDOList);

        //仓内考勤确认异常处理
        abnormalAttendanceDOList.forEach(abnormal -> warehouseAttendanceHandlerService.warehouseConfirmExceptionHandler(abnormal.getId(), abnormal.getUserId(), abnormal.getDayId()));
    }

}
