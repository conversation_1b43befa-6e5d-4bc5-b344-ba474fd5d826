package com.imile.attendance.abnormal.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.WarehouseDetailSnapshotAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.warehouse.query.WarehouseDetailQuery;
import com.imile.attendance.report.AttendanceReportAbnormalHandlerService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.user.UserService;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.util.BeanUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 每日生成考勤数据快照
 *
 * <AUTHOR>
 * @since 2025/5/28
 */
@Component
@Slf4j
public class AttendanceDayGenerateSnapshotHandler {

    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceEmployeeDetailManage employeeDetailManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceEmployeeDetailSnapshotDao attendanceEmployeeDetailSnapshotDao;
    @Resource
    private EmployeeAbnormalAttendanceSnapshotDao employeeAbnormalAttendanceSnapshotDao;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private AttendanceReportAbnormalHandlerService attendanceReportAbnormalHandlerService;
    @Resource
    private UserService userService;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseDetailSnapshotDao warehouseDetailSnapshotDao;
    @Resource
    private WarehouseDetailSnapshotAdapter warehouseDetailSnapshotAdapter;


    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_DAY_GENERATE_SNAPSHOT_HANDLER)
    public ReturnT<String> attendanceDayGenerateSnapshotHandler(String param) {
        String handler = BusinessConstant.JobHandler.ATTENDANCE_DAY_GENERATE_SNAPSHOT_HANDLER;
        XxlJobLogger.log("XXL-JOB:{} Start the Param:{}", handler, param);
        AttendanceDayGenerateSnapshotHandlerParam handlerParam = StringUtils.isNotBlank(param)
                ? JSON.parseObject(param, AttendanceDayGenerateSnapshotHandlerParam.class)
                : new AttendanceDayGenerateSnapshotHandlerParam();
        if (StringUtils.isBlank(handlerParam.getCountryList())) {
            XxlJobLogger.log("XXL-JOB:{} the incoming country does not exist", handler);
            return ReturnT.FAIL;
        }

        if (ObjectUtil.isNull(handlerParam.getHour())) {
            handlerParam.setHour(0);
        }
        Integer hour = handlerParam.getHour();
        boolean saveData = handlerParam.getSaveData();

        List<String> countryList = Arrays.asList(handlerParam.getCountryList().split(BusinessConstant.DEFAULT_DELIMITER));
        // 获取对应国家的当前时间
        Map<String, Date> countryCurrentDate = attendanceCountryService.getCountryCurrentDate(countryList);

        for (String country : countryList) {
            Long attendanceDayId = handlerParam.getAttendanceDayId();
            Date date = countryCurrentDate.get(country);
            if (Objects.isNull(date)) {
                XxlJobLogger.log("XXL-JOB:{} the time for the current country was not obtained:{}", handler, country);
                continue;
            }
            if (Objects.isNull(attendanceDayId) || attendanceDayId <= 0) {
                attendanceDayId = DateHelper.getDayId(date);
                XxlJobLogger.log("XXL-JOB:{} locationCountry:{} preDayId:{}", handler, country, attendanceDayId);
            }
            log.info("XXL-JOB: {},country: {},date:{}", handler, country, DateHelper.formatYYYYMMDDHHMMSS(date));
            Long preDayId = DateHelper.getPreviousDayId(attendanceDayId);
            UserDaoQuery userQuery = UserDaoQuery.builder().locationCountry(country).isDriver(BusinessConstant.N).build();
            if (ObjectUtil.isNotEmpty(handlerParam.getUserCodes())) {
                List<String> userCodeList = Arrays.asList(handlerParam.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER));
                userQuery.setUserCodes(userCodeList);
            }
            int currentPage = 1;
            int pageSize = 5000;
            Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
            // 总记录数
            List<UserInfoDO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                log.info("XXL-JOB: {},country: {},pageUserInfoList size:{}，pageUserInfoList：{}", handler, country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                generateAttendanceSnapshot(preDayId, pageUserInfoList, date, country, hour, handler, saveData);
            }
            log.info("XXL-JOB: {},country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", handler, country, currentPage, pageSize, pageInfo.getTotal());
            while (currentPage < pageInfo.getPages()) {
                log.info("XXL-JOB: {},country：{},进入while循环", handler, country);
                currentPage++;
                log.info("XXL-JOB: {},country：{},currentPage：{}，pages：{}", handler, country, currentPage, pageInfo.getPages());
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    log.info("XXL-JOB: {}, country：{},,while循环：pageUserInfoList size:{}，pageUserInfoList：{}", handler, country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    generateAttendanceSnapshot(preDayId, pageUserInfoList, date, country, hour, handler, saveData);
                }
                log.info("XXL-JOB: {},country：{},while循环：pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", handler, country, currentPage, pageSize, pageInfo.getTotal());
                log.info("XXL-JOB: {},country：{},currentPage {}，while循环结束", handler, country, currentPage);
            }
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 生成考勤数据快照
     *
     * @param preDayId       考勤日前一天
     * @param userInfoDOList 用户离别哦
     * @param date           当前时间
     * @param country        国家
     * @param executeHour    执行时间点
     * @param handler        处理累
     * @param saveData       是否保存数据
     */
    private void generateAttendanceSnapshot(Long preDayId,
                                            List<UserInfoDO> userInfoDOList,
                                            Date date,
                                            String country,
                                            Integer executeHour,
                                            String handler,
                                            boolean saveData) {

        userInfoDOList = userInfoDOList.stream().filter(user -> userService.checkGrayscaleRange(user.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("XXL-JOB: {}, userInfoDOList is empty", handler);
            return;
        }

        // 这里只需要计算当地时间前一天的数据
        List<Long> preDayIdList = Collections.singletonList(preDayId);
        List<Long> userIds = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());

        // 校验当前时间dateTime是不是零点
        int hour = DateUtil.hour(date, true);
        log.info("generateAttendanceSnapshot locationCountry：{},localHour：{},localDate：{}", country, hour, date);

        Date finalDateNow = DateUtil.endOfDay(DateHelper.transferDayIdToDate(preDayId));

        // 获取考勤正常表数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeList = employeeDetailManage.selectByUserIdListAndDayIdList(userIds, preDayIdList);
        // 获取考勤异常表数据
        List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendance = abnormalAttendanceManage.selectAbnormalAttendanceByDayIdList(userIds, preDayIdList);

        // 获取考勤正常快照数据
        List<AttendanceEmployeeDetailSnapshotDO> employeeDetailSnapshot = attendanceEmployeeDetailSnapshotDao.selectByUserIdListAndDayIdList(userIds, preDayIdList);
        // 获取异常快照数据
        List<EmployeeAbnormalAttendanceSnapshotDO> abnormalAttendanceSnapshot = employeeAbnormalAttendanceSnapshotDao.selectByUserIdListAndDayIdList(userIds, preDayIdList);

        //查询用户打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.mapByUserIds(userIds, finalDateNow);

        //查询用户班次规则
        Map<Long, List<PunchClassConfigDTO>> punchClassConfigMap = punchClassConfigManage.mapByUserIds(userIds, finalDateNow);

        //免打卡用户集合
        Set<Long> noNeedWorkUserIdList = punchConfigMap.entrySet().stream()
                .filter(entry -> Objects.equals(entry.getValue().getConfigType(), PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        //灵活打卡用户集合
        Set<Long> flexiblePunchUserIdList = punchConfigMap.entrySet().stream()
                .filter(entry -> PunchConfigTypeEnum.isFlexibleWork(entry.getValue().getConfigType()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        //灵活打卡两次用户集合
        Set<Long> flexibleWorkTwiceUserIdList = punchConfigMap.entrySet().stream()
                .filter(entry -> PunchConfigTypeEnum.isFlexibleWorkTwice(entry.getValue().getConfigType()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());

        // 未排班的人员
        Set<Long> noClassUserIdList = new HashSet<>();
        // 排班为休息日的人员
        Set<Long> offClassUserIdList = new HashSet<>();
        // 排班满足最晚下班时间的人员
        Set<Long> userIdListPreDay = new HashSet<>();

        // 获取这些用户昨日排班计划
        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIds, preDayId, preDayId);
        if (CollectionUtils.isEmpty(userShiftConfigList)) {
            XxlJobLogger.log("XXL-JOB:{} locationCountry:{} preDayId:{} NO_CLASS_EMPLOYEE_CONFIG", handler, country, preDayId);
        }
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        for (UserInfoDO userInfo : userInfoDOList) {
            try {
                // 获取排班数据
                List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigMap.get(userInfo.getId());
                // 排班数据为空， 则未排班
                if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                    noClassUserIdList.add(userInfo.getId());
                    XxlJobLogger.log("XXL-JOB:{} userCode:{} preDayId:{} NO_CLASS_EMPLOYEE_CONFIG", handler, userInfo.getUserCode(), preDayId);
                    continue;
                }

                // 排班了，获取排上班的数据
                List<UserShiftConfigDO> nonNullConfigList = userShiftConfigDOList.stream()
                        .filter(item -> Objects.nonNull(item.getPunchClassConfigId()) && item.getPunchClassConfigId() > 0).collect(Collectors.toList());

                // 排班了，获取排休息日的数据
                List<UserShiftConfigDO> nullConfigList = userShiftConfigDOList.stream()
                        .filter(item -> Objects.isNull(item.getPunchClassConfigId())).collect(Collectors.toList());

                // 该用户该天排班为休息日
                if (CollUtil.isNotEmpty(nullConfigList)) {
                    // 处理休息数据
                    offClassUserIdList.add(userInfo.getId());
                    log.info("XXL-JOB:{} userCode:{} preDayId:{} today is a day off", handler, userInfo, preDayId);
                }

                if (CollectionUtils.isEmpty(nonNullConfigList)) {
                    continue;
                }

                UserShiftConfigDO configDO = nonNullConfigList.get(0);
                if (noNeedWorkUserIdList.contains(userInfo.getId())) {
                    log.info("XXL-JOB:{} userCode:{} preDayId:{} today no need work", handler, userInfo, preDayId);
                    continue;
                }

                List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigMap.get(configDO.getUserId());
                Optional<PunchClassConfigDTO> punchClassConfigOptional = punchClassConfigDTOList.stream()
                        .filter(config -> Objects.equals(config.getId(), configDO.getPunchClassConfigId())).findFirst();
                if (!punchClassConfigOptional.isPresent()) {
                    continue;
                }
                PunchClassConfigDTO punchClassConfigDTO = punchClassConfigOptional.get();
                if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassItemConfigList())) {
                    continue;
                }
                List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(punchClassConfigDTO.getClassItemConfigList());
                if (flexiblePunchUserIdList.contains(userInfo.getId()) && itemConfigDOList.size() > 1) {
                    log.error("每日考勤数据快照,用户：{} 打卡规则跟班次时段不匹配", userInfo.getUserCode());
                    continue;
                }
                for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
                    DayPunchTimeDTO dayPunchTimeDTO;
                    if (flexibleWorkTwiceUserIdList.contains(userInfo.getId())) {
                        dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(preDayId, itemConfigDO);
                    } else {
                        dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(preDayId, itemConfigDO.getId(), itemConfigDOList);
                    }
                    if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                        continue;
                    }

                    if (date.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0) {
                        // 最晚下班时间
                        Date latestEndTime = dayPunchTimeDTO.getDayPunchEndTime();
                        // 判断当前时间是否大于等于最晚下班时间
                        if (date.compareTo(latestEndTime) >= 0) {
                            // 只需要前一天
                            userIdListPreDay.add(userInfo.getId());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("XXL-JOB handler:{} userCode:{} preDayId:{},exception:{}", handler, userInfo.getUserCode(), preDayId, Throwables.getStackTraceAsString(e));
            }
        }

        // 落库数据
        List<AttendanceEmployeeDetailSnapshotDO> targetEmployeeDetailSnapshot = Lists.newArrayList();
        List<EmployeeAbnormalAttendanceSnapshotDO> targetAbnormalAttendanceSnapshot = Lists.newArrayList();


        // 1：处理排班为休息日 + 未排班 + 免打卡的数据
        noClassUserIdList.addAll(offClassUserIdList);
        noClassUserIdList.addAll(noNeedWorkUserIdList);
        if (CollUtil.isNotEmpty(noClassUserIdList)) {
            // 如果该国家现在不是凌晨0点钟，就不执行。只有每一个国家凌晨四点钟的时候执行后续业务
            if (ObjectUtil.equal(hour, executeHour)) {
                log.info("XXL-JOB:{} locationCountry:{} meet 0 points", handler, country);
                // 执行数据库操作
                for (Long userId : noClassUserIdList) {
                    handlerSnapshot(userId, attendanceEmployeeList, employeeAbnormalAttendance, employeeDetailSnapshot, abnormalAttendanceSnapshot, targetEmployeeDetailSnapshot, targetAbnormalAttendanceSnapshot, handler);
                }
            }
        }

        // 2: 处理排班为上班的数据
        for (Long userId : userIdListPreDay) {
            handlerSnapshot(userId, attendanceEmployeeList, employeeAbnormalAttendance, employeeDetailSnapshot, abnormalAttendanceSnapshot, targetEmployeeDetailSnapshot, targetAbnormalAttendanceSnapshot, handler);
        }

        // 落库
        log.info("XXL-JOB {} save data targetEmployeeDetailSnapshot size:{} targetAbnormalAttendanceSnapshot size:{}", handler, targetEmployeeDetailSnapshot.size(), targetAbnormalAttendanceSnapshot.size());
        log.info("XXL-JOB {} save data targetEmployeeDetailSnapshot:{} targetAbnormalAttendanceSnapshot:{}", handler, targetEmployeeDetailSnapshot, targetAbnormalAttendanceSnapshot);

        if (Boolean.TRUE.equals(saveData)) {
            employeeDetailManage.attendanceGenerateSnapshotSaveOrUpdate(targetEmployeeDetailSnapshot, targetAbnormalAttendanceSnapshot);
        }

        // 3. 仓内考勤初始表
        List<WarehouseDetailSnapshotDO> warehouseDetailSnapshotDOList = new ArrayList<>();

        WarehouseDetailQuery warehouseDetailParam = new WarehouseDetailQuery();
        Date warehouseDate = DateUtil.parse(preDayId.toString(), DateFormatterUtil.FORMAT_YYYYMMDD);
        warehouseDetailParam.setUserIdList(userIds);
        warehouseDetailParam.setWarehouseDate(warehouseDate);
        List<WarehouseDetailDO> warehouseDetailDOList = warehouseDetailDao.selectByCondition(warehouseDetailParam);

        List<WarehouseDetailSnapshotDO> warehouseDetailOriginalDOList = warehouseDetailSnapshotDao.selectByWarehouseDateAndUserIds(warehouseDate, userIds);

        for (Long userId : userIdListPreDay) {
            handlerWarehouseSnapshot(userId, warehouseDetailDOList, warehouseDetailOriginalDOList, warehouseDetailSnapshotDOList, handler);
        }

        if (CollectionUtils.isNotEmpty(targetAbnormalAttendanceSnapshot)) {
            warehouseDetailSnapshotAdapter.saveOrUpdateBatch(warehouseDetailSnapshotDOList);
        }

        // 处理日报逻辑
        attendanceReportAbnormalHandlerService.batchSnapShotExecute(preDayId,
                targetEmployeeDetailSnapshot, targetAbnormalAttendanceSnapshot, noNeedWorkUserIdList);
    }

    /**
     * 处理仓内考勤快照表数据
     */
    private static void handlerWarehouseSnapshot(Long userId,
                                                 List<WarehouseDetailDO> warehouseDetailDOList,
                                                 List<WarehouseDetailSnapshotDO> warehouseDetailOriginalDOList,
                                                 List<WarehouseDetailSnapshotDO> targetWarehouseDetailOriginalDOList,
                                                 String handler) {
        // 过滤仓内考勤日报
        List<WarehouseDetailDO> warehouseDetails = warehouseDetailDOList.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(warehouseDetails)) {
            log.info("XXL-JOB:{} userId:{} origin data is empty continue", handler, userId);
            return;
        }

        List<WarehouseDetailSnapshotDO> warehouseDetailOriginals = warehouseDetailOriginalDOList.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());
        // 获取仓内考勤快照表数据不为空则跳过
        if (CollUtil.isNotEmpty(warehouseDetailOriginals) && warehouseDetails.size() == warehouseDetailOriginals.size()) {
            log.info("XXL-JOB:{} userId:{} snapshot is not empty continue", handler, userId);
            return;
        }

        if (CollectionUtils.isEmpty(warehouseDetailOriginals)) {
            List<WarehouseDetailSnapshotDO> warehouseDetailOriginalSnapshotList = BeanUtils.convert(WarehouseDetailSnapshotDO.class, warehouseDetails);
            targetWarehouseDetailOriginalDOList.addAll(warehouseDetailOriginalSnapshotList);
            return;
        }

        List<Long> warehouseDetailIds = warehouseDetailOriginals.stream().map(WarehouseDetailSnapshotDO::getId).distinct().collect(Collectors.toList());
        List<WarehouseDetailDO> insertWarehouseDetailList = warehouseDetails.stream().filter(item -> !warehouseDetailIds.contains(item.getId())).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(insertWarehouseDetailList)) {
            // 插入到快照表里面
            List<WarehouseDetailSnapshotDO> warehouseDetailOriginalSnapshotList = BeanUtils.convert(WarehouseDetailSnapshotDO.class, insertWarehouseDetailList);
            targetWarehouseDetailOriginalDOList.addAll(warehouseDetailOriginalSnapshotList);
        }
    }

    /**
     * 处理快照表数据
     *
     * @param userId                           用户ID
     * @param attendanceEmployeeList           考勤正常表数据
     * @param employeeAbnormalAttendance       考勤异常表数据
     * @param employeeDetailSnapshot           考勤正常快照表数据
     * @param abnormalAttendanceSnapshot       考勤异常快照表数据
     * @param targetEmployeeDetailSnapshot     目标考勤正常快照表数据
     * @param targetAbnormalAttendanceSnapshot 目标考勤异常快照表数据
     * @param handler                          处理类
     */
    private static void handlerSnapshot(Long userId,
                                        List<AttendanceEmployeeDetailDO> attendanceEmployeeList,
                                        List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendance,
                                        List<AttendanceEmployeeDetailSnapshotDO> employeeDetailSnapshot,
                                        List<EmployeeAbnormalAttendanceSnapshotDO> abnormalAttendanceSnapshot,
                                        List<AttendanceEmployeeDetailSnapshotDO> targetEmployeeDetailSnapshot,
                                        List<EmployeeAbnormalAttendanceSnapshotDO> targetAbnormalAttendanceSnapshot,
                                        String handler) {
        // 获取考勤正常表数据
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetail = attendanceEmployeeList.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());
        // 获取考勤异常表数据
        List<EmployeeAbnormalAttendanceDO> attendanceEmployeeAbnormal = employeeAbnormalAttendance.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());

        // 如果正常异常表数据都为空，则不操作
        if (CollUtil.isEmpty(attendanceEmployeeDetail) && CollUtil.isEmpty(attendanceEmployeeAbnormal)) {
            log.info("XXL-JOB:{} userId:{} origin data is empty continue", handler, userId);
            return;
        }

        List<AttendanceEmployeeDetailSnapshotDO> employeeDetailSnapshotList = employeeDetailSnapshot.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());
        List<EmployeeAbnormalAttendanceSnapshotDO> abnormalAttendanceSnapshotList = abnormalAttendanceSnapshot.stream().filter(item -> ObjectUtil.equal(userId, item.getUserId())).collect(Collectors.toList());
        // 获取快照正常异常表数据不为空则跳过
        if (CollUtil.isNotEmpty(employeeDetailSnapshotList) || CollUtil.isNotEmpty(abnormalAttendanceSnapshotList)) {
            log.info("XXL-JOB:{} userId:{} snapshot is not empty continue", handler, userId);
            return;
        }

        // 否则插入到快照表里面
        List<AttendanceEmployeeDetailSnapshotDO> employeeDetailSnapshotInfoList = BeanUtils.convert(AttendanceEmployeeDetailSnapshotDO.class, attendanceEmployeeDetail);
        List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotInfoList = BeanUtils.convert(EmployeeAbnormalAttendanceSnapshotDO.class, attendanceEmployeeAbnormal);
        targetEmployeeDetailSnapshot.addAll(employeeDetailSnapshotInfoList);
        targetAbnormalAttendanceSnapshot.addAll(employeeAbnormalAttendanceSnapshotInfoList);
    }

    @Data
    public static class AttendanceDayGenerateSnapshotHandlerParam {
        /**
         * 入参:国家
         */
        private String countryList;

        /**
         * 入参:需要计算的考勤天
         */
        private Long attendanceDayId;

        /**
         * 入参:用户编码   考勤审批通过后会通过这个字段来
         */
        private String userCodes;

        /**
         * 执行小时时间点，默认0点
         */
        private Integer hour = 0;

        /**
         * 是否落库
         */
        private Boolean saveData = true;


        /**
         * 界面无法输入，开始时间  yyyy-MM-dd HH:mm:ss  周期的开始时间
         */
        private Date startTime;

        /**
         * 界面无法输入，结束时间  yyyy-MM-dd HH:mm:ss  周期的结束时间
         */
        private Date endTime;

        /**
         * 界面无法输入，开始时间  yyyyMMdd  周期的开始时间的long
         */
        private Long startDayId;

        /**
         * 界面无法输入，结束时间  yyyyMMdd  周期的结束时间的long
         */
        private Long endDayId;

        /**
         * 界面无法输入，考勤时间，当天的结束时间  yyyy-MM-dd HH:mm:ss
         */
        private Date attendanceTime;

        /**
         * 界面无法输入，改天考勤真正的起始时间 yyyy-MM-dd HH:mm:ss
         */
        private Date actualAttendanceStartTime;

        /**
         * 界面无法输入，改天考勤真正的结束时间  yyyy-MM-dd HH:mm:ss
         */
        private Date actualAttendanceEndTime;

        /**
         * 界面无法输入，当天的考勤真正开始/结束时间是否存在交集
         */
        private Integer isIntersection;

    }


}
