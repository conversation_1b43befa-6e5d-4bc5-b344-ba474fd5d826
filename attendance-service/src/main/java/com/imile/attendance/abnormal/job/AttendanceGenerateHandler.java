package com.imile.attendance.abnormal.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.clock.param.AttendanceGenerateHandlerParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 考勤每日结算
 *
 * <AUTHOR>
 * @since 2025/6/13
 */
@Slf4j
@Component
public class AttendanceGenerateHandler {
    @Resource
    private AttendanceGenerateService attendanceGenerateService;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_GENERATE_HANDLER)
    public ReturnT<String> attendanceGenerateHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.ATTENDANCE_GENERATE_HANDLER, param);
        AttendanceGenerateHandlerParam handlerParam = StringUtils.isNotBlank(param)
                ? JSON.parseObject(param, AttendanceGenerateHandlerParam.class)
                : new AttendanceGenerateHandlerParam();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = new AttendanceCalculateHandlerDTO();
        calculateHandlerDTO.setCountryList(handlerParam.getCountryList());
        calculateHandlerDTO.setUserCodes(handlerParam.getUserCodes());
        if (handlerParam.getAttendanceDayId() != null) {
            calculateHandlerDTO.setAttendanceDayId(handlerParam.getAttendanceDayId());
            attendanceGenerateService.attendanceCalculateHandler(calculateHandlerDTO);
            return ReturnT.SUCCESS;
        }

        calculateHandlerDTO.setAttendanceDayId(DateHelper.getDayId(DateUtil.offsetDay(new Date(), -2)));
        attendanceGenerateService.attendanceCalculateHandler(calculateHandlerDTO);
        return ReturnT.SUCCESS;
    }
}
