package com.imile.attendance.abnormal.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceMinuteCalculateService;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.DayItemInfoDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 固定班次打卡考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service("fixedWorkCalculateServiceImpl")
@Strategy(value = AttendanceCalculateService.class, implKey = "FixedWorkCalculateServiceImpl")
public class FixedWorkCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {
    @Resource
    private AttendanceMinuteCalculateService attendanceMinuteCalculateService;


    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType, boolean isWarehouse, String employeeType) {
        return !isWarehouse
                && CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FIXED_WORK.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        UserInfoDO user = calculateContext.getUser();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 固定班次计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();
        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);

        //需要先将改天的所有审批通过的请假/外勤拼接起来，一个时刻可以有多个审批通过的假期，每次请10分钟，请5次，就有5个单据

        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = attendanceMinuteCalculateService.dayFormInfoBuild(calculateContext.getUserPassFormBOList(), Collections.emptyList());
        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();

        UserAttendancePunchConfigDTO attendancePunchConfigDTO = calculateContext.getUserAttendancePunchConfigDTOList().get(0);
        List<PunchClassItemConfigDTO> classItemConfigDOList = attendancePunchConfigDTO.getClassConfigDO().getClassItemConfigList();
        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigDOList);
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            DayItemConfigDateDTO itemConfigDate = attendanceMinuteCalculateService.buildDayItemConfigDateDTO(calculateHandlerDTO.getAttendanceDayId(), itemConfigDO, itemConfigDOList, calculateContext.getPunchRecordDOList());
            if (Objects.isNull(itemConfigDate)) {
                continue;
            }

            log.info("attendanceCalculate userCode:{}, date:{},sortNo:{},itemId:{},当天的班次的具体信息 itemConfigDate:{}",
                    user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), itemConfigDO.getSortNo(), itemConfigDO.getId(), JSON.toJSONString(itemConfigDate));

            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            //计算单据中请假或外勤的出勤时长
            BigDecimal usedMinutes = calculateLeaveHandler(calculateContext, addEmployeeDetailDOList, updateEmployeeDetailDOList, handlerFormDTOList, itemConfigDate, filterFormDTOList);
            if (usedMinutes == null) {
                continue;
            }
            log.info("attendanceCalculate userCode:{}, date:{},itemTotalMinutes:{},usedMinutes:{},filterFormDTOList:{}", user.getUserCode(),
                    calculateHandlerDTO.getAttendanceDayId(), itemConfigDate.getItemTotalMinutes(), usedMinutes, JSON.toJSONString(filterFormDTOList));

            //当前时刻完全请假
            if ((itemConfigDate.getItemTotalMinutes().subtract(usedMinutes)).compareTo(BigDecimal.ZERO) < 1) {
                log.info("attendanceCalculate userCode:{}, date:{},当天完全请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                continue;
            }

            //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
            List<UserPunchRecordBO> itemPunchRecordList = getEffectiveUserPunchRecord(itemConfigDate.getEarliestPunchInTime(), itemConfigDate.getLatestPunchOutTime(), calculateContext.getPunchRecordDOList());

            //情况1:当天没有打卡时间
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次没有打卡时间", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                normalPunchNoPunchTimeHandler(user, calculateHandlerDTO, itemConfigDate.getPunchInTime(), itemConfigDate.getPunchOutTime(), calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId(),
                        attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), addAbnormalAttendanceDOList);
                continue;
            }

            //情况2:当天完全没请假，看打卡记录
            if (CollectionUtils.isEmpty(filterFormDTOList)) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次有打卡记录,完全没请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                normalPunchHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList,
                        calculateContext.getPunchConfigDO().getId(), attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(),
                        itemConfigDate, itemConfigDate.getItemTotalMinutes().subtract(usedMinutes));
                continue;
            }

            //情况3:当天存在请假  有一条打卡记录
            if (itemPunchRecordList.size() == 1) {
                log.info("attendanceCalculate userCode:{}, date:{}, 当天固定班次有一条打卡记录且存在请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
                leaveSinglePunchRecordHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, filterFormDTOList, calculateContext.getPunchConfigDO().getId(),
                        attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(), itemConfigDate.getPunchInTime(),
                        itemConfigDate.getPunchOutTime(), itemConfigDate.getRestStartTime(), itemConfigDate.getRestEndTime(), itemConfigDate.getLatestPunchInTime());
                continue;
            }
            //情况4: 按照下面三个区间考虑所有情况
            //最早上班时间-上班时间
            //上班时间-下班时间
            //下班时间到-最晚下班时间
            log.info("attendanceCalculate userCode:{}, date:{},  当天固定班次有多条打卡记录且存在请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            leaveDayBatchHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, addEmployeeDetailDOList, filterFormDTOList,
                    calculateContext.getPunchConfigDO().getId(), attendancePunchConfigDTO.getClassConfigDO().getId(), itemConfigDO.getId(), calculateContext.getAttendanceType(),
                    itemConfigDate, itemConfigDate.getItemTotalMinutes().subtract(usedMinutes));
        }

        //过滤审批中的异常
        addAbnormalAttendanceDOList = filterAbnormalAttendanceList(calculateContext.getUserAbnormalAttendanceDOList(), addAbnormalAttendanceDOList);
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
    }

    /**
     * 当天没有打卡记录
     */
    private void normalPunchNoPunchTimeHandler(UserInfoDO user,
                                               AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                               Date punchInTime,
                                               Date punchOutTime,
                                               String attendanceType,
                                               Long punchConfigId,
                                               Long classId,
                                               Long itemConfigId,
                                               List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //2条异常，上下班都缺卡
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO beforeLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(beforeLackAbnormalAttendanceDO);

        abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
        EmployeeAbnormalAttendanceDO afterLackAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(afterLackAbnormalAttendanceDO);
    }

    /**
     * 时刻没有请假，正常打卡
     */
    private void normalPunchHandler(UserInfoDO user,
                                    AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                    List<UserPunchRecordBO> itemPunchRecordList,
                                    List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                    List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                    Long punchConfigId,
                                    Long classId,
                                    Long itemConfigId,
                                    String attendanceType,
                                    DayItemConfigDateDTO itemConfigDateDTO,
                                    BigDecimal itemTotalMinutes) {
        Date punchInTime = itemConfigDateDTO.getPunchInTime();
        Date punchOutTime = itemConfigDateDTO.getPunchOutTime();
        Date latestPunchInTime = itemConfigDateDTO.getLatestPunchInTime();

        //一次打卡
        if (itemPunchRecordList.size() == 1) {
            singlePunchRecordHandler(user, calculateHandlerDTO, itemPunchRecordList, addAbnormalAttendanceDOList, punchConfigId,
                    classId, itemConfigId, attendanceType, punchInTime, punchOutTime, latestPunchInTime);
            return;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }
        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                    null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
            return;
        }

        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }
            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE))
                    .compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            //还是早退，注意这里，下班补卡时间不再是班次的下班时间了，应该是弹性时间后移
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(itemPunchRecordList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班肯定迟到
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(0).getFormatPunchTime());
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(abnormalAttendanceDO);

        //下班也早退
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
            AbnormalExtendDTO leaveEarlyAbnormalExtendDTO = new AbnormalExtendDTO();
            leaveEarlyAbnormalExtendDTO.setActualPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime());
            leaveEarlyAbnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO leaveEarlyAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(leaveEarlyAbnormalExtendDTO));
            addAbnormalAttendanceDOList.add(leaveEarlyAbnormalAttendanceDO);
        }
    }


    /**
     * 存在请假和多条打卡记录
     */
    private void leaveDayBatchHandler(UserInfoDO user,
                                      AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                      List<UserPunchRecordBO> itemPunchRecordList,
                                      List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList,
                                      List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                      List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                      Long punchConfigId,
                                      Long classId,
                                      Long itemConfigId,
                                      String attendanceType,
                                      DayItemConfigDateDTO itemConfigDateDTO,
                                      BigDecimal itemTotalMinutes) {

        Date earliestPunchInTime = itemConfigDateDTO.getEarliestPunchInTime();
        Date punchInTime = itemConfigDateDTO.getPunchInTime();
        Date latestPunchInTime = itemConfigDateDTO.getLatestPunchInTime();
        Date punchOutTime = itemConfigDateDTO.getPunchOutTime();
        Date latestPunchOutTime = itemConfigDateDTO.getLatestPunchOutTime();
        Date restStartTime = itemConfigDateDTO.getRestStartTime();
        Date restEndTime = itemConfigDateDTO.getRestEndTime();
        long betweenMinutes = itemConfigDateDTO.getBetweenMinutes();

        // [最早上班时间-上班时间)
        List<UserPunchRecordBO> punchBeforeCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(earliestPunchInTime) > -1
                        && o.getFormatPunchTime().compareTo(punchInTime) < 0)
                .collect(Collectors.toList());

        //[上班时间-下班时间]
        List<UserPunchRecordBO> punchBetweenCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchInTime) > -1
                        && o.getFormatPunchTime().compareTo(punchOutTime) < 1)
                .collect(Collectors.toList());

        //(下班时间-最晚下班时间]
        List<UserPunchRecordBO> punchAfterCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchOutTime) > 0
                        && o.getFormatPunchTime().compareTo(latestPunchOutTime) < 1)
                .collect(Collectors.toList());

        List<DayItemInfoDTO> dayItemInfoList = new ArrayList<>();
        //根据打卡记录过滤单据中外勤或请假的时长计算得到的打卡出勤时长
        BigDecimal presentMinutes;
        //打卡时间全部在正规上下班中 需要考虑打卡时间和申请单据的时间交集部分
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            //整个时段内，除去请假外的打卡记录的区间
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoList, filterFormDTOList, punchBetweenCardList);
            //打卡时间段去除休息时间段得到的出勤时长
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoList, restStartTime, restEndTime);
            log.info("attendanceCalculate userCode:{}, date:{}, 固定班次计算,已出勤时长,presentMinutes:{}", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), presentMinutes);
            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            //早退
            if (punchBetweenCardList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(punchBetweenCardList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
                return;
            }

            Date firstPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            boolean checkNoLate = checkPunchTimeInRestOrFormTimeRange(firstPunchTime, restStartTime, restEndTime, filterFormDTOList);
            //迟到
            if (!checkNoLate) {
                AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
                lateAbnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(0).getFormatPunchTime());
                lateAbnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
                addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
            }

            Date lastPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            boolean checkNoLeaveEarly = checkPunchTimeInRestOrFormTimeRange(lastPunchTime, restStartTime, restEndTime, filterFormDTOList);
            //早退
            if (!checkNoLeaveEarly) {
                AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
                abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
                abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
                addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            }
            return;
        }

        //开始时间和中间时间为空,直接是上班卡未打异常
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchInTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //结束时间和中间时间为空,直接是下班卡未打异常
        if (CollectionUtils.isEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //下班未打卡
        if (CollectionUtils.isNotEmpty(punchBeforeCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            presentMinutes = attendanceMinuteCalculateService.getPresentMinutes(true, restStartTime, restEndTime, punchInTime, endPunchTime, dayItemInfoList, filterFormDTOList);
            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            //早退
            AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
            abnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime());
            abnormalExtendDTO.setCorrectPunchTime(punchOutTime);
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                    punchConfigId, classId, itemConfigId, JSON.toJSONString(abnormalExtendDTO));
            addAbnormalAttendanceDOList.add(abnormalAttendanceDO);
            return;
        }

        //上班未打卡
        if (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchBeforeCardList)) {
            Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            Date endPunchTime = punchOutTime;
            if (betweenMinutes != 0) {
                // 说明存在弹性时长，这边计算用户应该的的下班打卡时间（应该就是正常上下班的时间，包含弹性时长）
                Date shouldEndPunchTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                // 用户真实下班打卡时间
                Date lastPunchOutTime = punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime();
                // 获取最新的下班时间，判断是否大于弹性下班时长，大于等于的话，取弹性下班时间，否则取正常下班时长
                if (lastPunchOutTime.compareTo(shouldEndPunchTime) > -1) {
                    endPunchTime = shouldEndPunchTime;
                }
            }
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.getPresentMinutes(false, restStartTime, restEndTime, beginPunchTime, endPunchTime, dayItemInfoList, filterFormDTOList);

            if (presentMinutes.compareTo(itemTotalMinutes) > -1) {
                AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                        null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
                addEmployeeDetailDOList.add(userAttendance);
                return;
            }

            Date firstPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            if (firstPunchTime.compareTo(latestPunchInTime) > 0) {
                //迟到
                AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
                lateAbnormalExtendDTO.setActualPunchTime(punchBetweenCardList.get(0).getFormatPunchTime());
                lateAbnormalExtendDTO.setCorrectPunchTime(punchInTime);
                EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LATE.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
                addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
                return;
            }

            Date lastPunchTime = punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime();
            boolean checkNoLeaveEarly = checkPunchTimeInRestOrFormTimeRange(lastPunchTime, restStartTime, restEndTime, filterFormDTOList);
            //早退
            if (!checkNoLeaveEarly) {
                AbnormalExtendDTO lateAbnormalExtendDTO = new AbnormalExtendDTO();
                lateAbnormalExtendDTO.setActualPunchTime(punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime());
                // 这边应该是使用上下班时间间隔作为偏移量
                lateAbnormalExtendDTO.setCorrectPunchTime(DateUtil.offsetMinute(punchBetweenCardList.get(0).getFormatPunchTime(), (int) DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE)));
                EmployeeAbnormalAttendanceDO lateAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode(), attendanceType,
                        punchConfigId, classId, itemConfigId, JSON.toJSONString(lateAbnormalExtendDTO));
                addAbnormalAttendanceDOList.add(lateAbnormalAttendanceDO);
            }
            return;

        }

        //全部打卡,（或者前后都有打卡，就中间没打卡）没有异常，就看P的时间
        if ((CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))
                || (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))) {
            AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                    null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
            addEmployeeDetailDOList.add(userAttendance);
        }
    }
}
