package com.imile.attendance.calendar;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
@Service
public class CalendarConfigRangeService {

    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private MigrationService migrationService;


    /**
     * 为员工关联日历规则
     * 1. 根据员工所在国家获取该国家下所有的日历配置
     * 2. 优先使用默认日历配置作为基础配置
     * 3. 如果员工所在部门在某个日历配置的适用范围内，则使用该部门对应的日历配置
     * 4. 生效时间以员工入职确认时间为准
     *
     * @param attendanceUser            考勤用户信息，包含用户ID、部门ID、国家等基础信息
     * @param attendanceUserEntryRecord 考勤用户入职记录，包含入职确认时间等信息
     */
    public void addCalendarConfigRange(AttendanceUser attendanceUser,
                                       AttendanceUserEntryRecord attendanceUserEntryRecord) {
        // 过滤人员是否在灰度名单中
        Boolean userIsEnableNewAttendance = migrationService.verifyUserIsEnableNewAttendance(attendanceUser.getId());
        if (!userIsEnableNewAttendance) {
            log.info("userCode:{} 不在灰度名单中，无需为其关联日历规则", attendanceUser.getUserCode());
            return;
        }
        // 判断员工是否已经有绑定日历,如果已经有绑定日历，无需重复配置
        List<CalendarConfigRangeDO> userCalendarConfigRangeList =
                calendarManage.selectCalendarConfigRange(Collections.singletonList(attendanceUser.getId()));
        if (CollectionUtils.isNotEmpty(userCalendarConfigRangeList)) {
            log.info("userCode:{} 已经有日历配置:{}，无需重复配置", attendanceUser.getUserCode(), userCalendarConfigRangeList);
            return;
        }
        List<CalendarConfigDO> calendarConfigList = calendarConfigDao.getCountryCalendarByType(
                null, attendanceUser.getLocationCountry());
        if (CollectionUtils.isEmpty(calendarConfigList)) {
            log.info("userCode:{} country:{} 不存在考勤日历配置,无法为其关联日历规则",
                    attendanceUser.getUserCode(), attendanceUser.getLocationCountry());
            return;
        }
        List<CalendarConfigDO> defaultConfigList = calendarConfigList.stream()
                .filter(item -> item.getType().equals(AttendanceTypeEnum.DEFAULT.name()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultConfigList)) {
            log.info("userCode:{} country:{} 不存在默认日志，非法数据，无法为其关联日历规则",
                    attendanceUser.getUserCode(), attendanceUser.getLocationCountry());
            return;
        }
        CalendarConfigDO defaultCalendarConfigDO = defaultConfigList.get(0);
        //查询入职确认时间作为日历适用范围生效时间
        Date startDate = attendanceUserEntryRecord.getConfirmDate();

        CalendarConfigRangeDO configRangeDO = new CalendarConfigRangeDO();
        configRangeDO.setId(defaultIdWorker.nextId());
        configRangeDO.setBizId(attendanceUser.getId());
        configRangeDO.setAttendanceConfigId(defaultCalendarConfigDO.getId());
        configRangeDO.setAttendanceConfigNo(defaultCalendarConfigDO.getAttendanceConfigNo());
        configRangeDO.setRangeType(RangeTypeEnum.DEFAULT.name());
        configRangeDO.setStartDate(startDate);
        configRangeDO.setEndDate(BusinessConstant.DEFAULT_END_TIME);
        configRangeDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(configRangeDO);
        for (CalendarConfigDO calendarConfigDO : calendarConfigList) {
            if (StringUtils.isBlank(calendarConfigDO.getDeptIds())) {
                continue;
            }
            List<Long> deptList = calendarConfigDO.listDeptIds();
            if (deptList.contains(attendanceUser.getDeptId())) {
                configRangeDO.setAttendanceConfigId(calendarConfigDO.getId());
                configRangeDO.setAttendanceConfigNo(calendarConfigDO.getAttendanceConfigNo());
                configRangeDO.setRangeType(RangeTypeEnum.DEPT.name());
            }
        }
        calendarConfigRangeDao.save(configRangeDO);
        log.info("userCode:{} country:{}入职关联了日历规则：{}",
                attendanceUser.getUserCode(), attendanceUser.getLocationCountry(), configRangeDO);
    }

    /**
     * 仓内外包增加个人考勤日历配置
     */
    public void addWarehouseCalendarConfigRange(AttendanceUser attendanceUser) {
        List<CalendarConfigDO> configList = calendarConfigDao.getCountryCalendarByType(
                null, attendanceUser.getLocationCountry());
        if (CollectionUtils.isEmpty(configList)) {
            log.info("addWarehouseCalendarConfigRange 日历不存在,userCode:{}",attendanceUser.getUserCode());
            return ;
        }
        List<CalendarConfigDO> defaultConfigList = configList.stream()
                .filter(item -> item.getType().equals(AttendanceTypeEnum.DEFAULT.name()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultConfigList)) {
            log.info("addWarehouseCalendarConfigRange 默认日历不存在,userCode:{}",attendanceUser.getUserCode());
            return;
        }
        CalendarConfigRangeDO configRangeDO = new CalendarConfigRangeDO();
        configRangeDO.setId(defaultIdWorker.nextId());
        configRangeDO.setBizId(attendanceUser.getId());
        configRangeDO.setAttendanceConfigId(defaultConfigList.get(0).getId());
        configRangeDO.setAttendanceConfigNo(defaultConfigList.get(0).getAttendanceConfigNo());
        configRangeDO.setRangeType(RangeTypeEnum.DEFAULT.name());
        // 获取当前时间减一天的时间 昨天
        Date date = new Date();
        DateTime yesterday = DateUtil.offsetDay(date, -1);
        DateTime startDate = DateUtil.beginOfDay(yesterday);
        log.info("date : {} yesterday : {} startDate : {}", date, yesterday, startDate);

        configRangeDO.setStartDate(startDate);
        configRangeDO.setEndDate(BusinessConstant.DEFAULT_END_TIME);
        configRangeDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(configRangeDO);
        for (CalendarConfigDO configDO : configList) {
            if (StringUtils.isBlank(configDO.getDeptIds())) {
                continue;
            }
            List<Long> deptList = Arrays.asList((Long[]) ConvertUtils.convert(configDO.getDeptIds().split(","), Long.class));
            if (deptList.contains(attendanceUser.getDeptId())) {
                configRangeDO.setAttendanceConfigId(configDO.getId());
                configRangeDO.setAttendanceConfigNo(configDO.getAttendanceConfigNo());
                configRangeDO.setRangeType(RangeTypeEnum.DEPT.name());
            }
        }
        calendarConfigRangeDao.save(configRangeDO);
    }


    /**
     * 员工确认离职更新日历适用范围
     */
    public void updateAttendanceConfigRange(UserInfoDO user, Date actualDimissionDate, boolean isDelayConfirm) {
        // 过滤灰度名单
        Boolean userIsEnableNewAttendance = migrationService.verifyUserIsEnableNewAttendance(user.getId());
        if (!userIsEnableNewAttendance) {
            log.info("userId:{} 不在灰度名单中，无需为其关联日历规则", user.getUserCode());
            return;
        }
        List<CalendarConfigRangeDO> updateCalendarConfigRangeDOList = new ArrayList<>();
        CalendarConfigDateQuery calendarConfigDateQuery = new CalendarConfigDateQuery();
        calendarConfigDateQuery.setUserIds(Collections.singletonList(user.getId()));
        List<CalendarConfigRangeDO> configRangeList = calendarConfigRangeDao.selectCalendarConfigByDate(calendarConfigDateQuery);
        Date finalDateNow = DateUtil.endOfDay(actualDimissionDate);
        Optional<CalendarConfigRangeDO> attendanceConfigRangeOptional = configRangeList
                .stream()
                .filter(item -> item.getStartDate().compareTo(finalDateNow) < 1 &&
                        item.getEndDate().compareTo(finalDateNow) > -1)
                .findFirst();

        if (!attendanceConfigRangeOptional.isPresent()) {
            return;
        }
        CalendarConfigRangeDO calendarConfigRangeDO = attendanceConfigRangeOptional.get();
        calendarConfigRangeDO.setEndDate(finalDateNow);
        //如果当天确认离职,当天的异常需要计算
        calendarConfigRangeDO.setIsLatest(isDelayConfirm ? BusinessConstant.N : BusinessConstant.Y);
        BaseDOUtil.fillDOUpdateByUserOrSystem(calendarConfigRangeDO);
        updateCalendarConfigRangeDOList.add(calendarConfigRangeDO);

        List<CalendarConfigRangeDO> expireConfigRangeList = configRangeList.stream().filter(range -> range.getId().compareTo(calendarConfigRangeDO.getId()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireConfigRangeList)) {
            expireConfigRangeList.forEach(range -> {
                range.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
            updateCalendarConfigRangeDOList.addAll(expireConfigRangeList);
        }
        calendarConfigRangeDao.updateBatchById(updateCalendarConfigRangeDOList);
    }


}
