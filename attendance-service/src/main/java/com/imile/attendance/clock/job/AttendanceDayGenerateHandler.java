package com.imile.attendance.clock.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.clock.param.AttendanceGenerateHandlerParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.framework.redis.client.ImileRedisClient;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/26
 * @Description 用户未打卡人员每日考勤生成，每一个小时执行一次
 */
@Slf4j
@Component
public class AttendanceDayGenerateHandler {

    @Resource
    private CountryService countryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private ImileRedisClient imileRedisClient;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;
    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;

    public static final String JOB_KEY = "XXL-JOB:attendanceDayGenerateHandler:";

    public static Long EXPIRE_TIME = (long) 60 * 60 * 24;


    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_DAY_GENERATE_HANDLER)
    public ReturnT<String> attendanceDayGenerateHandler(String param) {

        AttendanceGenerateHandlerParam handlerParam = StringUtils.isNotBlank(param) ?
                JSON.parseObject(param, AttendanceGenerateHandlerParam.class) :
                new AttendanceGenerateHandlerParam();
        String countryList = handlerParam.getCountryList();
        if (StringUtils.isBlank(countryList)) {
            XxlJobLogger.log("国家不存在,不处理");
            return ReturnT.FAIL;
        }
        // 处理未打卡人员每日考勤
        List<String> countries = Arrays.asList(countryList.split(BusinessConstant.DEFAULT_DELIMITER));
        Map<String, CountryDTO> countryDTOMap = countryService.queryCountryList(countries)
                .stream()
                .collect(Collectors.toMap(CountryDTO::getCountryName, Function.identity(), (oldValue, newValue) -> oldValue));

        // 获取当前时间
        Date currentDate = new Date();

        for (String country : countries) {
            Long attendanceDayId = handlerParam.getAttendanceDayId();
            CountryDTO countryDTO = countryDTOMap.get(country);
            if (Objects.isNull(countryDTO)) {
                XxlJobLogger.log("未获取到国家:{}", country);
                continue;
            }
            String timeZone = countryDTO.getTimeZone();
            XxlJobLogger.log("当前国家:{}，时区:{}", country, timeZone);

            // 设置当前时间的对应国家的时区
            DateAndTimeZoneDate countryDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(currentDate);
            log.info("当前国家:{}, 时间为:{}", country, DateHelper.formatYYYYMMDDHHMMSS(countryDateAndTimeZoneDate.getTimeZoneDate()));

            // 如果需要计算的考勤天为空，根据当前dateTime获取考勤天
            if (Objects.isNull(attendanceDayId) || attendanceDayId <= 0) {
                attendanceDayId = DateHelper.getDayId(countryDateAndTimeZoneDate.getTimeZoneDate());
                XxlJobLogger.log("当前国家:{} 考勤日:{}", country, attendanceDayId);
            }
            Long preDayId = DateHelper.getPreviousDayId(attendanceDayId);

            UserDaoQuery userQuery = UserDaoQuery.builder()
                    .locationCountry(country)
                    .employeeTypeList(CommonUserService.getCountryEmployeeTypes(country))
                    .isDriver(BusinessConstant.N)
                    .build();
            if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
                List<String> userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
                userQuery.setUserCodes(userCodeList);
            }
            int currentPage = 1;
            int pageSize = 5000;
            Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
            // 总记录数
            List<UserInfoDO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                log.info("country: {},pageUserInfoList size:{}，pageUserInfoList：{}", country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                generateAttendance(attendanceDayId, preDayId, pageUserInfoList, countryDateAndTimeZoneDate, country);
            }
            log.info("country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());

            while (currentPage < pageInfo.getPages()) {
                log.info("country：{},进入while循环", country);
                currentPage++;
                log.info("country：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    log.info("country：{},while循环：pageUserInfoList size:{}，pageUserInfoList：{}",
                            country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    generateAttendance(attendanceDayId, preDayId, pageUserInfoList, countryDateAndTimeZoneDate, country);
                }
                log.info("country：{},while循环：pageUserInfoHandle | currentPage:{},pageSize:{},total:{}", country, currentPage, pageSize, pageInfo.getTotal());
                log.info("country：{},currentPage {}，while循环结束", country, currentPage);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void generateAttendance(Long attendanceDayId,
                                    Long preDayId,
                                    List<UserInfoDO> userInfoDOList,
                                    DateAndTimeZoneDate countryDateAndTimeZoneDate,
                                    String country) {
        //过滤考勤范围内有效用工类型员工
        userInfoDOList = userInfoDOList
                .stream()
                .filter(item -> EmploymentTypeEnum.employeeTypeEffectiveDetect(item.getLocationCountry(), item.getEmployeeType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            XxlJobLogger.log("当前国家:{}，考勤日:{}，没有符合考勤范围的员工用工类型,不处理", country, attendanceDayId);
            return;
        }
        List<String> userCodes = userInfoDOList.stream()
                .map(UserInfoDO::getUserCode)
                .collect(Collectors.toList());
        List<Long> dayIds = Arrays.asList(preDayId, attendanceDayId);

        List<EmployeePunchRecordDO> userPunchRecords = employeePunchRecordManage.getUsersPunchRecordsInTimeRange(userCodes,
                dayIds.stream().map(String::valueOf).collect(Collectors.toList()));

        List<UserInfoDO> noClockUserInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userPunchRecords)) {
            //过滤出来未打卡的员工
            Map<String, List<EmployeePunchRecordDO>> userCodeRecordMap = userPunchRecords.stream()
                    .collect(Collectors.groupingBy(record -> record.getUserCode() + record.getDayId()));
            for (UserInfoDO userInfoDO : userInfoDOList) {
                List<EmployeePunchRecordDO> userDayPunchRecordList = userCodeRecordMap.get(userInfoDO.getUserCode() + attendanceDayId);
                if (CollectionUtils.isEmpty(userDayPunchRecordList)) {
                    noClockUserInfoList.add(userInfoDO);
                    continue;
                }
                List<EmployeePunchRecordDO> preReCord = userCodeRecordMap.get(userInfoDO.getUserCode() + preDayId);
                if (CollectionUtils.isEmpty(preReCord)) {
                    noClockUserInfoList.add(userInfoDO);
                }
            }
        } else {
            noClockUserInfoList = userInfoDOList;
        }
        if (CollectionUtils.isEmpty(noClockUserInfoList)) {
            XxlJobLogger.log("当前国家:{}，考勤日:{}，没有未打卡的员工，不处理", country, attendanceDayId);
            log.info("当前国家:{}，考勤日:{}，没有未打卡的员工，不处理", country, attendanceDayId);
            return;
        }

        // 需要处理的人员id
        List<Long> userIds = noClockUserInfoList.stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());
        // 批量获取员工对应的打卡规则
        Map<Long, PunchConfigDO> punchConfigDOMap = punchConfigManage.mapByUserIds(userIds, countryDateAndTimeZoneDate.getDate());

        // 批量获取员工的排班信息
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigManage.selectBatchUserRecord(userIds, dayIds);
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            XxlJobLogger.log("当前国家:{} 考勤日:{} 用户:{} 没有排班信息", country, attendanceDayId);
        }

        // 批量获取员工的班次配置
        List<Long> userPunchClassIdList = userShiftConfigDOList.stream()
                .filter(UserShiftConfigDO::havePunchClassId)
                .map(UserShiftConfigDO::getPunchClassConfigId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<PunchClassItemConfigDO>> classItemConfigMapByClassId = punchClassConfigManage.selectClassItemByClassIds(userPunchClassIdList)
                .stream()
                .collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        // 获取员工的排班和班次配置
        Map<Long, List<UserShiftConfigDO>> userShiftConfigDOMap = userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));
        // 获取员工的具体班次配置map<userId, List<PunchClassItemConfigDO>>
        Map<Long, List<PunchClassItemConfigDO>> userClassItemConfigMap = new HashMap<>();
        for (Map.Entry<Long, List<UserShiftConfigDO>> entry : userShiftConfigDOMap.entrySet()) {
            Long userId = entry.getKey();
            List<PunchClassItemConfigDO> userClassItemConfigDOS = entry.getValue().stream()
                    .filter(UserShiftConfigDO::havePunchClassId)
                    .map(UserShiftConfigDO::getPunchClassConfigId)
                    .distinct()
                    .flatMap(i -> classItemConfigMapByClassId.getOrDefault(i, Collections.emptyList()).stream())
                    .distinct()
                    .collect(Collectors.toList());
            userClassItemConfigMap.put(userId, userClassItemConfigDOS);
        }

        //查询满足下班时间条件的班次时段，固定/多班次 下班时间+弹性时间+3小时 及 自由排班 最晚下班时间-应出勤时长
        List<Long> itemConfigListOnTime_preDay = new ArrayList<>();
        List<Long> itemConfigListOnTime_attendanceDay = new ArrayList<>();

        for (Long userId : userIds) {
            PunchConfigDO userPunchConfigDO = punchConfigDOMap.get(userId);
            if (userPunchConfigDO == null) {
                XxlJobLogger.log("userCode:{} 考勤日:{} 没有打卡规则", userId, attendanceDayId);
                continue;
            }
            List<PunchClassItemConfigDO> userClassItemConfigDOS = userClassItemConfigMap.get(userId);
            if (CollectionUtils.isEmpty(userClassItemConfigDOS)) {
                XxlJobLogger.log("userCode:{} 考勤日:{} 没有班次配置", userId, attendanceDayId);
                continue;
            }
            if (PunchConfigTypeEnum.isFlexibleWork(userPunchConfigDO.getConfigType())) {
                Map<Long, List<PunchClassItemConfigDO>> classItemConfigMap = userClassItemConfigDOS.stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));
                //过滤得到班次单时段
                List<PunchClassItemConfigDO> singleItemConfigDOList = new ArrayList<>();
                for (Map.Entry<Long, List<PunchClassItemConfigDO>> entry : classItemConfigMap.entrySet()) {
                    List<PunchClassItemConfigDO> itemConfigDOList = entry.getValue();
                    if (itemConfigDOList.size() == 1) {
                        singleItemConfigDOList.addAll(itemConfigDOList);
                    }
                }
                if (CollectionUtils.isEmpty(singleItemConfigDOList)) {
                    XxlJobLogger.log("AttendanceDayGenerateHandler userId:{} 考勤日:{} 打卡规则跟班次时段不匹配", userId, attendanceDayId);
                    log.info("AttendanceDayGenerateHandler,用户：{} 打卡规则跟班次时段不匹配", userId);
                    continue;
                }
                userClassItemConfigDOS = singleItemConfigDOList;
            }

            if (PunchConfigTypeEnum.isFlexibleWorkTwice(userPunchConfigDO.getConfigType())) {
                for (PunchClassItemConfigDO itemConfigDO : userClassItemConfigDOS) {
                    for (Long dayId : dayIds) {
                        DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(dayId, itemConfigDO);
                        if (countryDateAndTimeZoneDate.getTimeZoneDate().compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0) {
                            // 判断当前时间是否大于等于最晚下班时间-应出勤时间
                            DateTime abnormalPunchOutTime = DateUtil.offsetHour(
                                    dayPunchTimeDTO.getDayPunchEndTime(),
                                    // 从打卡规则获取上下班打卡时间间隔
                                    userPunchConfigDO.getPunchTimeInterval().negate().intValue()
                            );
                            if (countryDateAndTimeZoneDate.getTimeZoneDate().compareTo(abnormalPunchOutTime) >= 0) {
                                if (preDayId.equals(dayId)) {
                                    itemConfigListOnTime_preDay.add(itemConfigDO.getPunchClassId());
                                    continue;
                                }
                                itemConfigListOnTime_attendanceDay.add(itemConfigDO.getPunchClassId());
                            }
                        }
                    }
                }
            }

            if (PunchConfigTypeEnum.isFixedWork(userPunchConfigDO.getConfigType())
                    || PunchConfigTypeEnum.isFlexibleWorkOnce(userPunchConfigDO.getConfigType())) {
                for (PunchClassItemConfigDO itemConfigDO : userClassItemConfigDOS) {
                    for (Long dayId : dayIds) {
                        DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), userClassItemConfigDOS);
                        if (Objects.isNull(dayPunchTimeDTO)) {
                            continue;
                        }
                        if (countryDateAndTimeZoneDate.getTimeZoneDate().compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0) {
                            BigDecimal elasticTime = itemConfigDO.getElasticTime();
                            if (Objects.isNull(elasticTime)) {
                                elasticTime = BigDecimal.valueOf(0);
                            }
                            //下班时间  默认和最晚下班时间同一天
                            Date punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(dayPunchTimeDTO.getDayPunchEndTime()), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
                            if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
                                punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(dayPunchTimeDTO.getDayPunchEndTime(), -1)), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
                            }
                            BigDecimal addMinutes = elasticTime.add(BigDecimal.valueOf(3)).multiply(new BigDecimal(60));
                            DateTime abnormalPunchOutTime = DateUtil.offsetMinute(punchOutTime, addMinutes.intValue());
                            // 判断当前时间是否大于等于下班时间+弹性时间+3小时
                            if (countryDateAndTimeZoneDate.getTimeZoneDate().compareTo(abnormalPunchOutTime) >= 0) {
                                if (preDayId.equals(dayId)) {
                                    itemConfigListOnTime_preDay.add(itemConfigDO.getPunchClassId());
                                    continue;
                                }
                                itemConfigListOnTime_attendanceDay.add(itemConfigDO.getPunchClassId());
                            }
                        }
                    }
                }
            }
        }

        String userCache = getUserCache(attendanceDayId);
        List<String> listUserCache = new ArrayList<>();
        if (StringUtils.isNotBlank(userCache)) {
            listUserCache = Arrays.asList(userCache.split(BusinessConstant.DEFAULT_DELIMITER));
            XxlJobLogger.log("缓存命中人员:{}", listUserCache);
        }
        // 未排班的人员
        List<UserInfoDO> noClassUserList = new ArrayList<>();
        // 排班为休息日的人员
        List<UserInfoDO> offClassUserList = new ArrayList<>();
        // 排班满足下班时间的人员
        List<UserInfoDO> userList_preDay = new ArrayList<>();
        List<UserInfoDO> userList = new ArrayList<>();

        // 获取员工的出勤信息记录,并根据用户天分组
        Map<Long, List<AttendanceEmployeeDetailDO>> attendanceEmployeeDetailMap = attendanceEmployeeDetailManage.selectByUserIdListAndDayIdList(userIds, dayIds)
                .stream()
                .collect(Collectors.groupingBy(item -> item.getUserId() + item.getDayId()));

        for (UserInfoDO noClockUserInfoDO : noClockUserInfoList) {
            List<UserShiftConfigDO> noClockUserShiftConfigList =
                    userShiftConfigDOMap.getOrDefault(noClockUserInfoDO.getId(), Collections.emptyList());
            if (CollectionUtils.isEmpty(noClockUserShiftConfigList)) {
                if (!listUserCache.contains(noClockUserInfoDO.getUserCode())) {
                    noClassUserList.add(noClockUserInfoDO);
                }
                XxlJobLogger.log("userCode:{} 考勤日:{} 没有排班信息", noClockUserInfoDO.getUserCode(), attendanceDayId);
                continue;
            }
            //查询为休息日或法假的班次
            List<UserShiftConfigDO> havePunchClassIdShiftConfigList = noClockUserShiftConfigList.stream()
                    .filter(UserShiftConfigDO::havePunchClassId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(havePunchClassIdShiftConfigList)) {
                if (!listUserCache.contains(noClockUserInfoDO.getUserCode())) {
                    offClassUserList.add(noClockUserInfoDO);
                }
                XxlJobLogger.log("userCode:{} 考勤日:{} 今天天为休息日", noClockUserInfoDO.getUserCode(), attendanceDayId);
                continue;
            }
            //查询班次是否在排班时间范围内存在
            for (UserShiftConfigDO userShiftConfigDO : havePunchClassIdShiftConfigList) {
                if (preDayId.equals(userShiftConfigDO.getDayId())) {
                    if (itemConfigListOnTime_preDay.contains(userShiftConfigDO.getPunchClassConfigId())
                            && CollectionUtils.isEmpty(attendanceEmployeeDetailMap.get(noClockUserInfoDO.getId() + preDayId))) {
                        userList_preDay.add(noClockUserInfoDO);
                    }
                }
                if (attendanceDayId.equals(userShiftConfigDO.getDayId())) {
                    if (itemConfigListOnTime_attendanceDay.contains(userShiftConfigDO.getPunchClassConfigId())
                            && CollectionUtils.isEmpty(attendanceEmployeeDetailMap.get(noClockUserInfoDO.getId() + attendanceDayId))) {
                        userList.add(noClockUserInfoDO);
                    }
                }
                XxlJobLogger.log("userCode:{} 考勤日:{} 存在排班 classId:{}",
                        noClockUserInfoDO.getUserCode(), attendanceDayId, userShiftConfigDO.getPunchClassConfigId());
            }

            //当前班次不在排班时间范围内,判断今天是否是休息日
            for (UserShiftConfigDO userShiftConfigDO : noClockUserShiftConfigList) {
                if (!userShiftConfigDO.havePunchClassId() && attendanceDayId.equals(userShiftConfigDO.getDayId())) {
                    if (!listUserCache.contains(noClockUserInfoDO.getUserCode())) {
                        offClassUserList.add(noClockUserInfoDO);
                    }
                    XxlJobLogger.log("userCode:{} 考勤日:{} 班次不在排班时间范围内，今天天为休息日", noClockUserInfoDO.getUserCode(), attendanceDayId);
                }
            }
        }

        // 将排班为休息日和未排班的人员加入到缓存中，避免下次重复执行
        noClassUserList.addAll(offClassUserList);

        // 实时计算异常（未排班和休息日）
        if (CollectionUtils.isNotEmpty(noClassUserList)) {
            List<String> userCodeList = noClassUserList.stream()
                    .map(UserInfoDO::getUserCode)
                    .distinct()
                    .collect(Collectors.toList());
            String userCacheCodes = StringUtils.join(userCodeList, BusinessConstant.DEFAULT_DELIMITER);
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO.builder()
                    .attendanceDayId(attendanceDayId)
                    .userCodes(userCacheCodes)
                    .build();
            XxlJobLogger.log("userCode:{} 考勤日:{} 实时计算未排班和休息日异常", userCacheCodes, attendanceDayId);
            attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);
            //设置缓存
            setUserCache(userCacheCodes, attendanceDayId);
        }

        // 实时计算异常（正常排班，preDay）
        calcAttendance(userList_preDay, preDayId);

        // 实时计算异常（正常排班，attendanceDay）
        calcAttendance(userList, attendanceDayId);

        //仓内考勤计算
        warehouseAttendanceHandlerService.warehouseAttendanceNoPunchHandler(noClassUserList, offClassUserList, userList_preDay, userList, preDayId, attendanceDayId);
        XxlJobLogger.log("noClassUserList:{}, userList_preDay:{}, userList:{}", noClassUserList, userList_preDay, userList);

    }

    private void calcAttendance(List<UserInfoDO> userList, Long attendanceDayId) {
        if (CollectionUtils.isNotEmpty(userList)) {
            List<String> userCodeList = userList.stream()
                    .map(UserInfoDO::getUserCode)
                    .collect(Collectors.toList());
            String userCodes = StringUtils.join(userCodeList, BusinessConstant.DEFAULT_DELIMITER);
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO.builder()
                    .attendanceDayId(attendanceDayId)
                    .userCodes(userCodes)
                    .build();
            XxlJobLogger.log("userCode:{} 考勤日:{} 进行实时考勤计算", userCodes, attendanceDayId);
            attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);
        }
    }


    private String getUserCache(Long attendanceId) {
        String result = null;
        String jobCacheKey = JOB_KEY + attendanceId;
        // 缓存, 1天
        Object cache = imileRedisClient.get(jobCacheKey);
        if (cache != null) {
            result = String.valueOf(cache);
        }
        return result;
    }

    private void setUserCache(String userCodes, Long attendanceId) {
        String jobCacheKey = JOB_KEY + attendanceId;
        // 缓存, 1天
        imileRedisClient.set(jobCacheKey, userCodes, EXPIRE_TIME);
    }

}
