package com.imile.attendance.clock.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.clock.param.AttendanceAcrossNightAbnormalParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/8/6
 * @Description 多班次跨夜打卡人员异常计算(兜底适配打卡但未计算异常场景)
 */
@Slf4j
@Component
public class AttendanceAcrossNightAbnormalHandler {

    @Resource
    private CountryService countryService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;


    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_ACROSS_NIGHT_ABNORMAL_HANDLER)
    public ReturnT<String> attendanceAcrossNightAbnormalHandler(String param) {

        AttendanceAcrossNightAbnormalParam handlerParam = StringUtils.isNotBlank(param) ?
                JSON.parseObject(param, AttendanceAcrossNightAbnormalParam.class) :
                new AttendanceAcrossNightAbnormalParam();
        String countryList = handlerParam.getCountryList();
        if (StringUtils.isBlank(countryList)) {
            XxlJobLogger.log("国家不存在,不处理");
            return ReturnT.SUCCESS;
        }
        // 国家下多班次跨夜打卡人员异常计算
        List<String> countries = Arrays.asList(countryList.split(BusinessConstant.DEFAULT_DELIMITER));
        Map<String, CountryDTO> countryDTOMap = countryService.queryCountryList(countries)
                .stream()
                .collect(Collectors.toMap(CountryDTO::getCountryName, Function.identity(), (oldValue, newValue) -> oldValue));

        Long attendanceDayId = handlerParam.getAttendanceDayId();
        Date currentDate = new Date();
        for (String country : countries) {
            CountryDTO countryDTO = countryDTOMap.get(country);
            if (Objects.isNull(countryDTO)) {
                XxlJobLogger.log("未获取到该国家时区配置:{}", country);
                continue;
            }
            // 设置当前时间的对应国家的时区
            DateAndTimeZoneDate countryDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(currentDate);
            Date timeZoneDate = countryDateAndTimeZoneDate.getTimeZoneDate();
            log.info("当前国家:{}, 时间为:{}", country, DateHelper.formatYYYYMMDDHHMMSS(timeZoneDate));

            if (Objects.isNull(attendanceDayId)) {
                // 获取前一天时间
                DateTime preDate = DateUtil.offsetDay(timeZoneDate, -1);
                attendanceDayId = DateHelper.getDayId(preDate);
            }
            XxlJobLogger.log("当前国家:{}, 考勤日:{}", country, attendanceDayId);

            // 查询当前国家下对应的多班次配置
            List<PunchClassConfigDTO> mutiClassConfigList = punchClassConfigManage.selectByCountries(country, ClassNatureEnum.MULTIPLE_CLASS.getCode());
            if (CollectionUtils.isEmpty(mutiClassConfigList)) {
                XxlJobLogger.log("当前国家:{}, 不存在多班次信息", country);
                continue;
            }
            // 判断当前日期是否超过该班次的最晚打卡时间
            mutiClassConfigList = this.filterClassConfig(mutiClassConfigList, timeZoneDate);
            if (CollectionUtils.isEmpty(mutiClassConfigList)) {
                XxlJobLogger.log("当前国家:{}, 当前日期:{}, 已经超过班次最晚下班时间，不重新计算异常", country, timeZoneDate);
                continue;
            }
            List<Long> mutiClassIds = mutiClassConfigList
                    .stream()
                    .map(PunchClassConfigDTO::getId)
                    .collect(Collectors.toList());
            // 查询考勤日排当前班次的人员
            List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByClassIdAndDayId(mutiClassIds, attendanceDayId);
            if (CollectionUtils.isEmpty(userShiftConfigList)) {
                XxlJobLogger.log("当前国家:{}, 为查询到考勤日排多班次的人员", country);
                continue;
            }
            // 分组1000查询用户
            List<Long> userIds = userShiftConfigList
                    .stream()
                    .map(UserShiftConfigDO::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            List<List<Long>> partition = Lists.partition(userIds, 1000);
            XxlJobLogger.log("当前国家:{}, 人员分组数量:{},", country, partition.size());
            // 需要计算异常的人员
            List<String> userCodeNeedCalcList = Lists.newArrayList();
            for (int i = 0; i < partition.size(); i++) {
                List<Long> userIdPartition = partition.get(i);
                List<UserInfoDO> userInfoList = userInfoDao.getByUserIds(userIdPartition);
                if (CollectionUtils.isEmpty(userInfoList)) {
                    XxlJobLogger.log("分组序号:{}, 当前国家:{}, 未查询到对应人员", i, country);
                    continue;
                }
                if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
                    List<String> userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
                    userInfoList = userInfoList
                            .stream()
                            .filter(item -> userCodeList.contains(item.getUserCode()))
                            .collect(Collectors.toList());
                }
                List<String> userCodes = userInfoList
                        .stream()
                        .filter(item -> StringUtils.isNotBlank(item.getUserCode()))
                        .map(UserInfoDO::getUserCode)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userCodes)) {
                    XxlJobLogger.log("分组序号:{}, 当前国家:{}, 未查询到有效人员编码", i, country);
                    continue;
                }
                // 查询用户对应考勤日的打卡记录
                List<EmployeePunchRecordDO> usersPunchRecords = employeePunchRecordManage.getUsersPunchRecordsInTimeRange(userCodes
                        , Arrays.asList(String.valueOf(attendanceDayId)));
                if (CollectionUtils.isEmpty(usersPunchRecords)) {
                    XxlJobLogger.log("分组序号:{}, 当前国家:{}, 未查询到该日人员打卡记录", i, country);
                    continue;
                }
                Map<String, List<EmployeePunchRecordDO>> punchRecordMap = usersPunchRecords
                        .stream()
                        .collect(Collectors.groupingBy(EmployeePunchRecordDO::getUserCode));
                for (String userCode : userCodes) {
                    List<EmployeePunchRecordDO> punchRecordByUser = punchRecordMap.get(userCode);
                    if (CollectionUtils.isEmpty(punchRecordByUser)) {
                        XxlJobLogger.log("分组序号:{}, 当前国家:{}, 考勤日:{}, 该人员:{}, 在当前考勤日无打卡记录"
                                , i, country, attendanceDayId, userCode);
                        continue;
                    }
                    userCodeNeedCalcList.add(userCode);
                }
                // 计算考勤
                this.calcAttendance(userCodeNeedCalcList, attendanceDayId, i);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void calcAttendance(List<String> userCodeNeedCalcList, Long attendanceDayId, int i) {
        if (CollectionUtils.isNotEmpty(userCodeNeedCalcList)) {
            String userCodes = StringUtils.join(userCodeNeedCalcList, BusinessConstant.DEFAULT_DELIMITER);
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO.builder()
                    .attendanceDayId(attendanceDayId)
                    .userCodes(userCodes)
                    .build();
            XxlJobLogger.log("分组序号:{}, userCode:{} 考勤日:{} 进行实时考勤计算", i, userCodes, attendanceDayId);
            attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);
        }
    }

    private List<PunchClassConfigDTO> filterClassConfig(List<PunchClassConfigDTO> mutiClassConfigList,
                                                        Date timeZoneDate) {
        // 过滤掉当前时间超过最晚打卡时间的班次
        List<PunchClassConfigDTO> finalPunchClassConfigList = Lists.newArrayList();
        out:
        for (PunchClassConfigDTO punchClassConfigDTO : mutiClassConfigList) {
            List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();
            if (CollectionUtils.isEmpty(classItemConfigList)) {
                continue;
            }
            for (PunchClassItemConfigDTO punchClassItemConfigDTO : classItemConfigList) {
                Date latestPunchOutTime = punchClassItemConfigDTO.getLatestPunchOutTime();
                if (Objects.isNull(latestPunchOutTime)) {
                    continue;
                }
                LocalDateTime latestLocalDate = latestPunchOutTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                LocalDateTime timeZoneLocalDate = timeZoneDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                // 提取时分秒
                LocalTime localTime = latestLocalDate.toLocalTime();
                // 提取年月日
                LocalDate localDate = timeZoneLocalDate.toLocalDate();
                // 组合日期
                LocalDateTime combinedDateTime = LocalDateTime.of(localDate, localTime);
                if (timeZoneLocalDate.isBefore(combinedDateTime)) {
                    finalPunchClassConfigList.add(punchClassConfigDTO);
                    continue out;
                }

            }
        }
        return finalPunchClassConfigList;
    }

}
