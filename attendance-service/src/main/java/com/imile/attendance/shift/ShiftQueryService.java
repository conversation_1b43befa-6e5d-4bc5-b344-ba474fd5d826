package com.imile.attendance.shift;

import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserDayShiftRuleDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.warehouse.WarehouseUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18
 * @Description
 */
@Slf4j
@Service
public class ShiftQueryService {

    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private WarehouseUserService warehouseUserService;

    /**
     * 排班限制
     * 目前墨西哥仓内劳务派遣员工不允许页面排班
     *
     * @param attendanceUser 用户
     */
    public boolean judgeSchedulingLimit(AttendanceUser attendanceUser) {
        return warehouseUserService.isWarehouseLaborSupport(attendanceUser) || attendanceUser.areDimission();
    }


    /**
     * 根据班次分类获取关联的班次
     */
    public List<PunchClassConfigSelectDTO> queryClassSelects(String classNature) {
        if (StringUtils.isBlank(classNature)) {
            return Collections.emptyList();
        }
        List<PunchClassConfigSelectDTO> punchClassConfigSelectDTOS = punchClassConfigQueryService.selectLatestByClassNature(classNature);
        if (CollectionUtils.isEmpty(punchClassConfigSelectDTOS)) {
            return Collections.emptyList();
        }
        return punchClassConfigSelectDTOS;
    }

    /**
     * 获取批量排班员工的排班规则【班次+OFF,没有H(多个用户在同一天不一定都是节假日)】
     */
    public List<UserDayShiftRuleDTO> queryBatchShiftUserShiftRule(List<Long> userList) {
        log.info("获取同一批员工当天的排班规则,userList:{}", userList);
        List<UserDayShiftRuleDTO> userDayShiftRuleDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(userList)) {
            return userDayShiftRuleDTOList;
        }
        //无班次信息直接返回
        List<UserClassConfigDTO> userClassConfigDTOS =
                punchClassConfigQueryService.selectUserClassConfigList(userList);
        if (CollectionUtils.isEmpty(userClassConfigDTOS)) {
            return userDayShiftRuleDTOList;
        }
        // 获取第一个用户配置信息
        UserClassConfigDTO firstUserConfig = userClassConfigDTOS.get(0);
        String firstClassNature = firstUserConfig.getClassNature();
        List<PunchClassConfigSelectDTO> firstUserClassList = firstUserConfig.getClassConfigSelectList();

        // 如果第一个用户没有关联班次，直接返回空结果
        if (CollectionUtils.isEmpty(firstUserClassList)) {
            return userDayShiftRuleDTOList;
        }

        // 校验所有用户班次性质是否一致
        boolean allSameClassNature = userClassConfigDTOS.stream()
                .allMatch(config -> StringUtils.equals(config.getClassNature(), firstClassNature));
        if (!allSameClassNature) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "批量查询的用户班次性质不一致");
        }

        // 校验所有用户关联的班次是否一致

        // 将第一个用户的班次ID集合作为基准
        Set<Long> baseClassIds = firstUserClassList.stream()
                .map(PunchClassConfigSelectDTO::getId)
                .collect(Collectors.toSet());

        // 检查其他用户的班次是否与基准完全一致
        boolean allUsersHaveSameClasses = userClassConfigDTOS.stream()
                .skip(1) // 跳过第一个用户
                .allMatch(userConfig -> {
                    Set<Long> currentUserClassIds = userConfig.getClassConfigSelectList().stream()
                            .map(PunchClassConfigSelectDTO::getId)
                            .collect(Collectors.toSet());
                    return baseClassIds.equals(currentUserClassIds);
                });

        if (!allUsersHaveSameClasses) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "批量查询的用户关联的班次不一致");
        }

        // 获取班次性质枚举
        ClassNatureEnum classNatureEnum = ClassNatureEnum.getByCode(firstClassNature);
        if (classNatureEnum == null) {
            log.error("班次性质:{}不存在", firstClassNature);
            return userDayShiftRuleDTOList;
        }

        // 3. 根据班次性质处理排班规则
        switch (classNatureEnum) {
            case FIXED_CLASS:
                // 固定班次：取第一个关联的班次
                PunchClassConfigSelectDTO firstClass = firstUserClassList.get(0);
                userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildClass(
                        firstClass.getId(),
                        firstClass.getClassName()
                ));
                break;
            case MULTIPLE_CLASS:
                // 多班次：添加所有关联的班次（因为已经确认所有用户班次一致，只需要用第一个用户的班次列表）
                for (PunchClassConfigSelectDTO classConfig : firstUserClassList) {
                    userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildClass(
                            classConfig.getId(),
                            classConfig.getClassName()
                    ));
                }
                break;
            default:
                break;
        }
        //添加休息日
        userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildOff());
        return userDayShiftRuleDTOList;
    }

    /**
     * 获取循环排班员工的排班规则【班次+OFF,没有H(多个用户在同一天不一定都是节假日)】
     */
    public List<UserDayShiftRuleDTO> queryCycleShiftUserShiftRuleParam(List<Long> userList, List<Long> classIdList) {
        if (CollectionUtils.isEmpty(classIdList)) {
            return queryBatchShiftUserShiftRule(userList);
        }
        List<UserDayShiftRuleDTO> userDayShiftRuleDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(userList)) {
            return userDayShiftRuleDTOList;
        }
        //无班次信息直接返回
        List<UserClassConfigDTO> userClassConfigDTOList =
                punchClassConfigQueryService.selectUserClassConfigList(userList);
        if (CollectionUtils.isEmpty(userClassConfigDTOList)) {
            return userDayShiftRuleDTOList;
        }
        boolean userIsMatchFixedClass = userClassConfigDTOList.stream()
                .anyMatch(UserClassConfigDTO::checkUserIsFixedClass);
        if (userIsMatchFixedClass) {
            throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_MUST_BELONG_TO_MULTI_CLASS);
        }
        userClassConfigDTOList.forEach(userClassConfigDTO -> {
            if (!userClassConfigDTO.checkClassIdListIsContains(classIdList)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CYCLE_SHIFT_USER_CLASS_NOT_MATCH_SELECTED_CLASS);
            }
        });
        UserClassConfigDTO firstUserConfig = userClassConfigDTOList.get(0);
        //添加前端选择的班次作为班次规则
        List<PunchClassConfigSelectDTO> firstUserClassList = firstUserConfig.getClassConfigSelectList();
        firstUserClassList.stream()
                .filter(item -> classIdList.contains(item.getId()))
                .forEach(item ->
                        userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildClass(
                                item.getId(),
                                item.getClassName()
                        )));
        //添加休息日
        userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildOff());
        return userDayShiftRuleDTOList;
    }


    /**
     * 获取员工当天的排班规则
     */
    public List<UserDayShiftRuleDTO> queryUserDayShiftRule(Long userId, Long dayId) {
        log.info("获取员工当天的排班规则，userId:{},dayId:{}", userId, dayId);
        List<UserDayShiftRuleDTO> userDayShiftRuleDTOList = new ArrayList<>();
        if (userId == null || dayId == null) {
            return userDayShiftRuleDTOList;
        }
        AttendanceUser attendanceUser = attendanceUserService.getByUserId(userId);
        if (attendanceUser == null) {
            log.error("用户:{}不存在", userId);
            return userDayShiftRuleDTOList;
        }
        String classNature = attendanceUser.getClassNature();
        if (StringUtils.isBlank(classNature)) {
            log.error("用户:{}的班次性质为空,无法获取当天的排班规则", userId);
            return userDayShiftRuleDTOList;
        }
        ClassNatureEnum classNatureEnum = ClassNatureEnum.getByCode(classNature);
        if (classNatureEnum == null) {
            log.error("用户:{}的班次性质:{}不存在", userId, classNature);
            return userDayShiftRuleDTOList;
        }
        List<UserClassConfigDTO> userClassConfigDTOS =
                punchClassConfigQueryService.selectUserClassConfigList(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(userClassConfigDTOS)) {
            return userDayShiftRuleDTOList;
        }
        UserClassConfigDTO userClassConfigDTO = userClassConfigDTOS.get(0);
        if (!StringUtils.equals(userClassConfigDTO.getClassNature(), classNature)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "用户:" + userId + "的班次性质:" + classNature + "与查询的班次性质:" + userClassConfigDTO.getClassNature() + "不一致");
        }
        List<PunchClassConfigSelectDTO> classConfigSelectList = userClassConfigDTO.getClassConfigSelectList();
        switch (classNatureEnum) {
            case FIXED_CLASS:
                //获取员工关联的固定班次（最高优先级）
                if (CollectionUtils.isNotEmpty(classConfigSelectList)) {
                    PunchClassConfigSelectDTO punchClassConfigSelectDTO = classConfigSelectList.get(0);
                    userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildClass(
                            punchClassConfigSelectDTO.getId(),
                            punchClassConfigSelectDTO.getClassName()
                    ));
                }
                break;
            case MULTIPLE_CLASS:
                //获取员工关联的多班次
                if (CollectionUtils.isNotEmpty(classConfigSelectList)) {
                    for (PunchClassConfigSelectDTO punchClassConfigSelectDTO : classConfigSelectList) {
                        userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildClass(
                                punchClassConfigSelectDTO.getId(),
                                punchClassConfigSelectDTO.getClassName()
                        ));
                    }
                }
                break;
            default:
                break;
        }
        //判断员工关联的日历的dayId是否为节假日
        CalendarConfigDetailDO calendarConfigDetailDO = userCalendarService.getUserCalendarDayDetail(userId, dayId);
        if (null != calendarConfigDetailDO && calendarConfigDetailDO.areHOLIDAY()) {
            userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildHoliday());
        }
        //添加休息日
        userDayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildOff());
        return userDayShiftRuleDTOList;
    }
}
