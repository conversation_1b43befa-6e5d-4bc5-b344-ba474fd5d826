package com.imile.attendance.third.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.punch.PunchTypeEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.EmployeePunchRecordAdapter;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.third.ThirdZktecoService;
import com.imile.attendance.third.dto.ZKTecoAttendanceDTO;
import com.imile.attendance.third.utils.ZKTecoUtils;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.warehouse.WarehouseAttendanceCalculateService;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description 同步中控考勤打卡记录
 */
@Slf4j
@Component
public class SyncEmployeeAttendanceHandler {

    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private ZKTecoUtils zkTecoUtils;
    @Resource
    private ThirdZktecoService thirdZktecoService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private EmployeePunchRecordAdapter employeePunchRecordAdapter;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;
    @Resource
    private ThreadPoolTaskExecutor attendanceCalculateTaskThreadPool;
    @Resource
    private MigrationService migrationService;
    @Resource
    private WarehouseAttendanceCalculateService warehouseAttendanceCalculateService;

    @XxlJob(BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER)
    public ReturnT<String> syncEmployeeAttendanceHandler(String param) {
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, param);
        SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam handlerParam = StringUtils.isNotBlank(param) ?
                JSON.parseObject(param, SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam.class) :
                new SyncEmployeeAttendanceHandler.SyncEmployeeAttendanceHandlerParam();

        String token80 = zkTecoUtils.getToken(
                attendanceProperties.getZkteco().getUserNameVersion8(),
                attendanceProperties.getZkteco().getPasswordVersion8(),
                attendanceProperties.getZkteco().getSERVER_URL_VERSION_8()
        );
        String token85 = zkTecoUtils.getToken(
                attendanceProperties.getZkteco().getUserName(),
                attendanceProperties.getZkteco().getPassword(),
                attendanceProperties.getZkteco().getSERVER_URL()
        );

        List<String> userCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(handlerParam.getUserCodes())) {
            userCodeList = Arrays.asList(handlerParam.getUserCodes().split(","));
        }
        if (handlerParam.getStartDate() == null || handlerParam.getEndDate() == null) {
            handlerParam.setStartDate(DateHelper.pushDate(DateUtils.dayBegin(new Date()), -1));
            handlerParam.setEndDate(new Date());
        }
        List<ZKTecoAttendanceDTO> attendanceList = new ArrayList<>();
        // 获取本次拉取zkt的所有打卡记录
        List<EmployeePunchRecordDO> allPunchRecordDOList = new ArrayList<>();

        //查询用户信息
        List<AttendanceUser> userList = userService.listUsersByUserCodes(userCodeList);
        if (CollectionUtils.isNotEmpty(userList)) {
            XxlJobLogger.log("syncEmployeeAttendanceHandler specify user userCodeList:{}", JSON.toJSONString(userCodeList));
            log.info("syncEmployeeAttendanceHandler specify user userCodeList:{}", JSON.toJSONString(userCodeList));
            for (AttendanceUser attendanceUser : userList) {
                String attendances = "";
                if (thirdZktecoService.isCountryVersion8(attendanceUser.getLocationCountry())) {
                    //走8.0版本
                    attendances = zkTecoUtils.listAttendances(token80, handlerParam.getStartDate(),
                            handlerParam.getEndDate(), null, null, handlerParam.getTerminalSn(),
                            attendanceUser.getUserCode(), attendanceProperties.getZkteco().getSERVER_URL_VERSION_8());
                } else {
                    attendances = zkTecoUtils.listAttendances(token85, handlerParam.getStartDate(),
                            handlerParam.getEndDate(), null, null, handlerParam.getTerminalSn(),
                            attendanceUser.getUserCode(), attendanceProperties.getZkteco().getSERVER_URL());
                }
                JSONObject attendancesJson = JSONObject.parseObject(attendances);
                if (Objects.isNull(attendancesJson)) {
                    continue;
                }
                attendanceList = JSONObject.parseArray(attendancesJson.get("data").toString(), ZKTecoAttendanceDTO.class);
                punchCardHandler(attendanceList, allPunchRecordDOList);
            }
            // 处理拉取打卡记录信息，并计算考勤
            handlerPunchRecordCalculateAttendance(allPunchRecordDOList);
            return ReturnT.SUCCESS;
        }

        int index = 1;
        int pageSize = 1000;

        String attendances80 = zkTecoUtils.listAttendances(token80, handlerParam.getStartDate(), handlerParam.getEndDate(),
                index, pageSize, handlerParam.getTerminalSn(), null, attendanceProperties.getZkteco().getSERVER_URL_VERSION_8());
        String attendances85 = zkTecoUtils.listAttendances(token85, handlerParam.getStartDate(), handlerParam.getEndDate(),
                index, pageSize, handlerParam.getTerminalSn(), null, attendanceProperties.getZkteco().getSERVER_URL());

        if (StringUtils.isNotBlank(attendances80)) {
            allPunchCardHandler(token80, attendanceProperties.getZkteco().getSERVER_URL_VERSION_8(),
                    attendances80, handlerParam, allPunchRecordDOList);
        }
        if (StringUtils.isNotBlank(attendances85)) {
            allPunchCardHandler(token85, attendanceProperties.getZkteco().getSERVER_URL(),
                    attendances85, handlerParam, allPunchRecordDOList);
        }

        // 处理拉取打卡记录信息，并计算考勤
        handlerPunchRecordCalculateAttendance(allPunchRecordDOList);

        return ReturnT.SUCCESS;
    }

    private void allPunchCardHandler(String token,
                                     String serverUrl,
                                     String attendances,
                                     SyncEmployeeAttendanceHandlerParam handlerParam,
                                     List<EmployeePunchRecordDO> allPunchRecordDOList) {
        int count;
        int index;
        int pageSize = 1000;

        JSONObject attendancesJson = JSONObject.parseObject(attendances);
        count = (int) attendancesJson.get("count");
        List<ZKTecoAttendanceDTO> attendanceList = JSONObject.parseArray(attendancesJson.get("data").toString(), ZKTecoAttendanceDTO.class);
        punchCardHandler(attendanceList, allPunchRecordDOList);

        if (count <= pageSize) {
            return;
        }

        index = count / pageSize;
        if (count % pageSize > 0) {
            index += 1;
        }

        for (int i = 1; i <= index; i++) {
            String attendancesForEach = zkTecoUtils.listAttendances(token, handlerParam.getStartDate(), handlerParam.getEndDate(),
                    i + 1, pageSize, handlerParam.getTerminalSn(), null, serverUrl);
            JSONObject attendancesJsonForEach = JSONObject.parseObject(attendancesForEach);
            List<ZKTecoAttendanceDTO> attendanceListForEach = JSONObject.parseArray(attendancesJsonForEach.get("data").toString(), ZKTecoAttendanceDTO.class);
            punchCardHandler(attendanceListForEach, allPunchRecordDOList);
        }
    }

    /**
     * 根据每一个小时拉过来的打卡记录，实时计算这些人的考勤
     *
     * @param allPunchRecordDOList 打卡记录
     */
    private void handlerPunchRecordCalculateAttendance(List<EmployeePunchRecordDO> allPunchRecordDOList) {
        if (CollectionUtils.isEmpty(allPunchRecordDOList)) {
            log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList is empty，无需计算考勤");
            return;
        }
        log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList size:{}", allPunchRecordDOList.size());
        log.info("handlerPunchRecordCalculateAttendance allPunchRecordDOList:{}", JSON.toJSONString(allPunchRecordDOList));
        // 过滤重复数据
        allPunchRecordDOList = allPunchRecordDOList
                .stream()
                .collect(Collectors.toMap(item -> Arrays.asList(item.getDayId(), item.getRelationId(), item.getPunchTime())
                        , Function.identity(), (existing, replacement) -> existing, LinkedHashMap::new))
                .values()
                .stream()
                .collect(Collectors.toList());

        // 获取用户code集合
        List<String> userCodeList = allPunchRecordDOList.stream()
                .map(EmployeePunchRecordDO::getUserCode)
                .distinct()
                .collect(Collectors.toList());
        // 获取dayId集合
        List<String> dayIdList = allPunchRecordDOList.stream()
                .map(EmployeePunchRecordDO::getDayId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userCodeList) || CollectionUtils.isEmpty(dayIdList)) {
            log.info("handlerPunchRecordCalculateAttendance userCodeList or dayIdList is empty");
            log.info("handlerPunchRecordCalculateAttendance userCodeList:{}", JSON.toJSONString(userCodeList));
            log.info("handlerPunchRecordCalculateAttendance dayIdList:{}", JSON.toJSONString(dayIdList));
            return;
        }
        log.info("handlerPunchRecordCalculateAttendance zkt sync userCodeList size:{}", userCodeList.size());
        log.info("handlerPunchRecordCalculateAttendance zkt sync userCodeList:{}", JSON.toJSONString(userCodeList));

        //查询员工是否在hr系统中
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(userCodeList);

        log.info("handlerPunchRecordCalculateAttendance hrms userInfoList size:{}", userInfoList.size());

        // 将userInfoList按照userCode转为map
        Map<String, AttendanceUser> userInfoMap = userInfoList.stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity(), (v1, v2) -> v1));

        // 获取员工id集合
        List<Long> userIdList = userInfoList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        // 获取员工常驻国集合
        List<String> locationCountryList = userInfoList.stream()
                .map(AttendanceUser::getLocationCountry)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(locationCountryList)) {
            log.info("handlerPunchRecordCalculateAttendance locationCountryList is empty");
            return;
        }
        List<CountryDTO> countryDTOList = countryService.queryCountryList(locationCountryList);
        Map<String, String> countryConfigMap = countryDTOList.stream()
                .collect(Collectors.toMap(CountryDTO::getCountryName, CountryDTO::getTimeZone));

        // 封装dayId集合，将dayIdList转为Long的dayIdList，并且加一天减一天
        List<Long> dayIdLongList = getDayIdLongList(dayIdList);
        log.info("handlerPunchRecordCalculateAttendance dayIdLongList:{}", JSON.toJSONString(dayIdLongList));
        // 通过userIdList + dayIdLongList 查询员工的正常考勤表记录
        List<AttendanceEmployeeDetailDO> attendanceEmployeeList =
                attendanceEmployeeDetailManage.selectByUserIdListAndDayIdList(userIdList, dayIdLongList);
        Map<Long, List<AttendanceEmployeeDetailDO>> userAttendanceMap = attendanceEmployeeList.stream()
                .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getUserId));

        // 通过userIdList + dayIdLongList 查询员工的排班信息，【如果userIdList为空，上面已经拦截了，下面这个就不会执行了】
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigManage.selectBatchUserRecord(userIdList, dayIdLongList);
        Map<Long, List<UserShiftConfigDO>> userClassConfigMap = userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        // 获取排班班次id
        List<Long> classIdList = userShiftConfigDOList.stream()
                .map(UserShiftConfigDO::getPunchClassConfigId)
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.toList());
        // 根据班次id获取排班班次配置信息
        List<PunchClassConfigDO> classConfigDOList = punchClassConfigManage.selectByClassIds(classIdList);
        // 将classConfig按照classId转为map
        Map<Long, PunchClassConfigDO> classConfigMap = classConfigDOList.stream()
                .collect(Collectors.toMap(PunchClassConfigDO::getId, Function.identity(), (v1, v2) -> v1));
        // 获取时段id
        List<PunchClassItemConfigDO> classItemConfig = punchClassConfigManage.selectClassItemByClassIds(classIdList);
        Map<Long, List<PunchClassItemConfigDO>> classItemMap = classItemConfig.stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));
        // 获取打卡规则
        List<Long> dayIds = dayIdLongList.stream().sorted().collect(Collectors.toList());
        List<UserDayConfigDTO> punchConfig = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dayIds)) {
            DateTime startDate = DateUtil.endOfDay(DateHelper.transferDayIdToDate(String.valueOf(dayIds.get(0))));
            DateTime endDate = DateUtil.endOfDay(DateHelper.transferDayIdToDate(String.valueOf(dayIds.get(dayIds.size() - 1))));
            punchConfig = punchConfigManage.selectDayConfigByUserIdList(userIdList, startDate, endDate);
        }

        // 将HrmsAttendancePunchConfigDO转为map
        Map<Long, List<UserDayConfigDTO>> punchConfigMap = punchConfig.stream().collect(Collectors.groupingBy(UserDayConfigDTO::getUserId));

        // 获取当前时间
        DateTime nowDate = DateUtil.date();
        // 处理打卡记录DayId
        this.transferPunchRecordDayIdAndSave(userInfoMap, userClassConfigMap,
                punchConfigMap, classItemMap, allPunchRecordDOList);

        // 需要计算考勤的数据
        List<AttendanceCalculateHandlerDTO> dayAttendanceHandlerList = Lists.newArrayList();
        // 1. 按照员工编号分组
        Map<String, List<EmployeePunchRecordDO>> userPunchRecordMap = allPunchRecordDOList.stream()
                .collect(Collectors.groupingBy(EmployeePunchRecordDO::getUserCode));
        // 2. 按照员工编号分组，计算考勤
        for (Map.Entry<String, List<EmployeePunchRecordDO>> entry : userPunchRecordMap.entrySet()) {
            // 用户code
            String userCode = entry.getKey();
            AttendanceUser attendanceUser = userInfoMap.get(userCode);
            if (ObjectUtil.isNull(attendanceUser)) {
                log.info("handlerPunchRecordCalculateAttendance attendanceUser is null，userCode：{}", userCode);
                continue;
            }
            log.info("handlerPunchRecordCalculateAttendance attendanceUser：{}", JSON.toJSONString(attendanceUser));
            String locationCountry = attendanceUser.getLocationCountry();
            if (ObjectUtil.equal(locationCountry, "")) {
                XxlJobLogger.log("userCode：{},常驻国为空", userCode);
                continue;
            }
            String timeZone = countryConfigMap.getOrDefault(locationCountry, "");
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("userCode：{},该国家:{},不存在国家时区", userCode, locationCountry);
                continue;
            }
            // 将当前时间，转换为国家本地时间
            Date localDateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, nowDate);
            Long localDayId = DateHelper.getDayId(localDateTime);
            int localYear = DateHelper.year(localDateTime);
            int localMonth = DateHelper.month(localDateTime);
            int localDay = DateHelper.dayOfMonth(localDateTime);
            // 校验当前时间dateTime是不是凌晨四点钟
            int localHour = DateUtil.hour(localDateTime, true);

            log.info("handlerPunchRecordCalculateAttendance userCode：{}，localYear：{}，localMonth：{}，localDay：{}，localHour：{},serverDate：{}",
                    userCode, localYear, localMonth, localDay, localHour, nowDate);

            // 获取该人员的正常考勤数据
            List<AttendanceEmployeeDetailDO> attendanceEmployeeDetail =
                    userAttendanceMap.getOrDefault(attendanceUser.getId(), Lists.newArrayList());
            // 将hrmsAttendanceEmployeeDetail按照dayId分组
            Map<Long, List<AttendanceEmployeeDetailDO>> userDayAttendanceMap = attendanceEmployeeDetail.stream()
                    .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getDayId));

            // 获取该人员的排班信息
            List<UserShiftConfigDO> userClassConfigDOS = userClassConfigMap.getOrDefault(attendanceUser.getId(), Lists.newArrayList());
            // 将hrmsAttendanceClassEmployeeConfigInfo按照dayId分组
            Map<Long, List<UserShiftConfigDO>> userDayShiftConfigMap = userClassConfigDOS.stream()
                    .collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));

            // 用户对应的本次拉取的所有打卡记录
            List<EmployeePunchRecordDO> employeePunchRecordDOList = entry.getValue();
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，value：{}", userCode, JSON.toJSONString(employeePunchRecordDOList));
            // 获取该人员的dayId集合
            List<String> dayIdListByUserCode = employeePunchRecordDOList.stream()
                    .map(EmployeePunchRecordDO::getDayId)
                    .distinct()
                    .collect(Collectors.toList());
            // 封装dayId数据，该dayIdByUserCodeLongList是该用户本次同步过来，需要遍历的dayId
            List<Long> dayIdByUserCodeLongList = getDayIdLongList(dayIdListByUserCode);
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，dayIdByUserCodeLongList：{}",
                    userCode, JSON.toJSONString(dayIdByUserCodeLongList));

            // 获取dayIdByUserCodeLongList的最大的dayId
            Long maxDayId = dayIdByUserCodeLongList.stream()
                    .max(Long::compareTo)
                    .orElse(0L);
            log.info("handlerPunchRecordCalculateAttendance userCode：{}，maxDayId：{}，localDayId：{}", userCode, maxDayId, localDayId);
            /*
                如果最大计算考勤日的时间 == 当地当前时间，则需要判断该用户当天是否存在打卡记录，不存在打卡记录，则不需要计算考勤，
                因为有可能人员在国外，但是人员的打卡规则是在国内，所以获取localDayId就==计算考勤日了
                比如人员在MEX（人员的打卡规则是CHN，常驻地是CHN），通过考勤机器打卡时间是 2024-07-15 09:09:27，中国时间2024-07-16 00:00:00 会去拉打卡记录，
                找到这个人，然后计算 20240714 20240715 20240716三天的考勤，所以20240716就等当前时间了，就不满足上面大于的条件，就计算考勤了，然后是上下班缺卡
             */
            // 如果最大计算考勤日的时间 == 当地当前时间，则需要判断该用户当天是否存在打卡记录，不存在打卡记录，则不需要计算考勤
            if (maxDayId.equals(localDayId)) {
                List<EmployeePunchRecordDO> punchList = employeePunchRecordDao.getPunchList(maxDayId, userCode, null);
                log.info("handlerPunchRecordCalculateAttendance maxDayId == localDay count，userCode：{}，maxDayId：{}，localDay：{}，punchList：{}",
                        userCode, maxDayId, localDayId, JSON.toJSONString(punchList));
                if (CollectionUtils.isEmpty(punchList)) {
                    log.info("handlerPunchRecordCalculateAttendance maxDayId == localDayId and employeePunchRecord is empty，userCode：{}，locationCountry：{}，maxDayId：{}，localDayId：{}",
                            userCode, locationCountry, maxDayId, localDayId);
                    // dayIdByUserCodeLongList去除掉最大的dayId
                    log.info("handlerPunchRecordCalculateAttendance dayIdByUserCodeLongList remove maxDayId，userCode：{}，locationCountry：{}，maxDayId：{}，localDayId：{}",
                            userCode, locationCountry, maxDayId, localDayId);
                    dayIdByUserCodeLongList.removeIf(element -> element.equals(maxDayId));
                }
            }
            // 遍历dayIdLongList，所有的dayId
            for (Long dayId : dayIdByUserCodeLongList) {

                // 如果需要计算考勤日的时间大于当前时间，则不进行考勤计算，只有小于等于当天的才计算
                if (dayId > localDayId) {
                    log.info("handlerPunchRecordCalculateAttendance dayId > localDayId，userCode：{}，locationCountry：{}，attendanceDayId：{}，localDayId：{}",
                            userCode, locationCountry, dayId, localDayId);
                    continue;
                }

                // 获取该员工的当天的正常考勤数据
                List<AttendanceEmployeeDetailDO> userDayAttendanceDetailList = userDayAttendanceMap.getOrDefault(dayId, Lists.newArrayList());

                // 如果attendanceEmployeeDetail不为空且存在考勤类型为P的数据，则不进行考勤计算,[这边只过滤了为P的数据，来判断是否不需要计算，这样会存在一天都请假或都外勤的情况也需要重新计算考勤，这多计算也没有问题。不能少计算了]
                //if (CollUtil.isNotEmpty(attendanceEmployeeDetail) && attendanceEmployeeDetail.stream().anyMatch(o -> StringUtils.equalsIgnoreCase(o.getConcreteType(), AttendanceConcreteTypeEnum.P.getCode()))) {
                //    log.info("handlerPunchRecordCalculateAttendance hrmsAttendanceEmployeeDetail is not null and attendanceType has P record，userCode：{}，hrmsUserInfo：{}，不进行考勤计算逻辑处理", userCode, JSON.toJSONString(hrmsUserInfo));
                //    continue;
                //}

                BigDecimal attendanceMinutes = BigDecimal.ZERO;
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //先看该用户当天正常考勤表的数据是不是正常，统计正常出勤时长以及请假时长
                for (AttendanceEmployeeDetailDO detailDO : userDayAttendanceDetailList) {
                    if (detailDO.getAttendanceMinutes() != null && detailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        attendanceMinutes = attendanceMinutes.add(detailDO.getAttendanceMinutes());
                    }
                    if (detailDO.getLeaveMinutes() != null && detailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                        leaveMinutes = leaveMinutes.add(detailDO.getLeaveMinutes());
                    }
                }
                log.info("handlerPunchRecordCalculateAttendance userCode：{}，dayId：{}, attendanceMinutes：{}，leaveMinutes：{}",
                        userCode, dayId, attendanceMinutes, leaveMinutes);

                // 获取当天排班信息
                List<UserShiftConfigDO> userDayShiftConfigList = userDayShiftConfigMap.getOrDefault(dayId, Lists.newArrayList());

                if (CollectionUtils.isNotEmpty(userDayShiftConfigList)) {
                    if (userDayShiftConfigList.size() > 1) {
                        log.info("handlerPunchRecordCalculateAttendance userDayShiftConfigList size > 1，userCode：{}，dayId：{}, hrmsAttendanceClassEmployeeConfigInfoList：{}",
                                userCode, dayId, JSON.toJSONString(userDayShiftConfigList));
                        continue;
                    }
                    UserShiftConfigDO userClassConfigDO = userDayShiftConfigList.get(0);
                    if (userClassConfigDO.havePunchClassId()) {
                        Long punchClassConfigId = userClassConfigDO.getPunchClassConfigId();
                        log.info("handlerPunchRecordCalculateAttendance classId is not null，userCode：{}，hrmsUserInfo：{}，classId：{}",
                                userCode, JSON.toJSONString(attendanceUser), punchClassConfigId);
                        // 获取班次信息
                        PunchClassConfigDO punchClassConfig = classConfigMap.get(punchClassConfigId);
                        if (ObjectUtil.isNotNull(punchClassConfig)) {
                            // 获取班次信息
                            BigDecimal legalWorkingHours = punchClassConfig.getLegalWorkingHours();
                            if (ObjectUtil.isNotNull(legalWorkingHours)) {
                                BigDecimal totalMinutes = legalWorkingHours.multiply(BusinessConstant.MINUTES);
                                log.info("handlerPunchRecordCalculateAttendance legalWorkingHours：{}，totalMinutes：{}",
                                        legalWorkingHours, totalMinutes);
                                // 判断该人员考勤正常表数据，该天的考勤时长是否已经达到了规定的工作时长，已经达到了则不进行考勤计算逻辑处理
                                if (attendanceMinutes.add(leaveMinutes).compareTo(totalMinutes) >= 0) {
                                    log.info("handlerPunchRecordCalculateAttendance attendanceMinutes.add(leaveMinutes).compareTo(totalMinutes) >= 0 不进行考勤计算逻辑处理，userCode：{}，hrmsUserInfo：{}，attendanceMinutes：{}，leaveMinutes：{}，totalMinutes：{}",
                                            userCode, JSON.toJSONString(attendanceUser), attendanceMinutes, leaveMinutes, totalMinutes);
                                    continue;
                                }
                            }
                        }
                    }
                }

                // 否则是需要计算考勤的，这边记录下来
                AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
                dayAttendanceHandlerDTO.setUserCodes(userCode);
                dayAttendanceHandlerDTO.setAttendanceDayId(dayId);
                dayAttendanceHandlerList.add(dayAttendanceHandlerDTO);
            }
        }

        if (CollectionUtils.isEmpty(dayAttendanceHandlerList)) {
            XxlJobLogger.log("{} 执行完毕,没有需要计算考勤的数据", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
            return;
        }
        // 将dayAttendanceHandlerList按照dayId分组，并将userCodes拼接成字符串，逗号分隔
        Map<Long, List<AttendanceCalculateHandlerDTO>> dayAttendanceHandlerMap = dayAttendanceHandlerList.stream()
                .collect(Collectors.groupingBy(AttendanceCalculateHandlerDTO::getAttendanceDayId));
        // 遍历dayAttendanceHandlerMap，将userCodes拼接成字符串，逗号分隔
        List<AttendanceCalculateHandlerDTO> targetDayAttendanceHandlerList = Lists.newArrayList();
        for (Map.Entry<Long, List<AttendanceCalculateHandlerDTO>> entry : dayAttendanceHandlerMap.entrySet()) {
            Long key = entry.getKey();
            List<AttendanceCalculateHandlerDTO> dayAttendanceHandlerDTOList = entry.getValue();
            String userCodes = dayAttendanceHandlerDTOList.stream()
                    .map(AttendanceCalculateHandlerDTO::getUserCodes)
                    .collect(Collectors.joining(","));
            AttendanceCalculateHandlerDTO targetDayAttendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
            targetDayAttendanceHandlerDTO.setUserCodes(userCodes);
            targetDayAttendanceHandlerDTO.setAttendanceDayId(key);
            targetDayAttendanceHandlerList.add(targetDayAttendanceHandlerDTO);
        }

        log.info("handlerPunchRecordCalculateAttendance targetDayAttendanceHandlerList size:{}", targetDayAttendanceHandlerList.size());
        log.info("handlerPunchRecordCalculateAttendance targetDayAttendanceHandlerList:{}", JSON.toJSONString(targetDayAttendanceHandlerList));

        long startTime = System.currentTimeMillis();
        XxlJobLogger.log("XXL-JOB, {} " + "同步打卡记录，计算考勤" + "--->开始",
                BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
        log.info("XXL-JOB, {} " + "同步打卡记录，计算考勤" + "--->开始",
                BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);

        AtomicInteger successCount = new AtomicInteger();
        List<AttendanceCalculateHandlerDTO> failAttendanceHandlerList = Lists.newArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(targetDayAttendanceHandlerList.size());
        // 3. 调用考勤计算接口
        targetDayAttendanceHandlerList.forEach(targetDayAttendanceHandlerDTO -> {
            attendanceCalculateTaskThreadPool.execute(() -> {
                try {
                    // 计算考勤
                    attendanceGenerateService.attendanceCalculateHandler(targetDayAttendanceHandlerDTO);

                    //仓内计算考勤 当前存在自有员工混打场景
                    AttendanceCalculateHandlerDTO attendanceHandlerDTO = AttendanceCalculateHandlerDTO
                            .builder()
                            .attendanceDayId(targetDayAttendanceHandlerDTO.getAttendanceDayId())
                            .userCodes(targetDayAttendanceHandlerDTO.getUserCodes())
                            .build();
                    warehouseAttendanceCalculateService.warehouseAttendanceCalculate(attendanceHandlerDTO);

                    successCount.getAndIncrement();
                } catch (Exception e) {
                    failAttendanceHandlerList.add(targetDayAttendanceHandlerDTO);
                    XxlJobLogger.log("{} 同步打卡记录，计算考勤任务失败,targetDayAttendanceHandlerDTO:{},message:{}",
                            BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, JSON.toJSONString(targetDayAttendanceHandlerDTO), e.getMessage());
                    log.info("{} 同步打卡记录，计算考勤失败，targetDayAttendanceHandlerDTO:{},异常信息为:{}",
                            BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER, JSON.toJSONString(targetDayAttendanceHandlerDTO), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            XxlJobLogger.log("{} CountDownLatch wait InterruptedException", BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER);
            Thread.currentThread().interrupt();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        XxlJobLogger.log("XXL-JOB, {} , " + "同步打卡记录，计算考勤" + "--->结束，耗时 {} 毫秒，其中成功{}个,失败{}个,失败的信息为:{}",
                BusinessConstant.JobHandler.ABNORMAL_ATTENDANCE_REMINDER_HANDLER, duration, successCount.get(), failAttendanceHandlerList.size(), failAttendanceHandlerList);
    }

    private void punchCardHandler(List<ZKTecoAttendanceDTO> attendanceList,
                                  List<EmployeePunchRecordDO> allPunchRecordDOList) {
        if (CollectionUtils.isEmpty(attendanceList)) {
            return;
        }
        log.info("punchCardHandler attendanceList size:{}", attendanceList.size());
        List<String> userCodes = attendanceList.stream()
                .map(ZKTecoAttendanceDTO::getEmp_code)
                .collect(Collectors.toList());
        //查询员工是否在hr系统中
        List<AttendanceUser> userInfoDOList = userService.listUsersByUserCodes(userCodes);
        // 查询在灰度名单的人员
        Map<Long, Boolean> userMigrationMap;
        try {
            userMigrationMap = migrationService.verifyUsersIsEnableNewAttendance(userInfoDOList.stream()
                    .map(AttendanceUser::getId)
                    .distinct()
                    .collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("批量验证用户迁移状态异常", e);
            XxlJobLogger.log("迁移验证异常，跳过处理: {}", e.getMessage());
            return;
        }

        // 统计启用新系统的用户
        List<Long> enableNewAttendanceUserIdList = userMigrationMap.entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        int enableNewAttendanceUserSize = enableNewAttendanceUserIdList.size();
        if (enableNewAttendanceUserSize == 0) {
            XxlJobLogger.log("没有启用新考勤系统的用户，跳过处理");
            return;
        }

        // 过滤启用新系统的用户的异常考勤
        userInfoDOList = userInfoDOList.stream()
                .filter(i -> enableNewAttendanceUserIdList.contains(i.getId()))
                .collect(Collectors.toList());
        Map<String, AttendanceUser> userMap = userInfoDOList.stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity(), (v1, v2) -> v1));

        List<String> dayIdStr = attendanceList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getPunch_time()))
                .map(ZKTecoAttendanceDTO::getPunch_time)
                .map(item -> DateUtils.str2Date(item, DatePattern.NORM_DATETIME_PATTERN))
                .flatMap(item -> {
                    DateTime prevDay = DateUtil.offsetDay(item, -1);
                    DateTime nextDay = DateUtil.offsetDay(item, 1);
                    return Stream.of(prevDay, item, nextDay)
                            .map(date ->  DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
                })
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, List<EmployeePunchRecordDO>> userPunchRecordMap =
                employeePunchRecordManage.listBySourceTypeAndDayId(SourceTypeEnum.ZKTECO.name(), dayIdStr)
                        .stream()
                        .collect(Collectors.groupingBy(EmployeePunchRecordDO::getRelationId));

        Map<String, List<ZKTecoAttendanceDTO>> userPunchCardGroup = attendanceList.stream()
                .collect(Collectors.groupingBy(ZKTecoAttendanceDTO::getEmp_code));

        List<EmployeePunchRecordDO> punchRecordDOList = new ArrayList<>();

        for (Map.Entry<String, List<ZKTecoAttendanceDTO>> entry : userPunchCardGroup.entrySet()) {
            AttendanceUser attendanceUser = userMap.get(entry.getKey());
            if (attendanceUser == null) {
                continue;
            }
            for (ZKTecoAttendanceDTO attendanceDTO : entry.getValue()) {
                String punchType = attendanceDTO.getPunch_state() == 1 ?
                        PunchTypeEnum.ON_DUTY.getCode() : PunchTypeEnum.OUT_DUTY.getCode();
                Date punchTime = DateHelper.parseYYYYMMDDHHMMSS(attendanceDTO.getPunch_time());
                String dayId = DateUtil.format(punchTime, DatePattern.PURE_DATE_PATTERN);

                List<EmployeePunchRecordDO> punchRecords = userPunchRecordMap.get(attendanceDTO.getId());
                if (CollectionUtils.isNotEmpty(punchRecords)) {
                    List<EmployeePunchRecordDO> dayPunchRecords = punchRecords.stream()
                            .filter(o -> dayIdStr.contains(o.getDayId()))
                            .collect(Collectors.toList());
                    boolean has = dayPunchRecords.stream()
                            .anyMatch(o -> o.getPunchTime().compareTo(punchTime) == 0);
                    if (has) {
                        log.info("punchCardHandler punchRecord has exist,attendanceDTO:{}", JSON.toJSONString(attendanceDTO));
                        continue;
                    }
                }

                EmployeePunchRecordDO punchRecordDO = new EmployeePunchRecordDO();
                punchRecordDO.setId(defaultIdWorker.nextId());
                punchRecordDO.setUserCode(attendanceUser.getUserCode());
                punchRecordDO.setCountry(attendanceUser.getLocationCountry());
                punchRecordDO.setDeptId(attendanceUser.getDeptId());
                punchRecordDO.setRelationId(attendanceDTO.getId());
                punchRecordDO.setPunchCardType(attendanceDTO.getVerify_type_display());
                punchRecordDO.setPunchArea(attendanceDTO.getArea_alias());
                punchRecordDO.setLatitude(BigDecimal.ZERO);
                punchRecordDO.setLongitude(BigDecimal.ZERO);
                punchRecordDO.setPunchTime(punchTime);
                punchRecordDO.setDayId(dayId);
                punchRecordDO.setPunchType(punchType);
                punchRecordDO.setSourceType(SourceTypeEnum.ZKTECO.name());
                punchRecordDO.setEmployeeType(attendanceUser.getEmployeeType());
                punchRecordDOList.add(punchRecordDO);
            }
        }
        // 将封装好的打卡记录放到总的打卡记录中
        allPunchRecordDOList.addAll(punchRecordDOList);
    }

    /**
     * 封装dayIdList，将dayIdList转为Long的dayIdList，并且加一天减一天
     *
     * @param dayIdList dayIdList
     * @return List<Long>
     */
    private List<Long> getDayIdLongList(List<String> dayIdList) {
        log.info("getDayIdLongList dayIdList:{}，dayIdList size {}", JSON.toJSONString(dayIdList), dayIdList.size());
        if (CollUtil.isEmpty(dayIdList)) {
            return Lists.newArrayList();
        }
        // 防止并发修改异常
        List<DateTime> dayIdToDayDate = Lists.newCopyOnWriteArrayList();
        // 将dayIdLongList转为date格式
        for (String dayId : dayIdList) {
            DateTime dateTime = DateUtil.parse(dayId, DatePattern.PURE_DATE_PATTERN);
            dayIdToDayDate.add(dateTime);
        }
        log.info("getDayIdLongList dayIdToDayDate:{}，dayIdToDayDate size {}", JSON.toJSONString(dayIdToDayDate), dayIdToDayDate.size());

        // dayIdToDayDate加一天减一天添加到list里面
        for (Date date : dayIdToDayDate) {
            dayIdToDayDate.add(DateUtil.offsetDay(date, 1));
            dayIdToDayDate.add(DateUtil.offsetDay(date, -1));
        }
        log.info("getDayIdLongList dayIdToDayDate:{}，dayIdToDayDate size {}", JSON.toJSONString(dayIdToDayDate), dayIdToDayDate.size());

        List<Long> dayIdLongList = Lists.newArrayList();
        // 将dayIdToDayDate转为Long的dayIdList
        for (DateTime dateTime : dayIdToDayDate) {
            dayIdLongList.add(Long.parseLong(DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN)));
        }
        log.info("getDayIdLongList dayIdLongList:{}，dayIdLongList size {}", JSON.toJSONString(dayIdLongList), dayIdLongList.size());
        // 去重
        dayIdLongList = dayIdLongList.stream().distinct().collect(Collectors.toList());

        return dayIdLongList;
    }

    /**
     * 通过班次转换DayId
     *
     * @param userInfoMap
     * @param attendanceClassEmployeeConfigMap
     * @param classItemMap
     * @param userPunchRecordList
     */
    private void transferPunchRecordDayIdAndSave(Map<String, AttendanceUser> userInfoMap,
                                                 Map<Long, List<UserShiftConfigDO>> attendanceClassEmployeeConfigMap,
                                                 Map<Long, List<UserDayConfigDTO>> punchConfigMap,
                                                 Map<Long, List<PunchClassItemConfigDO>> classItemMap,
                                                 List<EmployeePunchRecordDO> userPunchRecordList) {
        if (CollectionUtils.isEmpty(userPunchRecordList)) {
            return;
        }

        for (EmployeePunchRecordDO employeePunchRecord : userPunchRecordList) {
            AttendanceUser userInfo = userInfoMap.get(employeePunchRecord.getUserCode());
            if (Objects.isNull(userInfo)) {
                continue;
            }
            // 获取打卡时间
            Date punchTime = employeePunchRecord.getPunchTime();
            // 获取初始dayId
            String dayIdStr = employeePunchRecord.getDayId();
            if (StringUtils.isBlank(dayIdStr)) {
                continue;
            }
            DateTime dateTime = DateUtil.parse(dayIdStr, DatePattern.PURE_DATE_PATTERN);
            Long preDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(dateTime, -1), DatePattern.PURE_DATE_PATTERN));
            Long afterDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(dateTime, 1), DatePattern.PURE_DATE_PATTERN));
            List<Long> dayIds = Arrays.asList(afterDayId, Long.valueOf(dayIdStr), preDayId)
                    .stream()
                    .sorted(Comparator.reverseOrder())
                    .collect(Collectors.toList());
            // 获取排班
            List<UserShiftConfigDO> employeeConfigList = attendanceClassEmployeeConfigMap.get(userInfo.getId());
            if (CollectionUtils.isEmpty(employeeConfigList)) {
                continue;
            }
            List<Long> classIds = employeeConfigList.stream()
                    .map(item -> item.getPunchClassConfigId())
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(classIds)) {
                continue;
            }
            // 获取时段
            List<PunchClassItemConfigDO> itemConfigList = new ArrayList<>();
            for (Long classId : classIds) {
                List<PunchClassItemConfigDO> itemConfigDOS = classItemMap.get(classId);
                if (CollectionUtils.isEmpty(itemConfigDOS)) {
                    continue;
                }
                itemConfigList.addAll(itemConfigDOS);
            }
            //上班时间为空的可能为自由排班
            List<PunchClassItemConfigDO> freeWorkClassItemConfigDOS = itemConfigList
                    .stream()
                    .filter(item -> Objects.isNull(item.getPunchInTime()))
                    .collect(Collectors.toList());
            itemConfigList = itemConfigList.stream().filter(item -> Objects.nonNull(item.getPunchInTime())).collect(Collectors.toList());
            Long dayId = getDayId(dayIds, punchTime, itemConfigList, employeeConfigList);
            if (Objects.nonNull(dayId) && !dayId.equals(employeePunchRecord.getDayId())) {
                employeePunchRecord.setDayId(String.valueOf(dayId));
                continue;
            }
            // 是否为自由排班
            if (CollectionUtils.isEmpty(freeWorkClassItemConfigDOS)) {
                continue;
            }
            List<UserDayConfigDTO> userDayConfigDTOList = punchConfigMap.get(userInfo.getId());
            if (CollectionUtils.isEmpty(userDayConfigDTOList)) {
                continue;
            }
            Long freeDayId = getFreeDayId(dayIds, punchTime, userDayConfigDTOList
                    , itemConfigList, employeeConfigList);
            if (Objects.nonNull(freeDayId)
                    && !dayId.equals(employeePunchRecord.getDayId())) {
                employeePunchRecord.setDayId(String.valueOf(dayId));
            }
        }
        // 保存打卡记录
        if (CollectionUtils.isNotEmpty(userPunchRecordList)) {
            employeePunchRecordAdapter.saveBatch(userPunchRecordList);
        }
    }

    private Long getDayId(List<Long> dayIds, Date punchTime,
                          List<PunchClassItemConfigDO> itemConfigList,
                          List<UserShiftConfigDO> employeeConfigList) {
        for (PunchClassItemConfigDO itemConfigDO : itemConfigList) {
            // 获取打卡时间对应的dayId
            for (Long dayId : dayIds) {
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), itemConfigList);
                if (Objects.isNull(dayPunchTimeDTO)) {
                    continue;
                }
                if (punchTime.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0
                        && punchTime.compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0) {
                    //查出来的班次和排班记录对不上，则不是当前对应的考勤日
                    List<UserShiftConfigDO> employeeConfigDOS = employeeConfigList
                            .stream()
                            .filter(item -> dayId.equals(item.getDayId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(employeeConfigDOS)
                            || !itemConfigDO.getPunchClassId().equals(employeeConfigDOS.get(0).getPunchClassConfigId())) {
                        continue;
                    }
                    //在最早最晚打卡时间范围内，则是对应的考勤日
                    return dayId;
                }
            }
        }
        return null;
    }

    private Long getFreeDayId(List<Long> dayIds, Date punchTime,
                              List<UserDayConfigDTO> userDayConfigDTOList,
                              List<PunchClassItemConfigDO> itemConfigList,
                              List<UserShiftConfigDO> employeeConfigList) {
        for (PunchClassItemConfigDO itemConfigDO : itemConfigList) {
            for (Long dayId : dayIds) {
                List<UserShiftConfigDO> employeeConfigDOS = employeeConfigList
                        .stream()
                        .filter(item -> dayId.equals(item.getDayId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(employeeConfigDOS)) continue;
                List<UserDayConfigDTO> punchConfig = userDayConfigDTOList.stream()
                        .filter(item -> dayId.equals(item.getDayId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(punchConfig)) continue;
                PunchConfigDO punchConfigDO = punchConfig.get(0).getPunchConfigDO();
                if (Objects.isNull(punchConfigDO)) {
                    continue;
                }
                //判断是否是自由排班 自由排班只需要取时段最早和最晚打卡时间，判断是否在班次时间范围内即可
                if (!StringUtils.equalsIgnoreCase(punchConfigDO.getConfigType(),
                        PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode())) {
                    continue;
                }
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(dayId, itemConfigDO);
                if (punchTime.compareTo(dayPunchTimeDTO.getDayPunchStartTime()) >= 0
                        && punchTime.compareTo(dayPunchTimeDTO.getDayPunchEndTime()) <= 0) {
                    //查出来的班次和排班记录对不上，则不是当前对应的考勤日
                    if (!itemConfigDO.getPunchClassId().equals(employeeConfigDOS.get(0).getPunchClassConfigId())) {
                        continue;
                    }
                    //在最早最晚打卡时间范围内，则是对应的考勤日
                    return dayId;
                }
            }
        }
        return null;
    }


    @Data
    private static class SyncEmployeeAttendanceHandlerParam {
        /**
         * 员工编号
         */
        private String userCodes;
        /**
         * 终端sn
         */
        private String terminalSn;
        /**
         * 开始时间
         */
        private Date startDate;
        /**
         * 结束时间
         */
        private Date endDate;
    }
}
