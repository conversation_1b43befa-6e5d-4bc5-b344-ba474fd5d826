package com.imile.attendance.cycleConfig;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigStatusSwitchCommand;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigUpdateCommand;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleCheckResult;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigDetailDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigPageDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.factory.AttendanceCycleConfigFactory;
import com.imile.attendance.cycleConfig.mapstruct.AttendanceCycleConfigMapstruct;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigDetailQuery;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigListQuery;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigPageQuery;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.util.PageUtil;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
@Service
public class AttendanceCycleConfigService {

    @Resource
    private AttendanceCycleConfigFactory cycleConfigFactory;
    @Resource
    private AttendanceCycleConfigDao attendanceCycleConfigDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private AttendanceProperties attendanceProperties;

    public List<CycleTypeEnum.CycleDetail> getCycleDetailList(String cycleType) {
        CycleTypeEnum cycleTypeEnum = CycleTypeEnum.getInstance(cycleType);
        BusinessLogicException.checkTrue(cycleTypeEnum == null,
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "cycleType");
        return cycleTypeEnum.getCycleDetailList();
    }

    public void add(AttendanceCycleConfigAddCommand addCommand) {
        cycleConfigFactory.add(addCommand);
    }

    public void update(AttendanceCycleConfigUpdateCommand updateCommand) {
        cycleConfigFactory.update(updateCommand);
    }

    public void statusSwitch(AttendanceCycleConfigStatusSwitchCommand switchCommand) {
        cycleConfigFactory.statusSwitch(switchCommand);
    }

    public AttendanceCycleConfigDetailDTO detail(AttendanceCycleConfigDetailQuery detailQuery) {
        if (ObjectUtil.isNull(detailQuery) || ObjectUtil.isNull(detailQuery.getId())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }
        Long id = detailQuery.getId();
        AttendanceCycleConfigDO attendanceCycleConfig = attendanceCycleConfigDao.getById(id);
        if (ObjectUtil.isNull(attendanceCycleConfig) ||
                ObjectUtil.equal(attendanceCycleConfig.getIsDelete(), IsDeleteEnum.YES.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_NO_EXISTS_ERROR);
        }
        return AttendanceCycleConfigMapstruct.INSTANCE.toDetail(attendanceCycleConfig);
    }

    public PaginationResult<AttendanceCycleConfigPageDTO> list(AttendanceCycleConfigListQuery listQuery) {
        // 封装参数
        AttendanceCycleConfigPageQuery query = AttendanceCycleConfigMapstruct.INSTANCE.toPageQuery(listQuery);
        List<String> countryList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), query.getCountryList());
        log.info("考勤周期列表当前用户：{},有权限的常驻国列表:{}", RequestInfoHolder.getUserCode(), countryList);
        if (CollectionUtils.isEmpty(countryList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setCountryList(countryList);
        PageInfo<AttendanceCycleConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> attendanceCycleConfigDao.selectListByCondition(query));
        List<AttendanceCycleConfigDO> cycleConfigDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(cycleConfigDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<AttendanceCycleConfigPageDTO> pageDTOList =
                AttendanceCycleConfigMapstruct.INSTANCE.toPageDTO(cycleConfigDOList);
        return PageUtil.getPageResult(pageDTOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 获取国家对应的考勤周期配置
     */
    public AttendanceCycleConfigDO getByCountryAndCycleType(String country, Integer cycleType) {
        AttendanceCycleConfigQuery query = new AttendanceCycleConfigQuery();
        query.setCountry(country);
        query.setCycleType(cycleType);
        List<AttendanceCycleConfigDO> attendanceCycleConfigList = attendanceCycleConfigDao.selectByCondition(query);
        // 过滤为激活状态的
        attendanceCycleConfigList = attendanceCycleConfigList.stream()
                .filter(AttendanceCycleConfigDO::areActive)
                .collect(Collectors.toList());
        // 该国家不存在考勤周期配置，使用默认
        if (CollectionUtils.isEmpty(attendanceCycleConfigList)) {
            return cycleConfigFactory.buildDefaultAttendanceCycleConfig();
        }
        // 如果存在则获取考勤周期
        return attendanceCycleConfigList.get(0);
    }

    /**
     * 获取考勤周期配置
     *
     * @param userId 用户ID
     * @return attendanceCycleConfigDO
     */
    public AttendanceCycleConfigDO getUserAttendanceCycleConfig(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return null;
        }
        // 获取人员信息
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            return null;
        }
        // 1：月维度，2:周维度
        int cycleType = 1;
        String locationCountry = userInfo.getLocationCountry();
        // 使用周维度的国家
        List<String> attendanceCycleWeekDimensionCountry = Optional.ofNullable(attendanceProperties.getAttendance().getAttendanceCycleWeekDimensionCountry())
                .map(str -> str.split(BusinessConstant.DEFAULT_DELIMITER))
                .map(Arrays::stream)
                .orElseGet(Stream::empty)
                .collect(Collectors.toList());
        if (attendanceCycleWeekDimensionCountry.contains(locationCountry)) {
            cycleType = 2;
        }
        return getByCountryAndCycleType(locationCountry, cycleType);
    }

    /**
     * 发放补卡次数逻辑：获取考勤周期
     * 获取考勤周期配置：补卡定时任务专属，这里不分国家，都是月维度
     *
     * @param userId 用户id
     * @return attendanceCycleConfigDO
     */
    public AttendanceCycleConfigDO getUserAttendanceCycleConfigUserCard(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return null;
        }
        // 获取人员信息
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            return null;
        }
        // 判断用户常驻国
        String locationCountry = userInfo.getLocationCountry();
        return getByCountryAndCycleType(locationCountry, AttendanceCycleTypeEnum.MONTH.getType());
    }

    /**
     * 获取当前时间所属考勤周期的时间点
     *
     * @param nowDayId      当前时间
     * @param cycleConfigDO 考勤周期配置
     * @return AttendanceDayCycleDTO
     */
    public AttendanceDayCycleDTO getUserAttendanceCycleConfigDay(Long nowDayId, AttendanceCycleConfigDO cycleConfigDO) {
        if (ObjectUtil.isNull(nowDayId) ||
                ObjectUtil.isNull(cycleConfigDO) ||
                ObjectUtil.isEmpty(cycleConfigDO.getCycleStart()) ||
                ObjectUtil.isEmpty(cycleConfigDO.getCycleEnd()) ||
                ObjectUtil.isNull(cycleConfigDO.getCycleType()) ||
                ObjectUtil.isNull(cycleConfigDO.getAbnormalExpired())) {
            return null;
        }
        return AttendanceDayCycleDTO.buildWithDayId(
                nowDayId,
                cycleConfigDO.getCycleType(),
                cycleConfigDO.getCycleStart(),
                cycleConfigDO.getCycleEnd()
        );
    }

    /**
     * 检查指定日期是否在用户的有效考勤周期内
     *
     * @param userId      用户ID
     * @param dateToCheck 需要检查的日期
     * @return 检查结果，包含是否在有效周期内及相关周期信息
     * @throws BusinessLogicException 如果用户没有考勤配置或日期超出允许的考勤周期
     */
    public AttendanceCycleCheckResult checkDateInUserAttendanceCycle(Long userId, Date dateToCheck) {
        // 获取当前日期
        Date nowDate = new Date();

        // 获取用户的考勤周期配置
        AttendanceCycleConfigDO userAttendanceCycleConfig = this.getUserAttendanceCycleConfig(userId);
        if (ObjectUtil.isNull(userAttendanceCycleConfig)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_USER_NOT_HAVE);
        }

        // 执行周期检查
        return checkDateInAttendanceCycle(nowDate, userAttendanceCycleConfig, dateToCheck);
    }

    /**
     * 检查指定日期是否在有效的考勤周期内
     *
     * @param currentDate 当前日期
     * @param cycleConfig 考勤周期配置
     * @param dateToCheck 需要检查的日期
     * @return 检查结果对象
     * @throws BusinessLogicException 如果日期超出允许的考勤周期
     */
    public AttendanceCycleCheckResult checkDateInAttendanceCycle(Date currentDate,
                                                                 AttendanceCycleConfigDO cycleConfig,
                                                                 Date dateToCheck) {
        // 将日期转换为数字格式
        Long dateToCheckId = Long.valueOf(DateUtil.format(dateToCheck, "yyyyMMdd"));
        Long currentDateId = Long.valueOf(DateUtil.format(currentDate, "yyyyMMdd"));

        // 获取考勤周期信息
        AttendanceDayCycleDTO currentCycle = this.getUserAttendanceCycleConfigDay(currentDateId, cycleConfig);
        AttendanceDayCycleDTO dateCycle = this.getUserAttendanceCycleConfigDay(dateToCheckId, cycleConfig);

        if (ObjectUtil.isNull(currentCycle) || ObjectUtil.isNull(dateCycle)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_USER_NOT_HAVE);
        }

        // 获取考勤周期类型
        CycleTypeEnum cycleTypeEnum = getCycleTypeEnum(cycleConfig.getCycleType());

        // 计算实际的异常过期周期
        Integer actualAbnormalExpired = cycleTypeEnum.getActualAbnormalExpired(
                currentDate,
                cycleConfig.getCycleStart(),
                cycleConfig.getCycleEnd(),
                cycleConfig.getAbnormalExpired()
        );

        // 计算周期偏移日期（最早可以申报的日期）
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(currentDate, cycleConfig.getCycleEnd(), actualAbnormalExpired);
        Long offsetCycleEndDayId = Long.valueOf(DateUtil.format(offsetCycleEndDate, "yyyyMMdd"));

        // 构建结果对象
        AttendanceCycleCheckResult result = new AttendanceCycleCheckResult();
        result.setInValidCycle(dateToCheckId.compareTo(offsetCycleEndDayId) > 0);
        result.setCycleStartDate(dateCycle.getAttendanceStartDate());
        result.setCycleEndDate(dateCycle.getAttendanceEndDate());
        result.setLatestValidDate(offsetCycleEndDate);

        // 如果不在有效周期内，抛出异常
        if (!result.isInValidCycle()) {
            String cycleStartStr = DateUtil.format(dateCycle.getAttendanceStartDate(), DatePattern.NORM_DATE_PATTERN);
            String cycleEndStr = DateUtil.format(dateCycle.getAttendanceEndDate(), DatePattern.NORM_DATE_PATTERN);
            throw BusinessException.get(
                    ErrorCodeEnum.EXTENDED_LEAVE_PERIOD.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.EXTENDED_LEAVE_PERIOD.getDesc(),
                            cycleStartStr, cycleEndStr)
            );
        }

        return result;
    }

    /**
     * 获取周期类型枚举
     */
    private CycleTypeEnum getCycleTypeEnum(Integer cycleType) {
        CycleTypeEnum cycleTypeEnum;
        if (ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType())) {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name());
        } else {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());
        }

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_GET_CYCLE_TYPE_ERROR);
        }

        return cycleTypeEnum;
    }

    /**
     * 确认周期校验
     *
     * @param nowDate                 当前时间
     * @param attendanceCycleConfigDO 考勤周期配置
     * @param specificTime            指定日期
     * @return Boolean
     */
    public void confirmCycleCheck(Date nowDate,
                                  AttendanceCycleConfigDO attendanceCycleConfigDO,
                                  Date specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = Long.valueOf(DateUtil.format(specificTime, "yyyyMMdd"));
        Long nowDayId = Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd"));

        Integer cycleType = attendanceCycleConfigDO.getCycleType();
        String cycleEnd = attendanceCycleConfigDO.getCycleEnd();
        Integer abnormalExpired = attendanceCycleConfigDO.getAbnormalExpired();
        //获取请假可请周期
        AttendanceDayCycleDTO attendanceDayCycleDTO = this.getUserAttendanceCycleConfigDay(Long.valueOf(DateUtil.format(nowDate, "yyyyMMdd")), attendanceCycleConfigDO);
        AttendanceDayCycleDTO attendanceDayCycle = this.getUserAttendanceCycleConfigDay(specificTimeDayId, attendanceCycleConfigDO);
        if (ObjectUtil.isNull(attendanceDayCycleDTO) || ObjectUtil.isNull(attendanceDayCycle)) {
            //throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_SALARY_CONFIG.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_SALARY_CONFIG.getDesc()));
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_USER_NOT_HAVE);
        }
        log.info("confirmCycleCheck attendanceDayCycleDTO:{}", JSON.toJSONString(attendanceDayCycleDTO));
        String oldStartDate = DateUtil.format(attendanceDayCycle.getAttendanceStartDate(), DatePattern.NORM_DATE_PATTERN);
        String oldEndDate = DateUtil.format(attendanceDayCycle.getAttendanceEndDate(), DatePattern.NORM_DATE_PATTERN);

        CycleTypeEnum cycleTypeEnum = null;
        if (ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType())) {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name());
            abnormalExpired = -abnormalExpired + 1;
        } else {
            cycleTypeEnum = CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());
            abnormalExpired = -abnormalExpired;
        }

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            log.info("cycle type is null");
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CYCLE_CONFIG_GET_CYCLE_TYPE_ERROR);
        }
        log.info("confirmCycleCheck cycleTypeEnum:{}", JSON.toJSONString(cycleTypeEnum));

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = Long.valueOf(DateUtil.format(offsetCycleEndDate, "yyyyMMdd"));

        log.info("confirmCycleCheck specificTimeDayId:{} offsetCycleEndDayId:{}", specificTimeDayId, offsetCycleEndDayId);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            throw BusinessException.get(ErrorCodeEnum.EXTENDED_LEAVE_PERIOD.getCode(), I18nUtils.getMessage(ErrorCodeEnum.EXTENDED_LEAVE_PERIOD.getDesc(), oldStartDate, oldEndDate));
        }

    }


}
