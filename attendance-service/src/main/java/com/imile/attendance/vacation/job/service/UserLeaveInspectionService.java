package com.imile.attendance.vacation.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIsSalary;
import com.imile.attendance.enums.vacation.LeaveConfigIssueFrequencyEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueTimeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueTypeEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordConditionQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.BigDecimalUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatUtils;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveConfigIssueRuleService;
import com.imile.attendance.vacation.CompanyLeaveConfigRangService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.DispatchUserRecordService;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveRecordService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.attendance.vacation.param.UserLeaveInspectionParam;
import com.imile.common.enums.StatusEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.imile.attendance.util.DateFormatUtils.DATE;


/**
 * 假期规则发放业务类(时间紧张, 暂时拷贝HRMS原有类不重构)
 *
 * <AUTHOR>
 * @menu 假期规则发放 业务层
 * @date 2025/4/26
 */
@Slf4j
@Service
public class UserLeaveInspectionService {
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private CompanyLeaveConfigRangService companyLeaveConfigRangService;
    @Resource
    private CompanyLeaveConfigIssueRuleService companyLeaveConfigIssueRuleService;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private CountryService countryService;
    @Resource
    private UserLeaveRecordService userLeaveRecordService;
    @Resource
    private DispatchUserRecordService dispatchUserRecordService;
    @Resource
    private RpcUserClient rpcUserClient;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private MigrationService migrationService;

    public void userLeaveInspectionHandler(UserLeaveInspectionParam param) {
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】参数为空");
            return;
        }
        if (ObjectUtil.isNull(param.getWhichDay())) {
            // 如果没有指定日期，则默认为1号
            param.setWhichDay(1);
        }

        if (ObjectUtil.isEmpty(param.getDispatchLeaveInterceptTime())) {
            param.setDispatchLeaveInterceptTime("2025-01-01 00:00:00");
        }

        List<String> employeeTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            employeeTypeList = Arrays.asList(param.getEmployeeType().split(","));
        }
        List<String> userCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            userCodeList = Arrays.asList(param.getUserCodes().split(","));
        }
        List<String> countryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            countryList = Arrays.asList(param.getCountryList().split(","));
        }
        List<String> leaveNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            leaveNameList = Arrays.asList(param.getLeaveName().split(","));
        }
        XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】开始执行，参数为：{}", JSON.toJSONString(param));

        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        // 获取所有配置了假期方案的国家的假期数据
        List<CompanyLeaveConfigDO> allCompanyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);
        // 指定国家的假期配置
        if (CollUtil.isNotEmpty(countryList)) {
            List<String> finalCountryList = countryList;
            allCompanyLeaveConfigList = allCompanyLeaveConfigList.stream().filter(o -> finalCountryList.contains(o.getCountry())).collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(leaveNameList)) {
            List<String> finalLeaveNameList = leaveNameList;
            allCompanyLeaveConfigList = allCompanyLeaveConfigList.stream().filter(o -> finalLeaveNameList.contains(o.getLeaveName())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(allCompanyLeaveConfigList)) {
            XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】国家：{},假期名称：{}条件过滤下,没有查询到配置了假期方案的国家的假期数据", countryList, leaveNameList);
            return;
        }

        if (CollUtil.isNotEmpty(allCompanyLeaveConfigList)) {
            // 由于需要获取时区，所以这边获取国家的列表
            List<String> countryInfoList = allCompanyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getCountry).distinct().collect(Collectors.toList());

            // 获取所有国家假期配置的主键id
            List<Long> allCompanyLeaveConfigIdList = allCompanyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getId).collect(Collectors.toList());

            // 查询所有国家每一个假期对应的假期使用范围
            List<CompanyLeaveConfigRangDO> leaveConfigRangList = companyLeaveConfigRangService.selectByLeaveId(allCompanyLeaveConfigIdList);

            // 获取所有国家假期配置下的发放规则数据
            List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRuleList = companyLeaveConfigIssueRuleService.selectByLeaveId(allCompanyLeaveConfigIdList);
            // 获取所有国家假期配置下的结转规则数据
            List<CompanyLeaveConfigCarryOverDO> leaveConfigCarryOverList = companyLeaveConfigCarryOverService.selectByLeaveId(allCompanyLeaveConfigIdList);

            // 获取所有国家假期配置下的发放规则范围表数据：这个表数据只有额度类型是司龄递增的时候才有数据
            List<Long> issueRuleIdList = leaveConfigIssueRuleList.stream().map(CompanyLeaveConfigIssueRuleDO::getId).collect(Collectors.toList());
            List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = companyLeaveConfigIssueRuleService.selectByIssueRuleId(issueRuleIdList);
            // 获取所有国家假期配置下的结转规则范围表数据：这个表数据只有结转失效类型是按入职日匹配的时候才有数据
            List<Long> carryOverIdList = leaveConfigCarryOverList.stream().map(CompanyLeaveConfigCarryOverDO::getId).collect(Collectors.toList());
            List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList = companyLeaveConfigCarryOverService.selectRangeByCarryOverId(carryOverIdList);

            // 根据issueRuleIdList获取派遣地远近的配置数据
            // 获取所有国家假期配置下的派遣地配置数据
            List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList = companyLeaveConfigIssueRuleService.selectJourneyRangByIssueRuleId(issueRuleIdList);

            // 获取所有国家假期配置下的假期详情数据
            List<CompanyLeaveItemConfigDO> allCompanyLeaveItemConfigList = companyLeaveConfigService.selectItemByConfigId(allCompanyLeaveConfigIdList);

            // 将所有配置了假期方案的所有国家根据国家分组
            Map<String, List<CompanyLeaveConfigDO>> allCountryToCompanyLeaveConfigMap = allCompanyLeaveConfigList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigDO::getCountry));

            // 新增用户假期集合
            List<UserLeaveDetailDO> addUserLeaveDetailList = Lists.newArrayList();
            // 新增用户假期详情集合
            List<UserLeaveStageDetailDO> addUserLeaveStageDetailList = Lists.newArrayList();
            // 新增用户假期操作记录集合
            List<UserLeaveRecordDO> addUserLeaveRecordList = Lists.newArrayList();
            // 更新用户假期详情集合
            List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList = Lists.newArrayList();
            // 更新用户假期集合
            List<UserLeaveDetailDO> updateUserLeaveDetailDOList = new ArrayList<>();
            // 遍历每一个国家的假期方案
            List<String> finalEmployeeTypeList = employeeTypeList;
            List<String> finalUserCodeList = userCodeList;

            CountryApiQuery countryQuery = new CountryApiQuery();
            countryQuery.setCountryNames(countryList);
            if (CollUtil.isEmpty(countryList)) {
                // 如果没有指定国家，则查询有设置假期的所有国家
                countryQuery.setCountryNames(countryInfoList);
            }
            countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
            Map<String, String> countryConfigMap = countryConfigList.stream()
                    .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));

            // 获取当前时间
            Date now = new Date();
            if (ObjectUtil.isNotEmpty(param.getEveryFullCurrentDate())) {
                now = DateUtil.parse(param.getEveryFullCurrentDate(), DatePattern.NORM_DATETIME_PATTERN);
            }

            Date finalNow = now;
            allCountryToCompanyLeaveConfigMap.forEach((country, companyLeaveConfigList) -> {
                // 获取国家对应时区
                String timeZone = countryConfigMap.getOrDefault(country, "");
                if (ObjectUtil.equal(timeZone, "")) {
                    XxlJobLogger.log("该国家:{},不存在国家时区", country);
                    return;
                }
                XxlJobLogger.log("当前国家:{},时区:{}", country, timeZone);
                // 获取国家当地时间
                Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, finalNow);
                if (ObjectUtil.isNotEmpty(param.getEveryFullCurrentDate())) {
                    dateTime = DateUtil.parse(param.getEveryFullCurrentDate(), DatePattern.NORM_DATETIME_PATTERN);
                }
                XxlJobLogger.log("北京时间:{},当前国家:{},当地时间:{}", finalNow, country, dateTime);
                Long dayId = Long.valueOf(DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN));
                int year = DateUtil.year(dateTime);
                int month = DateUtil.month(dateTime) + 1;
                int day = DateUtil.dayOfMonth(dateTime);
                XxlJobLogger.log("当前国家:{},年:{},月:{},日:{}", country, year, month, day);

                // 获取该国家假期配置id集合
                List<Long> leaveConfigIdList = companyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getId).collect(Collectors.toList());

                // 获取该国家下的假期使用范围
                List<CompanyLeaveConfigRangDO> countryLeaveConfigRangList = leaveConfigRangList.stream().filter(o -> leaveConfigIdList.contains(o.getLeaveId())).collect(Collectors.toList());
                // 将该国家下假期使用范围表按照leaveId分组，下面使用map的时候需要判空
                //Map<Long, List<CompanyLeaveConfigCarryOverDO>> countryLeaveIdToRangMap = countryLeaveConfigRangList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigCarryOverDO::getLeaveId));
                // 将该国家下假期使用范围表按照userCode分组，下面使用map的时候需要判空
                Map<String, List<CompanyLeaveConfigRangDO>> countryUserIdToRangMap = countryLeaveConfigRangList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigRangDO::getUserCode));
                // 获取countryLeaveConfigRangList中的userId集合
                List<String> countryRangUserCodeList = countryLeaveConfigRangList.stream().map(CompanyLeaveConfigRangDO::getUserCode).collect(Collectors.toList());

                // 获取该国家下的假期发放规则
                List<CompanyLeaveConfigIssueRuleDO> countryLeaveConfigIssueRuleList = leaveConfigIssueRuleList.stream().filter(o -> leaveConfigIdList.contains(o.getLeaveId())).collect(Collectors.toList());
                // 将该国家下假期发放规则表按照leaveId分组，下面使用map的时候需要判空
                Map<Long, CompanyLeaveConfigIssueRuleDO> countryLeaveIdToIssueRuleMap = countryLeaveConfigIssueRuleList.stream().collect(Collectors.toMap(CompanyLeaveConfigIssueRuleDO::getLeaveId, Function.identity()));
                // 获取该国家下的假期结转规则
                List<CompanyLeaveConfigCarryOverDO> countryLeaveConfigCarryOverList = leaveConfigCarryOverList.stream().filter(o -> leaveConfigIdList.contains(o.getLeaveId())).collect(Collectors.toList());
                // 将该国家下假期发放规则表按照leaveId分组，下面使用map的时候需要判空
                Map<Long, CompanyLeaveConfigCarryOverDO> countryLeaveIdToCarryOverMap = countryLeaveConfigCarryOverList.stream().collect(Collectors.toMap(CompanyLeaveConfigCarryOverDO::getLeaveId, Function.identity()));

                // 获取该国家下的假期发放规则范围
                // 获取countryLeaveConfigIssueRuleList的id集合
                List<Long> countryIssueRuleIdList = countryLeaveConfigIssueRuleList.stream().map(CompanyLeaveConfigIssueRuleDO::getId).collect(Collectors.toList());
                List<CompanyLeaveConfigIssueRuleRangeDO> countryLeaveConfigIssueRuleRangeList = leaveConfigIssueRuleRangeList.stream().filter(o -> countryIssueRuleIdList.contains(o.getIssueRuleId())).collect(Collectors.toList());
                // 将该国家下假期发放规则范围表按照issueRuleId分组，下面使用map的时候需要判空
                Map<Long, List<CompanyLeaveConfigIssueRuleRangeDO>> countryIssueRuleIdToRangeMap = countryLeaveConfigIssueRuleRangeList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigIssueRuleRangeDO::getIssueRuleId));
                // 获取countryLeaveConfigCarryOverList的id集合
                List<Long> countryCarryOverIdList = countryLeaveConfigCarryOverList.stream().map(CompanyLeaveConfigCarryOverDO::getId).collect(Collectors.toList());
                List<CompanyLeaveConfigCarryOverRangeDO> countryLeaveConfigCarryOverRangeList = leaveConfigCarryOverRangeList.stream().filter(o -> countryCarryOverIdList.contains(o.getCarryOverId())).collect(Collectors.toList());
                // 将该国家下假期发放规则范围表按照CarryOverId分组，下面使用map的时候需要判空
                Map<Long, List<CompanyLeaveConfigCarryOverRangeDO>> countryCarryOverIdToRangeMap = countryLeaveConfigCarryOverRangeList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigCarryOverRangeDO::getCarryOverId));

                // 获取该国家下发放规则对应的派遣地发放额度配置信息
                List<CompanyLeaveJourneyConfigDO> countryLeaveConfigIssueRuleJourneyConfigList = leaveJourneyConfigList.stream().filter(o -> countryIssueRuleIdList.contains(o.getIssueRuleId())).collect(Collectors.toList());
                // 将该国家下假期发放规则表按照issueRuleId分组，下面使用map的时候需要判空
                Map<Long, List<CompanyLeaveJourneyConfigDO>> countryIssueRuleIdToJourneyConfigRangeMap = countryLeaveConfigIssueRuleJourneyConfigList.stream().collect(Collectors.groupingBy(CompanyLeaveJourneyConfigDO::getIssueRuleId));

                // 获取该国家下的假期配置详情
                List<CompanyLeaveItemConfigDO> companyLeaveItemConfigInfo = allCompanyLeaveItemConfigList.stream().filter(o -> leaveConfigIdList.contains(o.getLeaveId())).collect(Collectors.toList());
                // 将该国家下假期详情表按照leaveId分组
                Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap = companyLeaveItemConfigInfo.stream().collect(Collectors.groupingBy(CompanyLeaveItemConfigDO::getLeaveId));

                // 将countryRangUserIdList进行去重
                List<String> countryDistinctRangUserCodeList = countryRangUserCodeList.stream().distinct().collect(Collectors.toList());
                XxlJobLogger.log("查询到的国家：{}下，所有假期绑定的员工个数去重为:{}", country, countryDistinctRangUserCodeList.size());
                // 查询这些用户(过滤)
                List<UserInfoDO> userInfoList = Optional.ofNullable(userInfoManage.listByUserCodes(countryDistinctRangUserCodeList))
                        .orElse(new ArrayList<>())
                        .stream()
                        .filter(item -> WorkStatusEnum.ON_JOB.getCode().equals(item.getWorkStatus()))
                        .collect(Collectors.toList());

                if (CollUtil.isNotEmpty(finalUserCodeList)) {
                    userInfoList = userInfoList.stream().filter(o -> finalUserCodeList.contains(o.getUserCode())).collect(Collectors.toList());
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】配置了userCodeList，userCodeList数据为：{}", finalUserCodeList.toString());
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】配置了userCodeList，过滤userCodeList之后查询到的国家：{}下，员工个数为:{}", country, userInfoList.size());
                }
                if (CollUtil.isNotEmpty(finalEmployeeTypeList)) {
                    userInfoList = userInfoList.stream().filter(o -> finalEmployeeTypeList.contains(o.getEmployeeType())).collect(Collectors.toList());
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】配置了employeeTypeList，employeeTypeList数据为：{}", finalEmployeeTypeList.toString());
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】配置了employeeTypeList，过滤employeeTypeList之后查询到的国家：{}下，员工个数为:{}", country, userInfoList.size());
                }
                // 获取该国家下所有用户主键id
                List<Long> userIdList = userInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
                if (CollUtil.isEmpty(userIdList)) {
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】查询到的国家：{}下，绑定员工个数为:{}", country, userIdList.size());
                    return;
                }
                // 查询在灰度名单的人员
                Map<Long, Boolean> userMigrationMap;
                try {
                    userMigrationMap = migrationService.verifyUsersIsEnableNewAttendance(userIdList);
                } catch (Exception e) {
                    log.error("批量验证用户迁移状态异常", e);
                    XxlJobLogger.log("迁移验证异常，跳过处理: {}", e.getMessage());
                    return;
                }
                // 统计启用新系统的用户
                List<Long> enableNewAttendanceUserIdList = userMigrationMap.entrySet()
                        .stream()
                        .filter(Map.Entry::getValue)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());

                int enableNewAttendanceUserSize = enableNewAttendanceUserIdList.size();
                XxlJobLogger.log("用户迁移状态统计 - 总数: {}, 启用新系统: {}, 未启用: {}",
                        userIdList.size(), enableNewAttendanceUserSize, (userIdList.size() - enableNewAttendanceUserSize));

                if (enableNewAttendanceUserSize == 0) {
                    XxlJobLogger.log("没有启用新考勤系统的用户，跳过处理");
                    return;
                }
                userIdList = enableNewAttendanceUserIdList;

                // 过滤启用新系统的用户信息
                userInfoList = userInfoList.stream()
                        .filter(i -> enableNewAttendanceUserIdList.contains(i.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(userInfoList)) {
                    XxlJobLogger.log("xxl-job【userLeaveInspectionNewHandler】，当前人员全部都在灰度人员里，无异常考勤要处理");
                    return;
                }
                // 获取员工入职信息
                List<AttendanceUserEntryRecord> userEntryRecordList = userEntryRecordService.selectUserEntryByUserIds(userIdList);
                // 将员工入职信息以userId为key转化为map
                Map<Long, AttendanceUserEntryRecord> userIdToUserEntryRecord = userEntryRecordList.stream().collect(Collectors.toMap(AttendanceUserEntryRecord::getUserId, Function.identity()));
                // 获取发放规则下属于工龄发放的用户，查询对应的工龄
                // 将员工信息以userId为key转化为map
                Map<Long, String> userInfoMap = userInfoList
                        .stream()
                        .filter(item -> Objects.nonNull(item.getUserCode()))
                        .collect(Collectors.toMap(UserInfoDO::getId
                                , UserInfoDO::getUserCode
                                , (existing, replacement) -> existing));
                // 将员工入职信息以userCode为key转化为map
                Map<String, Date> userEntryDateMap = new HashMap<>();
                userEntryRecordList.stream().forEach(item -> {
                    if (Objects.nonNull(item.getConfirmDate())
                            && Objects.nonNull(userInfoMap.get(item.getUserId()))) {
                        userEntryDateMap.put(userInfoMap.get(item.getUserId()), item.getConfirmDate());
                    }
                });
                Map<String, String> userMapForWorkSeniority = this.selectWorkSeniorityByIssueRule(countryLeaveConfigIssueRuleList
                        , countryLeaveConfigRangList, userEntryDateMap, dateTime);

                // 获取这些用户userIdList的所有的假期操作记录
                UserLeaveRecordConditionQuery query = new UserLeaveRecordConditionQuery();
                query.setUserIdList(userIdList);
                // 不能查询北京时间，因为数据库存储的是北京时间，存在时差问题，会导致一天发放两次的情况，要使用当地时间
                query.setDayId(dayId);
                query.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
                query.setOperationUserCode("xxl-job");
                query.setOperationUserName("xxl-job");
                List<UserLeaveRecordDO> userLeaveRecordList = userLeaveRecordService.selectUserLeaveRecordByCondition(query);


                UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
                userLeaveDetailQuery.setUserIds(userIdList);
                // 获取该国家所有员工拥有的假期
                List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
                // 该员工所有假期根据用户id分组
                Map<Long, List<UserLeaveDetailDO>> userIdToLeaveDetailMap = userLeaveDetailDOList.stream().collect(Collectors.groupingBy(UserLeaveDetailDO::getUserId));

                // 获取该国家所有员工拥有的年假假期的主键id
                List<Long> leaveIdList = userLeaveDetailDOList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
                // 获取该国家所有员工拥有的生效的并且未结转假期详情数据
                UserLeaveStageDetailQuery userLeaveStageDetailQuery = new UserLeaveStageDetailQuery();
                userLeaveStageDetailQuery.setIsInvalid(WhetherEnum.NO.getKey());
                userLeaveStageDetailQuery.setLeaveMark(WhetherEnum.NO.getKey());
                userLeaveStageDetailQuery.setLeaveIdList(leaveIdList);
                List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByCondition(userLeaveStageDetailQuery);

                // 获取该国家所有员工的拥有假期的详情map 按照leaveId分组
                Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap = userLeaveStageDetailList.stream().collect(Collectors.groupingBy(UserLeaveStageDetailDO::getLeaveId));

                // 查询这个国家下面假期绑定范围的人员的派遣信息
                List<DispatchUserRecordDO> attendanceDispatchUserRecordInfoList = dispatchUserRecordService.selectDispatchInfoByUserCode(countryDistinctRangUserCodeList);
                // 获取endFlag = 0记录
                List<DispatchUserRecordDO> endFlagZeroList = attendanceDispatchUserRecordInfoList.stream().filter(o -> ObjectUtil.equal(o.getEndFlag(), BusinessConstant.N)).collect(Collectors.toList());
                // 获取endFlag = 0记录中所有的派遣日期
                List<Date> endFlagZeroDispatchDateList = endFlagZeroList.stream().filter(it -> ObjectUtil.isNotNull(it.getDispatchDate())).map(DispatchUserRecordDO::getDispatchDate).collect(Collectors.toList());
                // 获取endFlag = 1记录
                List<DispatchUserRecordDO> endFlagOneList = attendanceDispatchUserRecordInfoList.stream().filter(o -> ObjectUtil.equal(o.getEndFlag(), BusinessConstant.Y)).collect(Collectors.toList());
                // 将endFlagOneList按照user_code分组
                Map<String, List<DispatchUserRecordDO>> userCodeToEndFlagOneListMap = endFlagOneList.stream().collect(Collectors.groupingBy(DispatchUserRecordDO::getUserCode));
                // 遍历userCodeToAttendanceDispatchUserRecordInfoMap获取每一个人处理后的派遣详细信息
                Map<String, DispatchUserRecordDO> endFlagOneDispatchUserRecordMap = Maps.newHashMap();
                handlerEndFlagDispatchUserRecordInfo(userCodeToEndFlagOneListMap, endFlagOneDispatchUserRecordMap);

                // 将attendanceDispatchUserRecordInfoList按照user_code分组
                Map<String, List<DispatchUserRecordDO>> userCodeToAttendanceDispatchUserRecordInfoMap = endFlagZeroList.stream().collect(Collectors.groupingBy(DispatchUserRecordDO::getUserCode));
                // 遍历userCodeToAttendanceDispatchUserRecordInfoMap获取每一个人处理后的派遣详细信息
                Map<String, DispatchUserRecordDO> dispatchUserRecordMap = Maps.newHashMap();
                handlerDispatchUserRecordInfo(userCodeToAttendanceDispatchUserRecordInfoMap, dispatchUserRecordMap);
                // 获取dispatchUserRecordMap的key集合和value集合
                Set<String> userCodeSet = dispatchUserRecordMap.keySet();
                // 将set转为list
                List<String> dispatchUserCodeList = Lists.newArrayList(userCodeSet);
                // 获取dispatchUserRecordMap的value集合
                List<DispatchUserRecordDO> dispatchUserRecordList = Lists.newArrayList(dispatchUserRecordMap.values());
                // 获取dispatchUserRecordList的派遣日集合，并过滤掉为null的数据
                List<Date> dispatchDateList = dispatchUserRecordList.stream().filter(Objects::nonNull).map(DispatchUserRecordDO::getDispatchDate).collect(Collectors.toList());
                // 将dispatchDateList去重，并转化为dayId的形式
                List<Date> distinctDispatchDateList = dispatchDateList.stream().distinct().collect(Collectors.toList());
                List<Long> distinctDayIdList = distinctDispatchDateList.stream().map(date -> Long.valueOf(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN))).collect(Collectors.toList());
                // 获取到endFlag = 0的派遣日dayId集合
                List<Long> endFlagZeroDistinctDayIdList = endFlagZeroDispatchDateList.stream().map(date -> Long.valueOf(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN))).collect(Collectors.toList());

                // 查询派遣人员
                List<UserInfoDO> dispatchUserInfoList = userInfoManage.listByUserCodes(dispatchUserCodeList);
                // 获取派遣人员的用户id集合
                List<Long> dispatchUserIdList = dispatchUserInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());

                // 查询派遣人员在派遣日的发放假期记录
                // 获取这些用户userIdList的所有派遣假期操作记录
                List<UserLeaveRecordDO> dispatchAllDayIdUserLeaveRecordList = Lists.newArrayList();
                // 这个是所有用户指定dayId的所有派遣假期操作记录
                List<UserLeaveRecordDO> dispatchUserLeaveRecordList = Lists.newArrayList();
                if (ObjectUtil.isNotEmpty(dispatchUserIdList) && ObjectUtil.isNotEmpty(endFlagZeroDistinctDayIdList)) {
                    UserLeaveRecordConditionQuery queryDispatch = new UserLeaveRecordConditionQuery();
                    queryDispatch.setUserIdList(dispatchUserIdList);
                    // 不能查询北京时间，因为数据库存储的是北京时间，存在时差问题，会导致一天发放两次的情况，要使用当地时间
                    queryDispatch.setDayIdList(endFlagZeroDistinctDayIdList);
                    queryDispatch.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
                    queryDispatch.setOperationUserCode("xxl-job");
                    queryDispatch.setOperationUserName("xxl-job");
                    dispatchAllDayIdUserLeaveRecordList = userLeaveRecordService.selectUserLeaveRecordByCondition(queryDispatch);
                }
                // 过滤dispatchAllDayIdUserLeaveRecordList中dayId在distinctDayIdList集合中的数据
                dispatchUserLeaveRecordList = dispatchAllDayIdUserLeaveRecordList.stream().filter(o -> distinctDayIdList.contains(o.getDayId())).collect(Collectors.toList());


                for (UserInfoDO userInfo : userInfoList) {
                    // 入职记录
                    AttendanceUserEntryRecord userEntryRecord = userIdToUserEntryRecord.get((userInfo.getId()));

                    // 该国家下该人员应该有的假期集合
                    List<CompanyLeaveConfigDO> userShouldLeaveList = Lists.newArrayList();
                    // 获取该员工应该拥有的所有假期
                    List<CompanyLeaveConfigRangDO> userLeaveConfigRangList = countryUserIdToRangMap.get(userInfo.getUserCode());

                    // 如果该员工没有绑定任何假期，则跳过
                    if (CollUtil.isEmpty(userLeaveConfigRangList)) {
                        continue;
                    }
                    // 获取userLeaveConfigRangList的leaveId集合，即是用户应该拥有的假期id集合
                    List<Long> userLeaveIdList = userLeaveConfigRangList.stream().map(CompanyLeaveConfigRangDO::getLeaveId).collect(Collectors.toList());
                    // 否则从国家所有假期里面去过滤出来该人员应该有的假期
                    userShouldLeaveList = companyLeaveConfigList.stream().filter(o -> userLeaveIdList.contains(o.getId())).collect(Collectors.toList());

                    XxlJobLogger.log("当前国家:{},用户为：{},应该有的假期个数为:{}", country, userInfo.getUserCode(), userShouldLeaveList.size());
                    XxlJobLogger.log("当前国家:{},用户为：{},应该有的假期为:{}", country, userInfo.getUserCode(), JSON.toJSONString(userShouldLeaveList));

                    // 获取指定国家假期配置按照假期类型分组
                    Map<Long, CompanyLeaveConfigDO> leaveTypeToCompanyLeaveConfigMap = userShouldLeaveList.stream()
                            .collect(Collectors.toMap(CompanyLeaveConfigDO::getId, Function.identity()));

                    // 获取该员工拥有的所有假期
                    List<UserLeaveDetailDO> userNowLeaveDetailList = userIdToLeaveDetailMap.get(userInfo.getId());
                    //防止空指针
                    userNowLeaveDetailList = userNowLeaveDetailList == null ? Lists.newArrayList() : userNowLeaveDetailList;
                    // 将该员工所有假期 变成假期类型为key的map
                    Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap = userNowLeaveDetailList.stream()
                            .filter(item -> Objects.nonNull(item.getConfigId()))
                            .collect(Collectors.toMap(UserLeaveDetailDO::getConfigId, Function.identity(), (e1, e2) -> e1));

                    // 获取用户的派遣记录
                    DispatchUserRecordDO dispatchUserRecord = dispatchUserRecordMap.get(userInfo.getUserCode());
                    // 获取回国记录
                    DispatchUserRecordDO endFlagOneDispatchUserRecord = endFlagOneDispatchUserRecordMap.get(userInfo.getUserCode());

                    // 遍历国家下用户应该有的假期
                    List<UserLeaveRecordDO> finalDispatchUserLeaveRecordList = dispatchUserLeaveRecordList;
                    List<UserLeaveRecordDO> finalDispatchAllDayIdUserLeaveRecordList = dispatchAllDayIdUserLeaveRecordList;
                    Date finalDateTime = dateTime;
                    leaveTypeToCompanyLeaveConfigMap.forEach((leaveConfigId, companyLeaveConfig) -> {
                        XxlJobLogger.log("当前国家:{},用户为：{},假期主键id：{},假期名称：{}", country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveName());

//                        // 获取假期主键id
//                        Long leaveId = companyLeaveConfig.getId();
                        // 获取假期是否派遣标志，0非派遣，1派遣
                        Integer isDispatch = companyLeaveConfig.getIsDispatch();

                        // 获取该假期的发放规则
                        CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule = countryLeaveIdToIssueRuleMap.get(leaveConfigId);
                        if (ObjectUtil.isNull(leaveConfigIssueRule)) {
                            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},没有配置发放规则，跳过。", country, leaveConfigId, companyLeaveConfig.getLeaveName());
                            return;
                        }
                        // 获取该假期的结转规则
                        CompanyLeaveConfigCarryOverDO leaveConfigCarryOver = countryLeaveIdToCarryOverMap.get(leaveConfigId);
                        if (ObjectUtil.isNull(leaveConfigCarryOver)) {
                            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},没有配置结转规则，跳过。", country, leaveConfigId, companyLeaveConfig.getLeaveName());
                            return;
                        }

                        // 由于定时任务是每小时执行一次，如果是每月固定日、或下面KSA、HQ、UAE的年假这种，这边根据记录的发放日期，判断是否发放过，如果发放过，直接跳过
                        List<UserLeaveRecordDO> filterUserLeaveRecordList = userLeaveRecordList.stream()
                                .filter(o -> o.getUserId().equals(userInfo.getId()) && leaveConfigId.equals(o.getConfigId()))
                                .collect(Collectors.toList());

                        // 派遣发放记录
                        List<UserLeaveRecordDO> dispatchFilterUserLeaveRecordList = finalDispatchUserLeaveRecordList.stream()
                                .filter(o -> o.getUserId().equals(userInfo.getId()) && leaveConfigId.equals(o.getConfigId()))
                                .collect(Collectors.toList());
                        // 所有派遣发放记录过滤指定用户以及假期类型
                        List<UserLeaveRecordDO> dispatchFilterAllDayIdUserLeaveRecordList = finalDispatchAllDayIdUserLeaveRecordList.stream()
                                .filter(o -> o.getUserId().equals(userInfo.getId()) && leaveConfigId.equals(o.getConfigId()))
                                .collect(Collectors.toList());

                        if (ObjectUtil.equal(isDispatch, BusinessConstant.Y) && (DateUtil.compare(finalDateTime, DateUtil.parse(param.getDispatchLeaveInterceptTime())) < 0)) {
                            XxlJobLogger.log("派遣假期不发放:当前国家:{},用户为：{},假期主键id：{},假期类型：{}"
                                    , country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveType());
                            return;
                        }

                        // 获取含司龄递增、年龄递增、工龄递增、常驻省市假期的范围数据，也就是具体额度的数据
                        List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList = countryIssueRuleIdToRangeMap.get(leaveConfigIssueRule.getId());
                        // 获取含入职日期设置得结转失效规则
                        List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList = countryCarryOverIdToRangeMap.get(leaveConfigCarryOver.getId());

                        // 获取路假期的范围数据，也就是那些国家发放多少路途假额度，通过这张表（hrms_attendance_dispatch_user_record）查询人员对应最新的派遣日以及派遣地
                        List<CompanyLeaveJourneyConfigDO> companyLeaveJourneyConfigRangInfoList = countryIssueRuleIdToJourneyConfigRangeMap.get(leaveConfigIssueRule.getId());

                        // 假期存在发放规则的情况，一次性发放的情况判断
                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())
                                && ObjectUtil.notEqual(leaveConfigIssueRule.getIssueTime(), LeaveConfigIssueTimeEnum.EMPLOYEE_ONBOARDING_DAY.getType())) {
                            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是一次性发放，发放时间不是员工入职日，属于配置错误，跳过。"
                                    , country, leaveConfigId, companyLeaveConfig.getLeaveType());
                            return;
                        }
                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())
                                && ObjectUtil.notEqual(leaveConfigIssueRule.getIssueType(), LeaveConfigIssueTypeEnum.FIXED_AMOUNT.getType())
                                && ObjectUtil.notEqual(leaveConfigIssueRule.getIssueType(), LeaveConfigIssueTypeEnum.RELEASE_BY_PROVINCE_OR_CITY.getType())) {
                            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是一次性发放，额度类型不是固定额度和按省市配置，属于配置错误，跳过。"
                                    , country, leaveConfigId, companyLeaveConfig.getLeaveType());
                            return;
                        }

                        // 如果发放频次是一次性发放并且发放时间配置是员工入职日，额度类型是固定额度
                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())
                                && ObjectUtil.equal(leaveConfigIssueRule.getIssueTime(), LeaveConfigIssueTimeEnum.EMPLOYEE_ONBOARDING_DAY.getType())
                                && ObjectUtil.equal(leaveConfigIssueRule.getIssueType(), LeaveConfigIssueTypeEnum.FIXED_AMOUNT.getType())) {
                            // 根据发放规则里面的额度类型，进行额度发放（一次行发放+员工入职日+固定额度)
                            grantHolidays(leaveConfigIssueRule
                                    , userInfo, userEntryRecord
                                    , leaveConfigId, companyLeaveConfig
                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                    , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                                    , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                                    , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, finalDateTime);
                        }

                        // 如果发放频次是一次性发放并且发放时间配置是员工入职日，额度类型是按省市发放
                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())
                                && ObjectUtil.equal(leaveConfigIssueRule.getIssueTime(), LeaveConfigIssueTimeEnum.EMPLOYEE_ONBOARDING_DAY.getType())
                                && ObjectUtil.equal(leaveConfigIssueRule.getIssueType(), LeaveConfigIssueTypeEnum.RELEASE_BY_PROVINCE_OR_CITY.getType())) {
                            // 根据发放规则里面的额度类型，进行额度发放（一次行发放+员工入职日+省市发放)
                            // 一次性发放
                            // 如果之前假期记录是激活状态，直接跳过
                            UserLeaveDetailDO userLeaveDetail = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
                            if (StatusEnum.ACTIVE.getCode().equals(userLeaveDetail.getStatus())) {
                                return;
                            }
                            // 获取人员对应发放余额
                            BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeInfoList);
                            // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                            grantSlHolidays(userInfo, userEntryRecord, leaveConfigId, leaveConfigIssueRule,
                                    leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, companyLeaveConfig,
                                    leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList,
                                    addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                                    updateUserLeaveStageDetailList, locationDayByRule,
                                    BigDecimal.ONE, BusinessConstant.ONE, finalDateTime);
                        }

                        // 如果是周期性发放，发放时间如果是员工入职日的话，则配置错误，直接跳过
                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType()) &&
                                ObjectUtil.equal(leaveConfigIssueRule.getIssueTime(), LeaveConfigIssueTimeEnum.EMPLOYEE_ONBOARDING_DAY.getType())) {
                            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是员工入职日，属于配置错误，跳过。"
                                    , country, leaveConfigId, companyLeaveConfig.getLeaveType());
                            return;
                        }

                        if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency()
                                , LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType())) {
                            // 获取数据库假期配置的发放月份和发放日期
                            Integer issueMonth = leaveConfigIssueRule.getIssueMonth();
                            Integer issueDay = leaveConfigIssueRule.getIssueDay();

                            // 获取每满的发放配置项
                            // 可能需要替换字段
                            Integer issueEveryFull = leaveConfigIssueRule.getCycleNumber();
                            Integer issueEveryFullUnit = leaveConfigIssueRule.getCycleUnit();

                            log.info("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放月份：{}，发放日期：{}"
                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType(), issueMonth, issueDay);
                            String workSeniority = userMapForWorkSeniority.get(userInfo.getUserCode());
                            switch (leaveConfigIssueRule.getIssueTime()) {
                                case 1:
                                    // 每年固定日操作
                                    log.info("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，发放日期：{}"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType(), issueDay);
                                    if (ObjectUtil.equal(issueMonth, 0) || ObjectUtil.equal(issueDay, 0)) {
                                        XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，发放日期配置错误，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    // 处理派遣假期
                                    if (ObjectUtil.equal(isDispatch, BusinessConstant.Y)) {
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，属于派遣假期，进入派遣假期发放。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        // 如果是派遣假期
                                        // 将下面内容，改造为，如果到了派遣日，则发放假期。如果到了发放日，则发放。如果派遣跟发放日相同则发放一次。
                                        // 到发放日发放或发放日跟派遣日在同一天情况
                                        if (month == issueMonth && day == issueDay) {
                                            // 这次针对不管是每满年还是每满月，如果已经发放过，直接跳过
                                            if (CollUtil.isNotEmpty(filterUserLeaveRecordList)) {
                                                if (filterUserLeaveRecordList.size() > 1) {
                                                    XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。"
                                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                                    return;
                                                }
                                                XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，跳过。"
                                                        , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                                return;
                                            }
                                            // 到了发放日
                                            XxlJobLogger.log("当前用户：{},当前国家时间:{},国家【{}】的假期【{}】到发放日，开始发放假期"
                                                    , userInfo.getUserCode(), dayId, country, companyLeaveConfig.getLeaveName());
                                            handleFixedDayEveryYear(finalDateTime, country, leaveConfigId, userEntryRecord
                                                    , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                                    , userInfo
                                                    , companyLeaveConfig.getLeaveType(), companyLeaveConfig, leaveIdToItemConfigMap
                                                    , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                                                    , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                                                    , updateUserLeaveStageDetailList, finalDateTime, workSeniority);
                                            return;
                                        }

                                        // 如果发放日跟派遣日不在同一天，各发各的
                                        if (ObjectUtil.isNull(dispatchUserRecord)) {
                                            XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},派遣记录不存在，跳过。"
                                                    , country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        Date dispatchDate = dispatchUserRecord.getDispatchDate();
                                        if (ObjectUtil.isNull(dispatchDate)) {
                                            XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},员工派遣日期为空，跳过。"
                                                    , country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        // 将dispatchDate转换为dayId
                                        Long dispatchDayId = Long.valueOf(DateUtil.format(dispatchDate, DatePattern.PURE_DATE_PATTERN));

                                        if (dispatchDayId > dayId) {
                                            XxlJobLogger.log("error：当前国家:{},用户为：{},派遣日期：{},当前日期: {},未到派遣日期那一天，跳过。"
                                                    , country, userInfo.getUserCode(), DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN), DateUtil.format(finalDateTime, DatePattern.NORM_DATETIME_PATTERN));
                                            return;
                                        }

                                        // 将dispatchFilterUserLeaveRecordList按照dayId正序
                                        List<UserLeaveRecordDO> dispatchFilterUserLeaveRecordListSort = dispatchFilterAllDayIdUserLeaveRecordList.stream().sorted(Comparator.comparing(UserLeaveRecordDO::getDayId)).collect(Collectors.toList());
                                        // 获取第一条数据的dayId和该用户最新派遣dayId比较，如果当前最新派遣日期小于已发放过的dayId，则跳过。
                                        if (CollUtil.isNotEmpty(dispatchFilterUserLeaveRecordListSort)) {
                                            UserLeaveRecordDO firstUserLeaveRecord = dispatchFilterUserLeaveRecordListSort.get(0);
                                            if (ObjectUtil.isNotNull(firstUserLeaveRecord) && ObjectUtil.isNotNull(firstUserLeaveRecord.getDayId())) {
                                                Long latestDayId = firstUserLeaveRecord.getDayId();
                                                // 如果当前派遣记录表里面最新的派遣日期小于已发放派遣假发放最新日期，则跳过
                                                if (dispatchDayId < latestDayId) {
                                                    XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},当前派遣记录表里面最新的派遣日期：{},已发放派遣假最新日期: {},当前派遣记录表里面最新的派遣日期小于已发放派遣假发放最新日期,跳过。"
                                                            , country, userInfo.getUserCode(), leaveConfigId, companyLeaveConfig.getLeaveType(), dispatchDayId, latestDayId);
                                                    return;
                                                }
                                            }
                                        }

                                        // 将dispatchFilterUserLeaveRecordList按照dayId匹配
                                        List<UserLeaveRecordDO> dispatchDayIdFilterUserLeaveRecordList = dispatchFilterUserLeaveRecordList.stream().filter(userLeaveRecord -> ObjectUtil.equal(userLeaveRecord.getDayId(), dispatchDayId)).collect(Collectors.toList());

                                        XxlJobLogger.log("当前国家:{},用户为：{},派遣日期：{},派遣记录数量：{}"
                                                , country, userInfo.getUserCode(), DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN), dispatchDayIdFilterUserLeaveRecordList.size());

                                        // 这边针对派遣的假期，需要累加发放，看派遣日当天是否发放过
                                        if (CollUtil.isNotEmpty(dispatchDayIdFilterUserLeaveRecordList)) {
                                            if (dispatchDayIdFilterUserLeaveRecordList.size() > 1) {
                                                XxlJobLogger.log("error：dispatch 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。"
                                                        , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                                return;
                                            }
                                            XxlJobLogger.log("dispatch 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，跳过。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }

                                        // 否则执行发放逻辑，这边使用dispatchDate，不是datetime，存在派遣时间在0801，但是审核通过时间0805,其实是5号才通过，定时任务才会扫描到。需要查询是派遣日有没有发放。
                                        XxlJobLogger.log("dispatch 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，开始发放。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        handleDispatchFixedDayEveryYear(finalDateTime, country, leaveConfigId, userEntryRecord
                                                , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                                , userInfo, companyLeaveConfig.getLeaveType()
                                                , companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                                , dispatchDate, dispatchDate, workSeniority);
                                        return;
                                    }

                                    // 特殊逻辑：如果这个人有派遣回国记录，以回国日期为标准
                                    // 回国场景：如果回国后，不派遣了，当年会发，后面年份会发满额度，因为在第二年的时候，回国日期所在年是小于当前年份的所以会发满额度，没问题
                                    if (ObjectUtil.isNotNull(endFlagOneDispatchUserRecord)) {

                                        // 到发放日发放或发放日跟回国日在同一天情况
                                        if (month == issueMonth && day == issueDay) {
                                            // 不需要下面判断是否发放过的逻辑，因为非派遣假期一年只会发放一次，通过判断是否存在非结转数据来控制是否需要发放。

                                            // 这次针对不管是每满年还是每满月，如果已经发放过，直接跳过
                                            //if (CollUtil.isNotEmpty(filterUserLeaveRecordList)) {
                                            //    if (filterUserLeaveRecordList.size() > 1) {
                                            //        XxlJobLogger.log("error return：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。", userInfo.getUserCode(), country, leaveId, leaveType);
                                            //        return;
                                            //    }
                                            //    XxlJobLogger.log("return 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，已经发放过，跳过。", userInfo.getUserCode(), country, leaveId, leaveType);
                                            //    return;
                                            //}
                                            // 到了发放日
                                            XxlJobLogger.log("return 当前用户：{},当前国家时间:{},国家【{}】的假期【{}】到发放日，开始发放假期"
                                                    , userInfo.getUserCode(), dayId, country, companyLeaveConfig.getLeaveName());
                                            handleFixedDayEveryYear(finalDateTime, country, leaveConfigId, userEntryRecord
                                                    , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                                    , userInfo
                                                    , companyLeaveConfig.getLeaveType(), companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                                    , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                                    , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                                    , finalDateTime, workSeniority);
                                            return;
                                        }

                                        Date dispatchDate = endFlagOneDispatchUserRecord.getDispatchDate();
                                        if (ObjectUtil.isNull(dispatchDate)) {
                                            XxlJobLogger.log("error return：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工不存在回国记录信息，跳过。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }

                                        // 如果当前时间不是发放日，直接跳过 【这个不需要拦截了，因为如果每年1月1号发放，但是别人入职是5月1号，还是需要给这个人发放假期的】
                                        if (ObjectUtil.isNull(userEntryRecord)) {
                                            XxlJobLogger.log("error return：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工不存在入职记录信息，跳过。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        Date entryDate = userEntryRecord.getConfirmDate();
                                        if (ObjectUtil.isNull(userEntryRecord.getConfirmDate())) {
                                            XxlJobLogger.log("error return：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工确认入职字段为null，跳过。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        int entryMonth = DateUtil.month(entryDate) + 1;
                                        int entryDay = DateUtil.dayOfMonth(entryDate);
                                        String formatEntryDate = DateUtil.format(entryDate, DatePattern.NORM_DATETIME_PATTERN);
                                        XxlJobLogger.log("return 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},员工入职日期：{}"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType(), formatEntryDate);

                                        // 如果不满足上面条件，则判断当前国家时间，如果不是发放日，直接跳过
                                        XxlJobLogger.log(" return not dispatch 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，开始发放。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        handleEndFlagOneDispatchFixedDayEveryYear(finalDateTime, country, leaveConfigId
                                                , userEntryRecord, leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                                , userInfo, companyLeaveConfig.getLeaveType(), companyLeaveConfig
                                                , leaveIdToItemConfigMap
                                                , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                                                , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                                                , updateUserLeaveStageDetailList, dispatchDate, dispatchDate, workSeniority);
                                        return;
                                    }

                                    // 非回国场景：  没有派遣也没有回国，一致呆在一个地方，那就是下面的普通逻辑按照入职日来比较
                                    // 如果当前时间不是发放日，直接跳过 【这个不需要拦截了，因为如果每年1月1号发放，但是别人入职是5月1号，还是需要给这个人发放假期的】
                                    if (ObjectUtil.isNull(userEntryRecord)) {
                                        XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工不存在入职记录信息，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    Date entryDate = userEntryRecord.getConfirmDate();
                                    if (ObjectUtil.isNull(userEntryRecord.getConfirmDate())) {
                                        XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工确认入职字段为null，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    int entryMonth = DateUtil.month(entryDate) + 1;
                                    int entryDay = DateUtil.dayOfMonth(entryDate);
                                    String formatEntryDate = DateUtil.format(entryDate, DatePattern.NORM_DATETIME_PATTERN);
                                    XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},员工入职日期：{}"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType(), formatEntryDate);

                                    // 如果不满足上面条件，则判断当前国家时间，如果不是发放日，直接跳过
                                    if (month != issueMonth || day != issueDay) {
                                        if (entryMonth > issueMonth || (entryMonth == issueMonth && entryDay >= issueDay)) {
                                            XxlJobLogger.log("国家【{}】的假期【{}】，当前用户：{},入职时间:{}大于发放日：{},进行补发假期"
                                                    , country, companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), formatEntryDate, issueMonth + "-" + issueDay);
                                            handleFixedDayEveryYear(finalDateTime, country, leaveConfigId, userEntryRecord
                                                    , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                                    , userInfo
                                                    , companyLeaveConfig.getLeaveType(), companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                                    , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                                    , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                                    , finalDateTime, workSeniority);
                                            return;
                                        }
                                        XxlJobLogger.log("当前用户：{},当前国家时间:{},国家【{}】的假期【{}】未到发放日，不发放假期"
                                                , userInfo.getUserCode(), dayId, country, companyLeaveConfig.getLeaveName());
                                        return;
                                    }
                                    XxlJobLogger.log("not dispatch 当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，开始发放。"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                    handleFixedDayEveryYear(finalDateTime, country, leaveConfigId, userEntryRecord
                                            , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                            , userInfo
                                            , companyLeaveConfig.getLeaveType(), companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                            , finalDateTime, workSeniority);
                                    break;
                                case 2:
                                    // 每月固定日
                                    log.info("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，发放日期：{}"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType(), issueDay);

                                    if (ObjectUtil.equal(issueDay, 0)) {
                                        XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，发放日期配置错误，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    // 如果当前时间不是发放日，直接跳过
                                    if (day != issueDay) {
                                        XxlJobLogger.log("当前用户：{},当前时间:{},国家【{}】的假期【{}】未到发放日"
                                                , userInfo.getUserCode(), dayId, country, companyLeaveConfig.getLeaveName());
                                        return;
                                    }

                                    // 由于定时任务是每小时执行一次，如果是每月固定日，这边根据记录的发放日期，判断是否发放过，如果发放过，直接跳过
                                    if (CollUtil.isNotEmpty(filterUserLeaveRecordList)) {
                                        if (filterUserLeaveRecordList.size() > 1) {
                                            XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，已经发放过，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，开始发放。"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                    handleFixedDayEveryMonth(finalDateTime, country, leaveConfigId, userEntryRecord
                                            , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                            , userInfo
                                            , companyLeaveConfig.getLeaveType(), companyLeaveConfig
                                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                            , finalDateTime);
                                    break;
                                case 4:
                                    // 按入职日每满发放
                                    // 取值：页面额度
                                    // 这个一般都是固定额度，然后固定额度取值从新的支持小数的余额（issue_quota字段）取发放值

                                    // 1. 根据发放时间判断是否到发放时间，比如入职日每满2年或每满2月
                                    // 2. 如果满足发放时间，则进入下面方法，根据发放类型，处理发放。发放类型包括：固定额度，司领递增，不限额度，无初始额度，按派遣国远近等等

                                    // 获取员工入职日期
                                    Date entryDate4 = userEntryRecord.getConfirmDate();
                                    if (entryDate4 == null) {
                                        XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},员工入职日期为空，跳过。"
                                                , country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveType());
                                        return;
                                    }

                                    // 判断发放逻辑是每满几年还是每满几月
                                    if (ObjectUtil.notEqual(issueEveryFullUnit, 1) && ObjectUtil.notEqual(issueEveryFullUnit, 2)) {
                                        XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},每满单位配置错误，每满单位配置为：{},跳过。"
                                                , country, userInfo.getUserCode(), companyLeaveConfig.getId(), companyLeaveConfig.getLeaveType(), issueEveryFullUnit);
                                        return;
                                    }
                                    boolean issueFlag = isIssueFlag(issueEveryFullUnit, entryDate4, finalDateTime, issueEveryFull);
                                    if (!issueFlag) {
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按入职日，未到发放日，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }

                                    // 这次针对不管是每满年还是每满月，如果已经发放过，直接跳过
                                    if (CollUtil.isNotEmpty(filterUserLeaveRecordList)) {
                                        if (filterUserLeaveRecordList.size() > 1) {
                                            XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，已经发放过，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    // 取发放表的发放额度进行发放假期余额
                                    XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按入职日，开始发放。"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                    handleByEntryDateEveryFull(finalDateTime, country, leaveConfigId, userEntryRecord
                                            , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                            , userInfo, companyLeaveConfig.getLeaveType()
                                            , companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                            , finalDateTime, workSeniority);
                                    break;
                                case 5:
                                    // 按派遣日每满发放
                                    // 取值：页面额度
                                    // 这个一般都是固定额度，然后固定额度取值从新的支持小数的余额（issue_amount字段）取发放值
                                    // 1. 根据发放时间判断是否到发放时间，比如入职日每满2年或每满2月
                                    // 2. 如果满足发放时间，则进入下面方法，根据发放类型，处理发放。发放类型包括：固定额度，司领递增，不限额度，无初始额度，按派遣国远近等等
                                    // 举例：路途假期：肯定是按派遣日发放，然后每满9个月发放一次，发放类型是按派遣国远近的配置额度来发的。
                                    // 1. 获取用户的派遣日期

                                    if (ObjectUtil.isNull(dispatchUserRecord)) {
                                        XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},派遣记录不存在，跳过。"
                                                , country, userInfo.getUserCode(), leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }

                                    Date dispatchDate = dispatchUserRecord.getDispatchDate();
                                    if (ObjectUtil.isNull(dispatchDate)) {
                                        XxlJobLogger.log("error：当前国家:{},用户为：{},假期主键id：{},假期类型：{},员工派遣日期为空，跳过。"
                                                , country, userInfo.getUserCode(), leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }
                                    boolean dispatchDateFlag = isIssueFlag(issueEveryFullUnit, dispatchDate, finalDateTime, issueEveryFull);
                                    if (!dispatchDateFlag) {
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，未到发放日，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }

                                    // 由于定时任务是每小时执行一次，如果是派遣日每满，这边根据记录的发放日期，判断是否发放过，如果发放过，直接跳过
                                    if (CollUtil.isNotEmpty(filterUserLeaveRecordList)) {
                                        if (filterUserLeaveRecordList.size() > 1) {
                                            XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，已经发放过，但是发放记录有多条，可能存在发放多次的情况，需要看一下，如果存在多次发放，需要hr调整他的余额值。"
                                                    , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                            return;
                                        }
                                        XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，已经发放过，跳过。"
                                                , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                        return;
                                    }

                                    // 发放假期
                                    // 取配置表配置的每一个国家的发放额度来发放具体额度
                                    XxlJobLogger.log("当前用户：{},当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，开始发放。"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                    handleByDispatchDateEveryFull(finalDateTime, country, leaveConfigId, userEntryRecord
                                            , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                            , userInfo
                                            , companyLeaveConfig.getLeaveType(), companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                            , companyLeaveJourneyConfigRangInfoList, dispatchUserRecord, finalDateTime);
                                    break;
                                default:
                                    XxlJobLogger.log("error：userInfo.getUserCode(),当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间配置错误，跳过。"
                                            , userInfo.getUserCode(), country, leaveConfigId, companyLeaveConfig.getLeaveType());
                                    break;
                            }
                        }
                    });
                }
            });

            //落库
            XxlJobLogger.log("开始落库");
            userLeaveDetailManage.userLeaveBalanceDaysUpdate(addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveStageDetailList, updateUserLeaveDetailDOList);
        }
    }

    /**
     * 获取人员年龄对应的应发假期余额
     *
     * @param userInfo
     * @param issueRuleRangeList
     * @return BigDecimal
     */
    public BigDecimal selectLeaveDayByRule(Date localDate, UserInfoDO userInfo,
                                           List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList) {
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getBirthday())) {
            return BigDecimal.ZERO;
        }
        if (CollectionUtils.isEmpty(issueRuleRangeList)) {
            return BigDecimal.ZERO;
        }
        // 计算对应的出生日期
        LocalDate birthDate = LocalDate.parse(DateUtil.format(userInfo.getBirthday(), DatePattern.NORM_DATE_PATTERN));
        LocalDate currentDate = LocalDate.parse(DateUtil.format(localDate, DatePattern.NORM_DATE_PATTERN));
        // 计算对应的年龄（包括小数部分）
        Period period = Period.between(birthDate, currentDate);
        int years = period.getYears();
        LocalDate lastBirthday = birthDate.plusYears(years);
        long daysSinceLastBirthday = ChronoUnit.DAYS.between(lastBirthday, currentDate);
        double age = years + (daysSinceLastBirthday / 365.25);
        // 计算年龄对应的应发余额
        return this.findQuotaByRuleRange(age, issueRuleRangeList);
    }

    /**
     * 获取人员工龄对应的应发假期余额
     *
     * @param workSeniorityStr
     * @param issueRuleRangeList
     * @return
     */
    public BigDecimal selectWorkSeniorityByRule(String workSeniorityStr,
                                                List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList) {
        if (Objects.isNull(workSeniorityStr) || Integer.valueOf(workSeniorityStr) < 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal workSeniorityMonth = BigDecimal.valueOf(Long.parseLong(workSeniorityStr));
        Double workSeniority = BigDecimalUtil.divide(workSeniorityMonth, BigDecimal.valueOf(12))
                .setScale(2, RoundingMode.HALF_UP).doubleValue();
        // 计算工龄龄对应的应发余额
        return this.findQuotaByRuleRange(workSeniority, issueRuleRangeList);
    }

    /**
     * 通过范围寻找应发余额
     *
     * @param year
     * @param issueRuleRangeList
     * @return
     */
    private BigDecimal findQuotaByRuleRange(Double year,
                                            List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList) {
        if (CollectionUtils.isEmpty(issueRuleRangeList)) {
            return BigDecimal.ZERO;
        }
        for (CompanyLeaveConfigIssueRuleRangeDO issueRuleRange : issueRuleRangeList) {
            Integer symbolLeft = issueRuleRange.getSymbolLeft();
            Integer yearLeft = issueRuleRange.getYearLeft();
            Integer symbolRight = issueRuleRange.getSymbolRight();
            Integer yearRight = issueRuleRange.getYearRight();
            BigDecimal issueQuota = issueRuleRange.getIssueQuota();
            // 左边是大于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)
                    && year > yearLeft && year < yearRight) {
                return issueQuota;
            }
            // 左边是大于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)
                    && year > yearLeft && year <= yearRight) {
                return issueQuota;
            }
            // 左边是大于等于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)
                    && year >= yearLeft && year < yearRight) {
                return issueQuota;
            }
            // 左边是大于等于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)
                    && year >= yearLeft && year <= yearRight) {
                return issueQuota;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取用户工龄信息
     *
     * @param userCodeList
     * @return
     */
    public Map<String, String> selectWorkSeniority(List<String> userCodeList,
                                                   Map<String, Date> userEntryDateMap,
                                                   Date dateTime) {
        if (CollectionUtils.isEmpty(userCodeList)) {
            return new HashMap<>();
        }
        Map<String, String> userMapForWorkSeniority = this.selectInitialWorkSeniority(userCodeList);
        // 根据日期计算初始工龄+司龄
        this.calcWorkSeniorityMonth(userMapForWorkSeniority, userEntryDateMap, dateTime);
        return userMapForWorkSeniority;
    }

    /**
     * 获取用户工龄信息
     *
     * @param userCodeList
     * @return
     */
    public Map<String, String> selectInitialWorkSeniority(List<String> userCodeList) {
        Map<String, String> userMapForWorkSeniority = new HashMap();
        if (CollectionUtils.isEmpty(userCodeList)) {
            return userMapForWorkSeniority;
        }
        // 分组500查询工龄接口
        for (List<String> userCodePartition : Lists.partition(userCodeList, 500)) {
            List<UserDynamicInfoDTO> userDynamicInfo = rpcUserClient.listUserDynamicInfo(userCodePartition
                    , Lists.newArrayList(UserDynamicFieldEnum.INITIAL_WORK_SENIORITY));
            if (CollectionUtils.isEmpty(userDynamicInfo)) {
                continue;
            }
            Map<String, String> workSeniorityMap = userDynamicInfo.stream()
                    .collect(Collectors.toMap(UserDynamicInfoDTO::getUserCode
                            , item -> item.getDynamicFieldMap()
                                    .getOrDefault(UserDynamicFieldEnum.INITIAL_WORK_SENIORITY.getKey(), "0")
                            , (existing, replacement) -> existing));
            userMapForWorkSeniority.putAll(workSeniorityMap);
        }
        return userMapForWorkSeniority;
    }

    /**
     * 通过发放规则获取用户工龄信息
     *
     * @param issueRuleList
     * @return
     */
    public Map<String, String> selectWorkSeniorityByIssueRule(List<CompanyLeaveConfigIssueRuleDO> issueRuleList,
                                                              List<CompanyLeaveConfigRangDO> leaveConfigRangList,
                                                              Map<String, Date> userEntryDateMap,
                                                              Date dateTime) {
        if (CollectionUtils.isEmpty(issueRuleList)) {
            return new HashMap();
        }
        // 过滤出工龄的规则
        List<Long> configIds = issueRuleList.stream()
                .filter(item -> LeaveConfigIssueTypeEnum.INCREASING_WITH_LENGTH_OF_SERVICE.getType().equals(item.getIssueType()))
                .map(CompanyLeaveConfigIssueRuleDO::getLeaveId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configIds)) {
            return new HashMap();
        }
        // 过滤出工龄规则的用户
        List<String> userCodes = leaveConfigRangList.stream()
                .filter(item -> configIds.contains(item.getLeaveId()))
                .map(CompanyLeaveConfigRangDO::getUserCode)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userCodes)) {
            return new HashMap();
        }
        // 获取用户工龄信息
        return this.selectWorkSeniority(userCodes, userEntryDateMap, dateTime);
    }

    /**
     * 获取人员省市对应的应发假期余额
     *
     * @param userInfo
     * @param issueRuleRangeList
     * @return
     */
    public BigDecimal selectLocationDayByRule(UserInfoDO userInfo,
                                              List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList) {
        if (Objects.isNull(userInfo)) {
            return BigDecimal.ZERO;
        }
        if (CollectionUtils.isEmpty(issueRuleRangeList)) {
            return BigDecimal.ZERO;
        }
        if (Objects.isNull(userInfo.getLocationProvince()) && Objects.isNull(userInfo.getLocationCity())) {
            return BigDecimal.ZERO;
        }
        // 查询对应省市
        for (CompanyLeaveConfigIssueRuleRangeDO issueRuleRange : issueRuleRangeList) {
            String locationProvince = issueRuleRange.getLocationProvince();
            String locationCity = issueRuleRange.getLocationCity();
            BigDecimal issueQuota = issueRuleRange.getIssueQuota();

            if (StringUtils.isBlank(locationProvince)) {
                continue;
            }

            if (StringUtils.isNotBlank(locationCity)
                    && StringUtils.equalsIgnoreCase(locationProvince, userInfo.getLocationProvince())
                    && StringUtils.equalsIgnoreCase(locationCity, userInfo.getLocationCity())) {
                return issueQuota;
            }

            if (StringUtils.equalsIgnoreCase(locationProvince, userInfo.getLocationProvince())) {
                return issueQuota;
            }
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取每年固定日，非司领的假期额度（包含折扣，非折扣）
     *
     * @param id              假期主表id
     * @param entryDate       入职时间
     * @param oldDispatchDate 上次派遣日期
     * @param dispatchDate    派遣日期
     * @param userInfo        用户id
     * @param isBack          是否回国场景
     * @param isReissue       是否补发场景
     * @return BigDecimal
     */
    public Map<Integer, BigDecimal> getFixedLeaveQuota(Long id, Date entryDate, Date oldDispatchDate,
                                                       Date dispatchDate, UserInfoDO userInfo,
                                                       Boolean isBack,
                                                       Boolean isReissue) {
        Map<Integer, BigDecimal> targetMap = Maps.newHashMap();
        log.info("getFixedLeaveQuota. id：{}, entryDate：{}, dispatchDate：{}", id, entryDate, dispatchDate);
        if (ObjectUtil.isNull(id) || ObjectUtil.isNull(entryDate) || ObjectUtil.isNull(dispatchDate)) {
            log.info("getFixedLeaveQuota params is empty. id：{}, entryDate：{}, dispatchDate：{}", id, entryDate, dispatchDate);
            return targetMap;
        }

        // 获取国家假期配置
        CompanyLeaveConfigDO companyLeaveConfig = companyLeaveConfigService.getById(id);
        if (ObjectUtil.isNull(companyLeaveConfig)) {
            log.info("companyLeaveConfig is empty. id：{}", id);
            return targetMap;
        }

        // 获取假期发放记录
        UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
        userLeaveDetailQuery.setUserId(userInfo.getId());
        userLeaveDetailQuery.setConfigId(companyLeaveConfig.getId());
        // 查询激活以及禁用的假期
        userLeaveDetailQuery.setStatus(StatusEnum.ACTIVE.getCode());
        // 获取该国家所有员工拥有的假期
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);

        // 获取该国家所有员工拥有的年假假期的主键id
        List<Long> leaveIdList = userLeaveDetailDOList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        // 获取该国家所有员工拥有的生效的并且未结转假期详情数据
        UserLeaveStageDetailQuery userLeaveStageDetailQuery = new UserLeaveStageDetailQuery();
        userLeaveStageDetailQuery.setIsInvalid(WhetherEnum.NO.getKey());
        userLeaveStageDetailQuery.setLeaveMark(WhetherEnum.NO.getKey());
        userLeaveStageDetailQuery.setLeaveIdList(leaveIdList);
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByCondition(userLeaveStageDetailQuery);

        List<Long> leaveId = Lists.newArrayList(id);
        // 获取假期详情表数据
        List<CompanyLeaveItemConfigDO> companyLeaveItemConfig = companyLeaveConfigService.selectItemByConfigId(leaveId);
        // 获取假期发放规则
        List<CompanyLeaveConfigIssueRuleDO> companyLeaveConfigIssueRule = companyLeaveConfigIssueRuleService.selectByLeaveId(leaveId);
        if (CollUtil.isEmpty(companyLeaveConfigIssueRule)) {
            log.info("companyLeaveConfigIssueRule is empty. leaveId：{}", leaveId);
            return targetMap;
        }
        // 获取假期发放规则
        CompanyLeaveConfigIssueRuleDO companyLeaveConfigIssueRuleInfo = companyLeaveConfigIssueRule.get(0);
        Long issueRuleInfoId = companyLeaveConfigIssueRuleInfo.getId();
        List<Long> issueRuleInfoIdList = Lists.newArrayList(issueRuleInfoId);
        List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = companyLeaveConfigIssueRuleService.selectByIssueRuleId(issueRuleInfoIdList);

        Integer issueFrequency = companyLeaveConfigIssueRuleInfo.getIssueFrequency();
        Integer issueTime = companyLeaveConfigIssueRuleInfo.getIssueTime();
        Integer issueMonth = companyLeaveConfigIssueRuleInfo.getIssueMonth();
        Integer issueDay = companyLeaveConfigIssueRuleInfo.getIssueDay();
        Integer issueType = companyLeaveConfigIssueRuleInfo.getIssueType();
        Long isConvert = companyLeaveConfigIssueRuleInfo.getIsConvert();
        Integer issueRoundingRule = companyLeaveConfigIssueRuleInfo.getIssueRoundingRule();
        if (ObjectUtil.notEqual(issueFrequency, LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType())) {
            log.info("issueFrequency is not PERIODICAL_ISSUANCE. issueFrequency：{}", issueFrequency);
            return targetMap;
        }
        if (ObjectUtil.notEqual(issueTime, LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType())) {
            log.info("issueTime is not FIXED_DAY_PER_YEAR. issueTime：{}", issueTime);
            return targetMap;
        }
        if (ObjectUtil.isNull(isConvert)) {
            log.info("isConvert is empty. isConvert：{}", isConvert);
            return targetMap;
        }

        if (ObjectUtil.isNull(issueRoundingRule) || (ObjectUtil.equal(isConvert, 1) && ObjectUtil.equal(issueRoundingRule, 0))) {
            log.info("issueRoundingRule is empty");
            return targetMap;
        }
        // 入职年份
        int entryYear = DateUtil.year(entryDate);
        // 回国年份
        int dispatchYear = DateUtil.year(dispatchDate);
        // 校验入职年份跟回国年份
        if (entryYear > dispatchYear) {
            log.info("entryYear > dispatchYear");
            return targetMap;
        }
        // 获取派遣日所在年的发放日
        DateTime targetDate = DateUtil.parse(dispatchYear
                + "-" + issueMonth + "-"
                + issueDay + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        switch (issueType) {
            case 1:
            case 3:
            case 4:
                if (CollUtil.isEmpty(companyLeaveItemConfig)) {
                    log.info("companyLeaveItemConfig is empty. leaveId：{}", leaveId);
                    return targetMap;
                }

                String formatEntryDateOne = DateUtil.format(entryDate, DatePattern.NORM_DATETIME_PATTERN);
                String formatDispatchDateOne = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);
                log.info("formatEntryDateOne:{},formatDispatchDateOne:{}", formatEntryDateOne, formatDispatchDateOne);

                if (entryYear > dispatchYear) {
                    log.info("entryYear > dispatchYear");
                    return targetMap;
                }
                for (CompanyLeaveItemConfigDO companyLeaveItemConfigDO : companyLeaveItemConfig) {
                    BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                    BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                    if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                        targetMap.put(companyLeaveItemConfigDO.getStage(), leaveResidueMinutes);
                    } else {
                        // 否则每一个阶梯设置为0
                        targetMap.put(companyLeaveItemConfigDO.getStage(), BigDecimal.ZERO);
                    }
                }
                break;
            case 2:
                // 司领递增
                if (CollUtil.isEmpty(leaveConfigIssueRuleRangeList)) {
                    log.info("leaveConfigIssueRuleRangeList is empty. issueRuleInfoId：{}", issueRuleInfoId);
                    return targetMap;
                }
                Integer stage = 1;
                // 如果入职年份跟小于等于当前年份，不打折全额发放
                // 计算司龄：入职时间与现在时间之间的天数，需要加1，算上当天
                //Date now = DateUtil.date();
                // 获取入职日到派遣年所在发放日之间的天数
                long betweenDays = DateUtil.betweenDay(entryDate, targetDate, true) + 1;
                log.info("betweenDays:{}", betweenDays);

                for (CompanyLeaveConfigIssueRuleRangeDO leaveConfigIssueRuleRangeInfo : leaveConfigIssueRuleRangeList) {
                    Integer symbolLeft = leaveConfigIssueRuleRangeInfo.getSymbolLeft();
                    Integer yearLeft = leaveConfigIssueRuleRangeInfo.getYearLeft();
                    Integer symbolRight = leaveConfigIssueRuleRangeInfo.getSymbolRight();
                    Integer yearRight = leaveConfigIssueRuleRangeInfo.getYearRight();
                    BigDecimal issueQuota = leaveConfigIssueRuleRangeInfo.getIssueQuota();

                    // 将司领转换为天数，规定一年时间为365天
                    long yearLeftDays = yearLeft * 365L;
                    long yearRightDays = yearRight * 365L;

                    log.info("yearLeft == 0 || entryYearSl == dispatchYearSl");
                    // 左边是大于，右边是小于
                    if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                        // (0, yearRightDays)
                        if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                            // 获取该假期总的分钟数
                            BigDecimal leaveResidueMinutes = issueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                                targetMap.put(stage, leaveResidueMinutes);
                            } else {
                                // 否则每一个阶梯设置为0
                                targetMap.put(stage, BigDecimal.ZERO);
                            }
                        }
                    }
                    // 左边是大于，右边是小于等于
                    if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                        // (0, yearRightDays]
                        if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                            // 发放假期
                            // 获取该假期总的分钟数
                            BigDecimal leaveResidueMinutes = issueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                                targetMap.put(stage, leaveResidueMinutes);
                            } else {
                                // 否则每一个阶梯设置为0
                                targetMap.put(stage, BigDecimal.ZERO);
                            }
                        }
                    }
                    // 左边是大于等于，右边是小于
                    if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                        // [0, yearRightDays)
                        if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                            // 发放假期
                            // 获取该假期总的分钟数
                            BigDecimal leaveResidueMinutes = issueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                                targetMap.put(stage, leaveResidueMinutes);
                            } else {
                                // 否则每一个阶梯设置为0
                                targetMap.put(stage, BigDecimal.ZERO);
                            }
                        }
                    }
                    // 左边是大于等于，右边是小于等于
                    if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                        // [0, yearRightDays]
                        if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                            // 发放假期
                            // 获取该假期总的分钟数
                            BigDecimal leaveResidueMinutes = issueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                                targetMap.put(stage, leaveResidueMinutes);
                            } else {
                                // 否则每一个阶梯设置为0
                                targetMap.put(stage, BigDecimal.ZERO);
                            }
                        }
                    }
                }
                break;
            case 6:
                // 随年龄增加
                if (CollUtil.isEmpty(leaveConfigIssueRuleRangeList)) {
                    log.info("leaveConfigIssueRuleRangeList is empty. issueRuleInfoId：{}", issueRuleInfoId);
                    return targetMap;
                }
                // 获取人员对应发放余额 (dispatchDate可能传递为派遣日期也可能是回国日期)
                Date leaveDayDate = dispatchDate;
                // 根据不同场景获取不同日期
                if (!isBack) {
                    // 派遣场景
                    leaveDayDate = this.getIssueQuoteByDispatch(entryYear, dispatchYear,
                            entryDate, dispatchDate, targetDate, companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                // 回国补扣场景
                if (isBack && !isReissue) {
                    leaveDayDate = this.getIssueQuoteByBack(entryYear, dispatchYear,
                            oldDispatchDate, dispatchDate, targetDate,
                            companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                // 回国补发场景
                if (isBack && isReissue) {
                    leaveDayDate = this.getIssueQuoteByBackAndReissue(entryYear, dispatchYear,
                            entryDate, oldDispatchDate, targetDate,
                            companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                BigDecimal leaveDayByRule = this.selectLeaveDayByRule(leaveDayDate, userInfo, leaveConfigIssueRuleRangeList);
                targetMap.put(BusinessConstant.ONE, leaveDayByRule
                        .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                        .multiply(BusinessConstant.MINUTES));
                break;
            case 7:
                // 随工龄增加
                if (CollUtil.isEmpty(leaveConfigIssueRuleRangeList)) {
                    log.info("leaveConfigIssueRuleRangeList is empty. issueRuleInfoId：{}", issueRuleInfoId);
                    return targetMap;
                }
                Date workSeniorityDate = dispatchDate;
                // 根据不同场景获取不同日期
                if (!isBack) {
                    // 派遣场景
                    workSeniorityDate = this.getIssueQuoteByDispatch(entryYear, dispatchYear,
                            entryDate, dispatchDate, targetDate, companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                // 回国补扣场景
                if (isBack && !isReissue) {
                    workSeniorityDate = this.getIssueQuoteByBack(entryYear, dispatchYear,
                            oldDispatchDate, dispatchDate, targetDate,
                            companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                // 回国补发场景
                if (isBack && isReissue) {
                    workSeniorityDate = this.getIssueQuoteByBackAndReissue(entryYear, dispatchYear,
                            entryDate, oldDispatchDate, targetDate,
                            companyLeaveConfigIssueRuleInfo.getIsRecalculate());
                }
                Map userEntryMap = new HashMap<Long, Date>();
                userEntryMap.put(userInfo.getUserCode(), entryDate);
                Map<String, String> userMapForWorkSeniority
                        = this.selectWorkSeniority(Lists.newArrayList(userInfo.getUserCode())
                        , userEntryMap
                        , workSeniorityDate);
                String workSeniority = userMapForWorkSeniority.get(userInfo.getUserCode());
                // 获取人员对应发放余额
                BigDecimal workSeniorityByRule = this.selectWorkSeniorityByRule(workSeniority, leaveConfigIssueRuleRangeList);
                targetMap.put(BusinessConstant.ONE, workSeniorityByRule
                        .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                        .multiply(BusinessConstant.MINUTES));
                break;
            case 8:
                // 按省市配置
                // 获取人员对应发放余额
                if (CollUtil.isEmpty(leaveConfigIssueRuleRangeList)) {
                    log.info("leaveConfigIssueRuleRangeList is empty. issueRuleInfoId：{}", issueRuleInfoId);
                    return targetMap;
                }
                BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeList);
//                BigDecimal locationDispatchMinutes = this.getDispatchResidueMinutes(locationDayByRule, entryDate, dispatchDate, isConvert, issueRoundingRule);
                targetMap.put(BusinessConstant.ONE, locationDayByRule
                        .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                        .multiply(BusinessConstant.MINUTES));
                break;
            default:
                log.info("default");
                break;
        }
        return targetMap;
    }

    /**
     * @param userCodeToEndFlagOneListMap
     * @param endFlagOneDispatchUserRecordMap
     */
    private void handlerEndFlagDispatchUserRecordInfo(Map<String, List<DispatchUserRecordDO>> userCodeToEndFlagOneListMap, Map<String, DispatchUserRecordDO> endFlagOneDispatchUserRecordMap) {
        for (Map.Entry<String, List<DispatchUserRecordDO>> entry : userCodeToEndFlagOneListMap.entrySet()) {
            DispatchUserRecordDO dispatchUserRecord = new DispatchUserRecordDO();
            String userCode = entry.getKey();
            List<DispatchUserRecordDO> dispatchUserRecordInfoList = entry.getValue();
            XxlJobLogger.log("handlerEndFlagDispatchUserRecordInfo userCode: {},before filter dispatchUserRecordInfoList: {}", userCode, JSON.toJSONString(dispatchUserRecordInfoList));

            // 过滤掉dispatchDate为null的数据
            dispatchUserRecordInfoList = dispatchUserRecordInfoList.stream()
                    .filter(it -> ObjectUtil.isNotNull(it.getDispatchDate()))
                    .collect(Collectors.toList());

            XxlJobLogger.log("handlerEndFlagDispatchUserRecordInfo userCode: {},after filter dispatchUserRecordInfoList: {}", userCode, JSON.toJSONString(dispatchUserRecordInfoList));

            if (ObjectUtil.isEmpty(userCode)) {
                XxlJobLogger.log("error handlerEndFlagDispatchUserRecordInfo：userCode is null,skip.");
                continue;
            }
            if (CollUtil.isEmpty(dispatchUserRecordInfoList)) {
                XxlJobLogger.log("error handlerEndFlagDispatchUserRecordInfo：userCode:" + userCode + ",handlerEndFlagDispatchUserRecordInfo is empty,skip.");
                continue;
            }
            dispatchUserRecord.setUserCode(userCode);

            // 将dispatchUserRecordInfoList按照dispatchDate正序排序
            dispatchUserRecordInfoList.sort(Comparator.comparing(DispatchUserRecordDO::getDispatchDate));
            DispatchUserRecordDO firstRecord = dispatchUserRecordInfoList.get(0);
            XxlJobLogger.log("userCode:" + userCode + " dispatchUserRecordInfoList:" + dispatchUserRecordInfoList);

            if (ObjectUtil.isNotNull(firstRecord)) {
                Date dispatchDate = firstRecord.getDispatchDate();
                dispatchUserRecord.setDispatchDate(dispatchDate);
            }
            endFlagOneDispatchUserRecordMap.put(userCode, dispatchUserRecord);
        }
    }

    /**
     * 处理每年固定日按派遣日发放
     *
     * @param now
     * @param country
     * @param leaveConfigId
     * @param userEntryRecord
     * @param leaveConfigIssueRule
     * @param leaveConfigIssueRuleRangeInfoList
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param dispatchDate
     * @param date
     */
    private void handleDispatchFixedDayEveryYear(Date now,
                                                 String country,
                                                 Long leaveConfigId,
                                                 AttendanceUserEntryRecord userEntryRecord,
                                                 CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                 List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                                 CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                 List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                                 UserInfoDO userInfo,
                                                 String leaveType,
                                                 CompanyLeaveConfigDO companyLeaveConfig,
                                                 Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                                 Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                                 List<UserLeaveDetailDO> addUserLeaveDetailList,
                                                 List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                 List<UserLeaveRecordDO> addUserLeaveRecordList,
                                                 List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                                 Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                                 List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                                 Date dispatchDate,
                                                 Date date,
                                                 String workSeniority) {
        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord,
                        leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                // 获取员工入职日期
                String formatDispatchDateOne = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatDispatchDateOne);

                // 入职年份
                int dispatchYear = DateUtil.year(dispatchDate);
                // 当前年份
                int nowYear = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, dispatchYear, nowYear);

                if (dispatchYear > nowYear) {
                    XxlJobLogger.log("当前用户：{},error：当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (dispatchYear < nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantDispatchHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (dispatchYear == nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDisPatchDiscountHolidays(leaveConfigIssueRule, dispatchDate, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                break;
            case 2:
                // 司领递增
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                if (ObjectUtil.isNull(userEntryRecord) || ObjectUtil.isNull(userEntryRecord.getConfirmDate())) {
                    XxlJobLogger.log("error：dispatch 当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工入职记录信息为空或员工确认入职字段为null，跳过。"
                            , country, leaveConfigId, leaveType);
                    return;
                }
                // 获取员工入职日期
                Date entryDateTwo = userEntryRecord.getConfirmDate();
                String formatEntryDateTwo = DateUtil.format(entryDateTwo, DatePattern.NORM_DATETIME_PATTERN);
                String formatDispatchDateTwo = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{},派遣日期: {}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateTwo, formatDispatchDateTwo);

                handleDispatchSlHolidays(country, leaveConfigId, leaveConfigIssueRuleRangeInfoList, entryDateTwo
                        , dispatchDate, now, isConvert, issueRoundingRule, userInfo, userEntryRecord
                        , leaveType, companyLeaveConfig, leaveConfigIssueRule
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 3:
                // 不限定额度
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                // 获取员工入职日期
                String formatDispatchDateThree = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatDispatchDateThree);

                // 入职年份
                int dispatchYearThree = DateUtil.year(dispatchDate);
                // 当前年份
                int nowYearThree = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, dispatchYearThree, nowYearThree);

                if (dispatchYearThree > nowYearThree) {
                    XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (dispatchYearThree < nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantDispatchHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (dispatchYearThree == nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDisPatchDiscountHolidays(leaveConfigIssueRule, dispatchDate, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, date);
                }
                break;
            case 4:
                // 无初始额度

                // 不需要使用入职日期，与是否打折两个字段，所以就使用原来的方法
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},无初始额度，全额发放。"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                grantDispatchHolidays(leaveConfigIssueRule
                        , userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                        , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                        , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 6:
                // 随年龄增加
                if (!this.checkAgeGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal leaveDayByRule = this.selectLeaveDayByRule(now, userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        leaveDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 7:
                // 随工龄增加
                if (!this.checkWorkSeniorityGrant(userInfo, companyLeaveConfig
                        , workSeniority, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal workSeniorityByRule = this.selectWorkSeniorityByRule(workSeniority, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        workSeniorityByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 8:
                // 按省市配置
                if (!this.checkLocationGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        locationDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            default:
                break;
        }
    }


    /**
     * @param now
     * @param country
     * @param leaveConfigId
     * @param userEntryRecord
     * @param leaveConfigIssueRule
     * @param leaveConfigIssueRuleRangeInfoList
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param dispatchDate
     * @param date
     */
    private void handleEndFlagOneDispatchFixedDayEveryYear(Date now, String country, Long leaveConfigId,
                                                           AttendanceUserEntryRecord userEntryRecord,
                                                           CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                           List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                                           CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                           List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                                           UserInfoDO userInfo,
                                                           String leaveType,
                                                           CompanyLeaveConfigDO companyLeaveConfig,
                                                           Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                                           Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                                           List<UserLeaveDetailDO> addUserLeaveDetailList,
                                                           List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                           List<UserLeaveRecordDO> addUserLeaveRecordList,
                                                           List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                                           Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                                           List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                                           Date dispatchDate,
                                                           Date date,
                                                           String workSeniority) {
        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                // 获取员工入职日期
                String formatDispatchDateOne = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatDispatchDateOne);

                // 入职年份
                int dispatchYear = DateUtil.year(dispatchDate);
                // 当前年份
                int nowYear = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, dispatchYear, nowYear);

                if (dispatchYear > nowYear) {
                    XxlJobLogger.log("当前用户：{},error：当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (dispatchYear < nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (dispatchYear == nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDiscountHolidays(leaveConfigIssueRule, dispatchDate, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                break;
            case 2:
                // 司领递增
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                if (ObjectUtil.isNull(userEntryRecord) || ObjectUtil.isNull(userEntryRecord.getConfirmDate())) {
                    XxlJobLogger.log("error：dispatch 当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工入职记录信息为空或员工确认入职字段为null，跳过。"
                            , country, leaveConfigId, leaveType);
                    return;
                }
                // 获取员工入职日期
                Date entryDateTwo = userEntryRecord.getConfirmDate();
                String formatEntryDateTwo = DateUtil.format(entryDateTwo, DatePattern.NORM_DATETIME_PATTERN);
                String formatDispatchDateTwo = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{},派遣日期: {}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateTwo, formatDispatchDateTwo);

                handleEndFlagOneDispatchSlHolidays(country, leaveConfigId, leaveConfigIssueRule
                        , leaveConfigIssueRuleRangeInfoList, entryDateTwo, dispatchDate, now, isConvert
                        , issueRoundingRule, userInfo, userEntryRecord
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveType, companyLeaveConfig, leaveIdToItemConfigMap
                        , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                        , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, date);
                break;
            case 3:
                // 不限定额度
                // 校验派遣日期、是否结转、取整规则是否为空
                if (checkDispatchParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule, dispatchDate)) {
                    return;
                }
                // 获取员工入职日期
                String formatDispatchDateThree = DateUtil.format(dispatchDate, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatDispatchDateThree);

                // 入职年份
                int dispatchYearThree = DateUtil.year(dispatchDate);
                // 当前年份
                int nowYearThree = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, dispatchYearThree, nowYearThree);

                if (dispatchYearThree > nowYearThree) {
                    XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (dispatchYearThree < nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (dispatchYearThree == nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},派遣年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDiscountHolidays(leaveConfigIssueRule, dispatchDate, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                break;
            case 4:
                // 无初始额度
                // 不需要使用入职日期，与是否打折两个字段，所以就使用原来的方法
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},无初始额度，全额发放。"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                grantHolidays(leaveConfigIssueRule
                        , userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                        , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                        , updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, date);
                break;
            case 6:
                // 随年龄增加
                if (!this.checkAgeGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal leaveDayByRule = this.selectLeaveDayByRule(now, userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        leaveDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 7:
                // 随工龄增加
                if (!this.checkWorkSeniorityGrant(userInfo, companyLeaveConfig
                        , workSeniority, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal workSeniorityByRule = this.selectWorkSeniorityByRule(workSeniority, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        workSeniorityByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 8:
                // 按省市配置
                if (!this.checkLocationGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule,
                        userInfo, userEntryRecord,
                        leaveConfigId, companyLeaveConfig, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                        addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList,
                        leaveIdToStageDetailMap, updateUserLeaveStageDetailList,
                        locationDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            default:
                break;
        }
    }

    /**
     * @param country
     * @param leaveConfigId
     * @param leaveConfigIssueRuleRangeInfoList
     * @param entryDateTwo
     * @param dispatchDate
     * @param now
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void handleEndFlagOneDispatchSlHolidays(String country, Long leaveConfigId,
                                                    CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                    List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                                    Date entryDateTwo, Date dispatchDate, Date now, Long isConvert,
                                                    Integer issueRoundingRule,
                                                    UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord,
                                                    CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                    List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                                    String leaveType,
                                                    CompanyLeaveConfigDO companyLeaveConfig,
                                                    Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                                    Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                                    List<UserLeaveDetailDO> addUserLeaveDetailList,
                                                    List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                    List<UserLeaveRecordDO> addUserLeaveRecordList,
                                                    List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                                    Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                                    List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                                    Date date) {

        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：handleDispatchSlHolidays 当前国家:{},假期主键id：{},假期类型：{},发放频次是司龄递增，额度类型是司龄递增，额度范围为空，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }
        // 入职年份
        int entryYear = DateUtil.year(entryDateTwo);
        // 派遣日期
        int dispatchYear = DateUtil.year(dispatchDate);
        // 当前年份
        int nowYear = DateUtil.year(now);

        if (dispatchYear > nowYear) {
            XxlJobLogger.log("error：handleDispatchSlHolidays 当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }
        BigDecimal percentSalary = BigDecimal.valueOf(1.00);
        Integer stage = 1;

        // 如果入职年份跟小于等于当前年份，不打折全额发放
        // 计算司龄：入职时间与现在时间之间的天数，需要加1，算上当天
        //long betweenDays = DateUtil.betweenDay(dispatchDate, now, true) + 1;
        // 计算入职时间与派遣时间之间的天数，需要加1，算上当天
        long betweenDays = DateUtil.betweenDay(entryDateTwo, dispatchDate, true) + 1;


        for (CompanyLeaveConfigIssueRuleRangeDO leaveConfigIssueRuleRangeInfo : leaveConfigIssueRuleRangeInfoList) {
            Integer symbolLeft = leaveConfigIssueRuleRangeInfo.getSymbolLeft();
            Integer yearLeft = leaveConfigIssueRuleRangeInfo.getYearLeft();
            Integer symbolRight = leaveConfigIssueRuleRangeInfo.getSymbolRight();
            Integer yearRight = leaveConfigIssueRuleRangeInfo.getYearRight();
            BigDecimal issueQuota = leaveConfigIssueRuleRangeInfo.getIssueQuota();

            // 将司领转换为天数，规定一年时间为365天
            long yearLeftDays = yearLeft * 365L;
            long yearRightDays = yearRight * 365L;

            // 特殊情况，左区间为0，且入职年份==当年，才有需要打折的情况，所以这个单独处理
            if (yearLeft == 0 || dispatchYear == nowYear) {
                // 如果左区间是0，那么就是0到yearRightDays，是需要打折的

                // 左边是大于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                    // (0, yearRightDays)
                    if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(dispatchDate, isConvert, leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                    // (0, yearRightDays]
                    if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(dispatchDate, isConvert, leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                    // [0, yearRightDays)
                    if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(dispatchDate, isConvert, leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                    // [0, yearRightDays]
                    if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(dispatchDate, isConvert, leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
            }
            // 其他情况，都是不需要打折的，全额发放，但是下面需要判断条件

            // 左边是大于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                // (0, yearRightDays)
                if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                // (0, yearRightDays]
                if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                // [0, yearRightDays)
                if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                // [0, yearRightDays]
                if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
        }


    }

    /**
     * 按派遣日处理司领递增假期
     *
     * @param country
     * @param leaveConfigId
     * @param leaveConfigIssueRuleRangeInfoList
     * @param dispatchDate
     * @param now
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void handleDispatchSlHolidays(String country,
                                          Long leaveConfigId,
                                          List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                          Date entryDateTwo,
                                          Date dispatchDate,
                                          Date now,
                                          Long isConvert,
                                          Integer issueRoundingRule,
                                          UserInfoDO userInfo,
                                          AttendanceUserEntryRecord userEntryRecord,
                                          String leaveType,
                                          CompanyLeaveConfigDO companyLeaveConfig,
                                          CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                          CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                          List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                          Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                          Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                          List<UserLeaveDetailDO> addUserLeaveDetailList,
                                          List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                          List<UserLeaveRecordDO> addUserLeaveRecordList,
                                          List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                          Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                          List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                          Date date) {

        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：handleDispatchSlHolidays 当前国家:{},假期主键id：{},假期类型：{},发放频次是司龄递增，额度类型是司龄递增，额度范围为空，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }

        // 入职年份
        int entryYear = DateUtil.year(entryDateTwo);
        // 派遣日期
        int dispatchYear = DateUtil.year(dispatchDate);
        // 当前年份
        int nowYear = DateUtil.year(now);

        if (dispatchYear > nowYear) {
            XxlJobLogger.log("error：handleDispatchSlHolidays 当前国家:{},假期主键id：{},假期类型：{},派遣年份大于当前年份，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }
        BigDecimal percentSalary = BigDecimal.valueOf(1.00);
        Integer stage = 1;

        // 如果入职年份跟小于等于当前年份，不打折全额发放
        // 计算司龄：入职时间与现在时间之间的天数，需要加1，算上当天
        //long betweenDays = DateUtil.betweenDay(dispatchDate, now, true) + 1;
        // 计算入职时间与派遣时间之间的天数，需要加1，算上当天
        long betweenDays = DateUtil.betweenDay(entryDateTwo, dispatchDate, true) + 1;


        for (CompanyLeaveConfigIssueRuleRangeDO leaveConfigIssueRuleRangeInfo : leaveConfigIssueRuleRangeInfoList) {
            Integer symbolLeft = leaveConfigIssueRuleRangeInfo.getSymbolLeft();
            Integer yearLeft = leaveConfigIssueRuleRangeInfo.getYearLeft();
            Integer symbolRight = leaveConfigIssueRuleRangeInfo.getSymbolRight();
            Integer yearRight = leaveConfigIssueRuleRangeInfo.getYearRight();
            BigDecimal issueQuota = leaveConfigIssueRuleRangeInfo.getIssueQuota();

            // 将司领转换为天数，规定一年时间为365天
            long yearLeftDays = yearLeft * 365L;
            long yearRightDays = yearRight * 365L;

            // 特殊情况，左区间为0，且入职年份==当年，才有需要打折的情况，所以这个单独处理
            if (yearLeft == 0 || dispatchYear == nowYear) {
                // 如果左区间是0，那么就是0到yearRightDays，是需要打折的

                // 左边是大于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                    // (0, yearRightDays)
                    if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig, leaveConfigIssueRule
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                    // (0, yearRightDays]
                    if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig, leaveConfigIssueRule
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                    // [0, yearRightDays)
                    if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig, leaveConfigIssueRule
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                    // [0, yearRightDays]
                    if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDispatchDiscountSlHolidays(dispatchDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig, leaveConfigIssueRule
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
            }
            // 其他情况，都是不需要打折的，全额发放，但是下面需要判断条件
            // 左边是大于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                // (0, yearRightDays)
                if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantDispatchSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                            , issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                // (0, yearRightDays]
                if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantDispatchSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                            , issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                // [0, yearRightDays)
                if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantDispatchSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                            , issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                // [0, yearRightDays]
                if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantDispatchSlHolidays(userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                            , issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
        }
    }

    /**
     * 发放每年固定日，司领递增满额发放
     *
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param bigDecimalIssueQuota
     * @param percentSalary
     * @param stage
     * @param date
     * @return
     */
    private boolean grantDispatchSlHolidays(UserInfoDO userInfo,
                                            AttendanceUserEntryRecord userEntryRecord,
                                            Long leaveConfigId,
                                            CompanyLeaveConfigDO companyLeaveConfig,
                                            CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                            List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                            Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                            List<UserLeaveDetailDO> addUserLeaveDetailList,
                                            List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                            List<UserLeaveRecordDO> addUserLeaveRecordList,
                                            List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                            Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                            List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                            BigDecimal bigDecimalIssueQuota,
                                            BigDecimal percentSalary,
                                            Integer stage,
                                            Date date) {
        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);

            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

            addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetail, leaveResidueMinutes
                    , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                    , addUserLeaveStageDetailList);

            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            // 因为目前司龄递增只有每年固定日才有，所以这边就不需要判断是每年固定日还是每月固定日了。如果后面有其他的情况，可以再加上判断

            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            log.info("grantDispatchSlHolidays：每年固定日，当前用户：{}，假期类型：{}，用户存在该假期，假期详情数据 size：{}，发放余额"
                    , userInfo.getUserCode(), leaveConfigId, CollUtil.isEmpty(userLeaveStageDetailList) ? 0 : userLeaveStageDetailList.size());

            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                    // 如果假期详情不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过
                    log.info("grantDispatchSlHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，并且存在假期详情数据，进行累加"
                            , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());

                    BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                    UserLeaveStageDetailDO UserLeaveStageDetailDO = userLeaveStageDetailList.get(0);
                    UserLeaveStageDetailDO.setLeaveResidueMinutes(UserLeaveStageDetailDO.getLeaveResidueMinutes().add(leaveResidueMinutes));
                    BaseDOUtil.fillDOUpdateByUserOrSystem(UserLeaveStageDetailDO);
                    updateUserLeaveStageDetailList.add(UserLeaveStageDetailDO);
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                    return true;
                }

                // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                log.info("grantDispatchSlHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，并且存在假期详情数据，已经发放过，跳过"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            } else {
                log.info("grantDispatchSlHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，但是为禁用状态，修改该假期类型为启用状态，重新发放余额"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                // 先删除该员工该假期类型 详情
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                //防止空指针
                userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
                // 获取该假期之前总的分钟数
                BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                // 设置假期详情为删除状态
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//                    stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                    stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
                    oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
                }
                updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
                // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
                if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                    addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
                }

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            }
        }
        return true;
    }

    /**
     * 发放每年固定日，司领递增则算发放
     *
     * @param dispatchDate
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param bigDecimalIssueQuota
     * @param percentSalary
     * @param stage
     * @param date
     * @return
     */
    private boolean grantDispatchDiscountSlHolidays(Date dispatchDate,
                                                    Long isConvert,
                                                    Integer issueRoundingRule,
                                                    UserInfoDO userInfo,
                                                    AttendanceUserEntryRecord userEntryRecord,
                                                    Long leaveConfigId,
                                                    CompanyLeaveConfigDO companyLeaveConfig,
                                                    CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                    CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                    List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                                    Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                                    List<UserLeaveDetailDO> addUserLeaveDetailList,
                                                    List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                    List<UserLeaveRecordDO> addUserLeaveRecordList,
                                                    List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                                    Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                                    List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                                    BigDecimal bigDecimalIssueQuota,
                                                    BigDecimal percentSalary,
                                                    Integer stage,
                                                    Date date) {
        if (BigDecimal.ZERO.equals(bigDecimalIssueQuota)) {
            log.info("grantSlHolidays：当前用户：{}，假期名称：{}，用户假期范围发放规则匹配余额为0"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
        }
        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);

            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            // 折扣分钟数
            leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);
            addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetail, leaveResidueMinutes
                    , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                    , addUserLeaveStageDetailList);

            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            // 因为目前司龄递增只有每年固定日才有，所以这边就不需要判断是每年固定日还是每月固定日了。如果后面有其他的情况，可以再加上判断
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            log.info("grantDispatchDiscountSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，假期详情数据 size：{}，发放余额"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName(), CollUtil.isEmpty(userLeaveStageDetailList) ? 0 : userLeaveStageDetailList.size());

            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                    // 如果假期详情不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过
                    log.info("grantDispatchDiscountSlHolidays：，当前用户：{}，假期名称：{}，用户存在该假期，并且存在假期详情数据，进行累加"
                            , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                    if (CollUtil.isNotEmpty(userLeaveStageDetailList)
                            && LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType().equals(leaveConfigIssueRule.getIssueFrequency())
                            && LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType().equals(leaveConfigIssueRule.getIssueTime())) {
                        return false;
                    }

                    BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                    // 折扣分钟数
                    leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                    UserLeaveStageDetailDO UserLeaveStageDetailDO = userLeaveStageDetailList.get(0);
                    UserLeaveStageDetailDO.setLeaveResidueMinutes(UserLeaveStageDetailDO.getLeaveResidueMinutes().add(leaveResidueMinutes));
                    BaseDOUtil.fillDOUpdateByUserOrSystem(UserLeaveStageDetailDO);
                    updateUserLeaveStageDetailList.add(UserLeaveStageDetailDO);
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                    return true;
                }
                // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 折扣分钟数
                leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);

            } else {
                log.info("grantDispatchDiscountSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，但是为禁用状态，修改该假期类型为启用状态，重新发放余额"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                // 先删除该员工该假期类型 详情
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                //防止空指针
                userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
                // 获取该假期之前总的分钟数
                BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                // 设置假期详情为删除状态
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//                    stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                    stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
                    oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
                }
                updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
                // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
                if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                    addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
                }

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 折扣分钟数
                leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            }
        }
        return true;

    }

    /**
     * 每年固定日发放派遣折算假期
     *
     * @param leaveConfigIssueRule
     * @param dispatchDate
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void grantDisPatchDiscountHolidays(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                               Date dispatchDate,
                                               Long isConvert,
                                               Integer issueRoundingRule,
                                               UserInfoDO userInfo,
                                               AttendanceUserEntryRecord userEntryRecord,
                                               Long leaveConfigId,
                                               CompanyLeaveConfigDO companyLeaveConfig,
                                               CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                               List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                               Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                               Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                               List<UserLeaveDetailDO> addUserLeaveDetailList,
                                               List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                               List<UserLeaveRecordDO> addUserLeaveRecordList,
                                               List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                               Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                               List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                               Date date) {
        // 获取该假期类型的详情
        List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig = leaveIdToItemConfigMap.get(companyLeaveConfig.getId());

        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);
            // 获取该假期总的分钟数
            BigDecimal totalMinutes = BigDecimal.ZERO;
            for (CompanyLeaveItemConfigDO companyLeaveItemConfigDO : hrmsCompanyLeaveItemConfig) {
                BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 增加折扣逻辑
                leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfigDO
                        , userLeaveDetail, leaveResidueMinutes
                        , date, userEntryRecord
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList);
            }
            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {

            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            // 如果周期性发放，发放时间如果是每年固定日的派遣假期
            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                    log.info("grantDisPatchDiscountHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                            , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                    // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                    // 获取该假期总的分钟数
                    BigDecimal totalMinutes = BigDecimal.ZERO;
                    // 遍历该国家下该假期类型的详情
                    for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                        // 获取该阶段假期天数
                        BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                        BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                        // 增加折扣逻辑
                        leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                        totalMinutes = totalMinutes.add(leaveResidueMinutes);
                        // 新增假期详情信息
                        addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                , userLeaveDetailDO, leaveResidueMinutes
                                , date, userEntryRecord
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList);
                    }
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);

                } else {
                    log.info("grantDisPatchDiscountHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的派遣假期，所以会累加余额"
                            , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());

                    // 获取该假期总的分钟数
                    BigDecimal totalMinutes = BigDecimal.ZERO;
                    List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                    // 将stageDetail 按照薪资比率降序
                    stageDetail.sort(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed());
                    // 将hrmsCompanyLeaveItemConfig 按照薪资比率降序
                    hrmsCompanyLeaveItemConfig.sort(Comparator.comparing(CompanyLeaveItemConfigDO::getPercentSalary).reversed());
                    for (int i = 0; i < stageDetail.size(); i++) {
                        for (int j = 0; j < hrmsCompanyLeaveItemConfig.size(); j++) {
                            if (i == j) {
                                CompanyLeaveItemConfigDO companyLeaveItemConfig = hrmsCompanyLeaveItemConfig.get(j);
                                UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                                BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                // 增加折扣逻辑
                                leaveResidueMinutes = convertLeaveResidueDay(dispatchDate, isConvert, issueRoundingRule, leaveResidueMinutes);
                                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                                updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                            }
                        }
                    }
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                }
            } else {
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                log.info("grantDisPatchDiscountHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是禁用状态，需要启用该假期，再重新发放余额"
                        , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                userLeaveDisabledDiscountOperator(dispatchDate, isConvert, issueRoundingRule
                        , userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList
                        , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                        , userLeaveDetailDO, hrmsCompanyLeaveItemConfig, date);
            }
        }
    }

    /**
     * 每年固定日派遣假期发放
     *
     * @param leaveConfigIssueRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void grantDispatchHolidays(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                       UserInfoDO userInfo,
                                       AttendanceUserEntryRecord userEntryRecord,
                                       Long leaveConfigId,
                                       CompanyLeaveConfigDO companyLeaveConfig,
                                       CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                       List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                       Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                       Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                       List<UserLeaveDetailDO> addUserLeaveDetailList,
                                       List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                       List<UserLeaveRecordDO> addUserLeaveRecordList,
                                       List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                       Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                       List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                       Date date) {

        // 获取该假期类型的详情
        List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig = leaveIdToItemConfigMap.get(leaveConfigId);

        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);
            // 获取该假期总的分钟数
            BigDecimal totalMinutes = BigDecimal.ZERO;
            for (CompanyLeaveItemConfigDO companyLeaveItemConfigDO : hrmsCompanyLeaveItemConfig) {
                BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfigDO
                        , userLeaveDetail, leaveResidueMinutes
                        , date, userEntryRecord
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList);
            }
            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());

            // 如果周期性发放，发放时间如果是每年固定日
            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                log.info("grantDispatchHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态"
                        , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());

                // 如果之前假期记录是激活状态
                if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                    log.info("grantDispatchHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                            , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                    // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                    // 获取该假期总的分钟数
                    BigDecimal totalMinutes = BigDecimal.ZERO;
                    // 遍历该国家下该假期类型的详情
                    for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                        // 获取该阶段假期天数
                        BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                        BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                        totalMinutes = totalMinutes.add(leaveResidueMinutes);
                        // 新增假期详情信息
                        addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                , userLeaveDetailDO, leaveResidueMinutes
                                , date, userEntryRecord
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList);
                    }
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                } else {
                    log.info("grantDispatchHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的派遣假期，所以会累加余额"
                            , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                    // 获取该假期总的分钟数
                    BigDecimal totalMinutes = BigDecimal.ZERO;

                    // 并且用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，因为是每月发放的，所以进行更新假期详情
                    List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                    // 将stageDetail 按照薪资比率降序
                    stageDetail.sort(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed());
                    // 将hrmsCompanyLeaveItemConfig 按照薪资比率降序
                    hrmsCompanyLeaveItemConfig.sort(Comparator.comparing(CompanyLeaveItemConfigDO::getPercentSalary).reversed());
                    for (int i = 0; i < stageDetail.size(); i++) {
                        for (int j = 0; j < hrmsCompanyLeaveItemConfig.size(); j++) {
                            if (i == j) {
                                CompanyLeaveItemConfigDO companyLeaveItemConfig = hrmsCompanyLeaveItemConfig.get(j);
                                UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                                BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                                updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                            }
                        }
                    }

                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                }
            } else {
                log.info("grantDispatchHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期被禁用，需要启用该假期，再重新发放余额"
                        , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDisabledOperator(userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList
                        , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig, date);
            }
        }

    }

    /**
     * 校验派遣日期参数
     *
     * @param country
     * @param leaveId
     * @param userEntryRecord
     * @param leaveType
     * @param isConvert
     * @param issueRoundingRule
     * @param dispatchDate
     * @return
     */
    private boolean checkDispatchParam(String country, Long leaveId, AttendanceUserEntryRecord userEntryRecord, String leaveType, Long isConvert, Integer issueRoundingRule, Date dispatchDate) {
        if (ObjectUtil.isNull(dispatchDate)) {
            XxlJobLogger.log("error：dispatch 当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工派遣日期为null，跳过。", country, leaveId, leaveType);
            return true;
        }

        if (ObjectUtil.isNull(isConvert)) {
            XxlJobLogger.log("error：dispatch 当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，是否打折未配置，跳过。", country, leaveId, leaveType);
            return true;
        }

        if (ObjectUtil.isNull(issueRoundingRule) || (ObjectUtil.equal(isConvert, 1) && ObjectUtil.equal(issueRoundingRule, 0))) {
            XxlJobLogger.log("error：dispatch 当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，取整规则未配置或设置了打折未配置取整规则，跳过。", country, leaveId, leaveType);
            return true;
        }
        return false;
    }

    /**
     * 处理发放，按派遣日发放
     *
     * @param now
     * @param country
     * @param leaveConfigId
     * @param userEntryRecord
     * @param leaveConfigIssueRule
     * @param leaveConfigIssueRuleRangeInfoList
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     */
    private void handleByDispatchDateEveryFull(Date now,
                                               String country,
                                               Long leaveConfigId,
                                               AttendanceUserEntryRecord userEntryRecord,
                                               CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                               List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                               CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                               List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                               UserInfoDO userInfo,
                                               String leaveType,
                                               CompanyLeaveConfigDO companyLeaveConfig,
                                               Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                               Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                               List<UserLeaveDetailDO> addUserLeaveDetailList,
                                               List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                               List<UserLeaveRecordDO> addUserLeaveRecordList,
                                               List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                               Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                               List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                               List<CompanyLeaveJourneyConfigDO> companyLeaveJourneyConfigRangInfoList,
                                               DispatchUserRecordDO dispatchUserRecord,
                                               Date date) {
        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                grantHolidaysEveryFull(leaveConfigIssueRule, userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 2:
                // 司领递增【这次不放开，后面如果需要，这个代码就可以使用】，追加：派遣日每满应该不存在这种情况
                XxlJobLogger.log("error：当前用户：{},当前国家：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是司领递增【应该】，属于配置错误，跳过。", userInfo.getUserCode(), country, leaveType);
                break;
            case 3:
                // 不限定额度
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是不限定额度，属于配置错误，跳过。", userInfo.getUserCode(), leaveType);

                break;
            case 4:
                // 无初始额度
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是无初始额度，属于配置错误，跳过。", userInfo.getUserCode(), leaveType);

                break;
            case 5:
                // 按派遣国远近
                // 获取应该发放假期额度
                if (ObjectUtil.isNull(dispatchUserRecord)) {
                    XxlJobLogger.log("error：当前用户：{},当前国家：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是按派遣国远近，但是没有获取到派遣记录，跳过。", userInfo.getUserCode(), country, leaveType);
                    return;
                }
                String dispatchCountry = dispatchUserRecord.getDispatchCountry();
                if (ObjectUtil.isEmpty(dispatchCountry)) {
                    XxlJobLogger.log("error：当前用户：{},当前国家：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是按派遣国远近，但是没有获取到派遣国家，跳过。", userInfo.getUserCode(), country, leaveType);
                    return;
                }
                if (CollUtil.isEmpty(companyLeaveJourneyConfigRangInfoList)) {
                    XxlJobLogger.log("error：当前用户：{},当前国家：{},假期类型：{},发放频次是周期性发放，发放时间是按派遣日，额度类型是按派遣国远近，但是没有获取到路途假发放规则，跳过。", userInfo.getUserCode(), country, leaveType);
                    return;
                }

                grantHolidaysByDistanceEveryFull(leaveConfigIssueRule, userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, companyLeaveJourneyConfigRangInfoList
                        , dispatchUserRecord, date);

                break;
            default:
                break;
        }
    }

    /**
     * @param leaveConfigIssueRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     */
    private void grantHolidaysByDistanceEveryFull(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                  UserInfoDO userInfo,
                                                  AttendanceUserEntryRecord userEntryRecord,
                                                  Long leaveConfigId,
                                                  CompanyLeaveConfigDO companyLeaveConfig,
                                                  CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                  List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                                  Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                                  Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                                  List<UserLeaveDetailDO> addUserLeaveDetailList,
                                                  List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                                  List<UserLeaveRecordDO> addUserLeaveRecordList,
                                                  List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                                  Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                                  List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                                  List<CompanyLeaveJourneyConfigDO> companyLeaveJourneyConfigRangInfoList,
                                                  DispatchUserRecordDO dispatchUserRecord,
                                                  Date date) {
        // 按入职日每满发放
        Integer isSalary = companyLeaveConfig.getIsSalary();
        BigDecimal percentSalary = BigDecimal.valueOf(LeaveConfigIsSalary.UNPAID_LEAVE.getType().equals(isSalary)
                ? 0 : 1);
        Integer stage = 1;
        // 根据额度类型配置获取这个人员应该发放多少派遣假期
        BigDecimal bigDecimalIssueQuota = null;
        if (CollUtil.isEmpty(companyLeaveJourneyConfigRangInfoList)) {
            XxlJobLogger.log("error grantHolidaysByDistanceEveryFull：当前用户：{},当前国家：{},发放类型是按派遣国远近，但是没有配置该假期下的路途假发放规则，跳过。"
                    , userInfo.getUserCode(), companyLeaveConfig.getCountry());
            return;
        }
        // 获取该用户的派遣国
        String dispatchCountry = dispatchUserRecord.getDispatchCountry();
        // 获取路途假期额度
        for (CompanyLeaveJourneyConfigDO configDO : companyLeaveJourneyConfigRangInfoList) {
            String country = configDO.getCountry();
            // 将country按照，逗号分割，然后获取第一个
            if (ObjectUtil.isNotEmpty(country)) {
                String[] countryArr = country.split(",");
                // 遍历countryArr，找到与dispatchCountry相同的数据
                if (countryArr.length > 0) {
                    List<String> countryList = Arrays.stream(countryArr).collect(Collectors.toList());
                    if (countryList.contains(dispatchCountry)) {
                        // 获取该用户的路途假期额度
                        bigDecimalIssueQuota = configDO.getJourneyDays();
                        break;
                    }
                }
            }
        }

        if (ObjectUtil.isNull(bigDecimalIssueQuota)) {
            XxlJobLogger.log("error grantHolidaysByDistanceEveryFull：当前用户：{},当前国家：{},发放类型是按派遣国远近，但是没有获取到该人员所在派遣地的配置额度，跳过。"
                    , userInfo.getUserCode(), companyLeaveConfig.getCountry());
            return;
        }
        grantGeneralHolidays(userInfo, userEntryRecord,
                leaveConfigId, companyLeaveConfig,
                leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                updateUserLeaveStageDetailList, bigDecimalIssueQuota, percentSalary, stage, date);
    }


    /**
     * 处理人员派遣信息记录
     *
     * @param userCodeToAttendanceDispatchUserRecordInfoMap 原数据
     * @param dispatchUserRecordMap                         目标数据
     */
    private void handlerDispatchUserRecordInfo(Map<String, List<DispatchUserRecordDO>> userCodeToAttendanceDispatchUserRecordInfoMap,
                                               Map<String, DispatchUserRecordDO> dispatchUserRecordMap) {
        for (Map.Entry<String, List<DispatchUserRecordDO>> entry : userCodeToAttendanceDispatchUserRecordInfoMap.entrySet()) {
            DispatchUserRecordDO dispatchUserRecord = new DispatchUserRecordDO();
            String userCode = entry.getKey();
            List<DispatchUserRecordDO> dispatchUserRecordInfoList = entry.getValue();
            XxlJobLogger.log("handlerDispatchUserRecordInfo userCode: {} before filter dispatchUserRecordInfoList: {}", userCode, JSON.toJSONString(dispatchUserRecordInfoList));

            // 过滤掉dispatchDate为null的数据
            dispatchUserRecordInfoList = dispatchUserRecordInfoList.stream()
                    .filter(it -> ObjectUtil.isNotNull(it.getDispatchDate()))
                    .collect(Collectors.toList());

            XxlJobLogger.log("handlerDispatchUserRecordInfo userCode: {} after filter dispatchUserRecordInfoList: {}", userCode, JSON.toJSONString(dispatchUserRecordInfoList));

            if (ObjectUtil.isEmpty(userCode)) {
                XxlJobLogger.log("error handlerDispatchUserRecordInfo：userCode is null,skip.");
                continue;
            }
            if (CollUtil.isEmpty(dispatchUserRecordInfoList)) {
                XxlJobLogger.log("error handlerDispatchUserRecordInfo：userCode:" + userCode + ",dispatchUserRecordInfoList is empty,skip.");
                continue;
            }
            dispatchUserRecord.setUserCode(userCode);

            // 将dispatchUserRecordInfoList正序排序
            dispatchUserRecordInfoList.sort(Comparator.comparing(DispatchUserRecordDO::getDispatchDate));
            DispatchUserRecordDO firstRecord = dispatchUserRecordInfoList.get(0);
            DispatchUserRecordDO endRecord = dispatchUserRecordInfoList.get(dispatchUserRecordInfoList.size() - 1);
            XxlJobLogger.log("userCode:" + userCode + " dispatchUserRecordInfoList:" + dispatchUserRecordInfoList);

            if (ObjectUtil.isNotNull(firstRecord)) {
                Date dispatchDate = firstRecord.getDispatchDate();
                dispatchUserRecord.setDispatchDate(dispatchDate);
            }

            if (ObjectUtil.isNotNull(endRecord)) {
                String dispatchCountry = endRecord.getDispatchCountry();
                dispatchUserRecord.setDispatchCountry(dispatchCountry);
            }
            dispatchUserRecordMap.put(userCode, dispatchUserRecord);
        }
    }

    /**
     * 是否发放
     *
     * @param issueEveryFullUnit 发放时间单位
     * @param baseDate           基准时间
     * @param dateTime           当前时间
     * @param issueEveryFull     发放点
     * @return true/false
     */
    private boolean isIssueFlag(Integer issueEveryFullUnit, Date baseDate, Date dateTime, Integer issueEveryFull) {
        // 设置DateField
        DateField dateField = null;
        switch (issueEveryFullUnit) {
            case 1:
                dateField = DateField.YEAR;
                break;
            case 2:
                dateField = DateField.MONTH;
                break;
            default:
                break;
        }
        List<Date> dateList = calculateCyclePointsPlus(baseDate, dateTime, issueEveryFull, dateField);
        return dateList.stream().anyMatch(date -> DateUtil.isSameDay(date, dateTime));
    }

    /**
     * 通用的计算周期点列表方法
     *
     * @param startDate   开始日期（如入职日期）
     * @param currentDate 当前日期或截止日期
     * @param cycleLength 周期长度（如每满 1 年、2 月、3 周等）
     * @param dateField   周期类型（如年、月、周）
     * @return 周期点列表
     */
    public List<Date> calculateCyclePointsPlus(Date startDate, Date currentDate, int cycleLength, DateField dateField) {
        List<Date> cyclePoints = new ArrayList<>();
        Date cyclePoint = startDate;

        while (cyclePoint.before(currentDate) || DateUtil.isSameDay(cyclePoint, currentDate)) {
            // 当天不需要
            //cyclePoints.add(cyclePoint);

            // 根据周期类型和周期长度，计算下一个周期点
            Date nextCyclePoint = DateUtil.offset(cyclePoint, dateField, cycleLength);

            // 特殊处理：如果是按月或按年，调整到与入职日的"日"对齐
            if (dateField == DateField.MONTH || dateField == DateField.YEAR) {
                // 获取入职日的"日"部分
                int dayOfMonth = DateUtil.dayOfMonth(startDate);

                // 调整到目标日期，如果超出目标月的最大天数，则设置为该月最后一天
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(nextCyclePoint);

                int maxDayOfMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
                if (dayOfMonth > maxDayOfMonth) {
                    calendar.set(Calendar.DAY_OF_MONTH, maxDayOfMonth);
                } else {
                    calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
                }
                nextCyclePoint = calendar.getTime();
            }
            // 将下一个周期点加入到周期点列表中
            cyclePoints.add(nextCyclePoint);
            cyclePoint = nextCyclePoint;
        }

        return cyclePoints;
    }

    /**
     * @param now
     * @param country
     * @param leaveConfigId
     * @param userEntryRecord
     * @param leaveConfigIssueRule
     * @param leaveConfigIssueRuleRangeInfoList
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void handleByEntryDateEveryFull(Date now,
                                            String country,
                                            Long leaveConfigId,
                                            AttendanceUserEntryRecord userEntryRecord,
                                            CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                            List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                            CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                            List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                            UserInfoDO userInfo,
                                            String leaveType,
                                            CompanyLeaveConfigDO companyLeaveConfig,
                                            Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                            Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                            List<UserLeaveDetailDO> addUserLeaveDetailList,
                                            List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                            List<UserLeaveRecordDO> addUserLeaveRecordList,
                                            List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                            Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                            List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                            Date date,
                                            String workSeniority) {
        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                grantHolidaysEveryFull(leaveConfigIssueRule, userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap
                        , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                        , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, date);
                break;
            case 2:
                // 司龄递增
                // 校验入职日期、是否结转、取整规则是否为空
                if (checkParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule)) {
                    return;
                }
                // 获取员工入职日期
                Date entryDateTwo = userEntryRecord.getConfirmDate();
                String formatEntryDateTwo = DateUtil.format(entryDateTwo, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateTwo);

                handleSlHolidays(country, leaveConfigId
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , entryDateTwo, now, isConvert, issueRoundingRule
                        , userInfo, userEntryRecord
                        , leaveType, companyLeaveConfig
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                        , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                        , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 3:
                // 不限定额度
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是按入职日，额度类型是不限定额度，属于配置错误，跳过。", userInfo.getUserCode(), leaveType);

                break;
            case 4:
                // 无初始额度
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是按入职日，额度类型是无初始额度，属于配置错误，跳过。", userInfo.getUserCode(), leaveType);

                break;
            case 5:
                // 按派遣国远近
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是按入职日，额度类型是按派遣国远近，属于配置错误，跳过。", userInfo.getUserCode(), leaveType);

                break;
            case 6:
                // 随年龄增加
                if (!this.checkAgeGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal leaveDayByRule = this.selectLeaveDayByRule(now, userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantSlHolidays(userInfo, userEntryRecord,
                        leaveConfigId, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        companyLeaveConfig,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList,
                        addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                        updateUserLeaveStageDetailList, leaveDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 7:
                // 随工龄增加
                if (!this.checkWorkSeniorityGrant(userInfo, companyLeaveConfig
                        , workSeniority, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal workSeniorityByRule = this.selectWorkSeniorityByRule(workSeniority, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantSlHolidays(userInfo, userEntryRecord,
                        leaveConfigId, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        companyLeaveConfig,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList,
                        addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                        updateUserLeaveStageDetailList, workSeniorityByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            case 8:
                // 按省市配置
                if (!this.checkLocationGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                grantSlHolidays(userInfo, userEntryRecord,
                        leaveConfigId, leaveConfigIssueRule,
                        leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                        companyLeaveConfig,
                        leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList,
                        addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                        updateUserLeaveStageDetailList, locationDayByRule, BigDecimal.ONE, BusinessConstant.ONE, date);
                break;
            default:
                break;
        }
    }

    /**
     * @param leaveConfigIssueRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void grantHolidaysEveryFull(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                        UserInfoDO userInfo,
                                        AttendanceUserEntryRecord userEntryRecord,
                                        Long leaveConfigId,
                                        CompanyLeaveConfigDO companyLeaveConfig,
                                        CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                        List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                        Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                        Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                        List<UserLeaveDetailDO> addUserLeaveDetailList,
                                        List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                        List<UserLeaveRecordDO> addUserLeaveRecordList,
                                        List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                        Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                        List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                        Date date) {
        // 按入职日每满发放
        Integer isSalary = companyLeaveConfig.getIsSalary();
        BigDecimal percentSalary = BigDecimal.valueOf(LeaveConfigIsSalary.UNPAID_LEAVE.getType().equals(isSalary)
                ? 0 : 1);
        Integer stage = 1;
        // 额度issueQuota字段需求换类型
        BigDecimal issueQuota = leaveConfigIssueRule.getIssueQuota();

        grantGeneralHolidays(userInfo, userEntryRecord,
                leaveConfigId, companyLeaveConfig,
                leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList,
                addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date);
    }

    /**
     * 按页面额度取值
     *
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param bigDecimalIssueQuota
     * @param percentSalary
     * @param stage
     * @param date
     */
    private void grantGeneralHolidays(UserInfoDO userInfo,
                                      AttendanceUserEntryRecord userEntryRecord,
                                      Long leaveConfigId,
                                      CompanyLeaveConfigDO companyLeaveConfig,
                                      CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                      List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                      Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                      List<UserLeaveDetailDO> addUserLeaveDetailList,
                                      List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                      List<UserLeaveRecordDO> addUserLeaveRecordList,
                                      List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                      Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                      List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                      BigDecimal bigDecimalIssueQuota,
                                      BigDecimal percentSalary,
                                      Integer stage,
                                      Date date) {

        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);

            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

            addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetail, leaveResidueMinutes
                    , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                    , addUserLeaveStageDetailList);

            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            // 按入职日、派遣每满的情况，只会存在一个阶梯，要么全薪要么无薪，所以直接更新即可
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            log.info("grantGeneralHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，假期详情数据 size：{}，发放余额"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName(), CollUtil.isEmpty(userLeaveStageDetailList) ? 0 : userLeaveStageDetailList.size());

            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                    // 如果假期详情不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过
                    log.info("grantGeneralHolidays：周期性发放，入职日、派遣日每满发放，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每月固定日发放的，所以会累加余额"
                            , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                    BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                    UserLeaveStageDetailDO UserLeaveStageDetailDO = userLeaveStageDetailList.get(0);
                    UserLeaveStageDetailDO.setLeaveResidueMinutes(UserLeaveStageDetailDO.getLeaveResidueMinutes().add(leaveResidueMinutes));
                    BaseDOUtil.fillDOUpdateByUserOrSystem(UserLeaveStageDetailDO);
                    updateUserLeaveStageDetailList.add(UserLeaveStageDetailDO);
                    // 新增销假操作记录
                    addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                    return;
                }

                // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                log.info("grantGeneralHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，并且存在假期详情数据，已经发放过，跳过"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            } else {
                log.info("grantGeneralHolidays：每年固定日，当前用户：{}，假期名称：{}，用户存在该假期，但是为禁用状态，修改该假期类型为启用状态，重新发放余额"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                // 先删除该员工该假期类型 详情
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                //防止空指针
                userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
                // 获取该假期之前总的分钟数
                BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                // 设置假期详情为删除状态
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//                    stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                    stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
                    oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
                }
                updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
                // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
                if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                    addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
                }

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            }
        }
    }

    /**
     * 处理每月固定日发放
     *
     * @param leaveConfigIssueRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     */
    private void handleFixedDayEveryMonth(Date now, String country, Long leaveConfigId, AttendanceUserEntryRecord userEntryRecord,
                                          CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                          List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                          CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                          List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                          UserInfoDO userInfo, String leaveType, CompanyLeaveConfigDO companyLeaveConfig,
                                          Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                          Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                          List<UserLeaveDetailDO> addUserLeaveDetailList,
                                          List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                          List<UserLeaveRecordDO> addUserLeaveRecordList,
                                          List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                          Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                          List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                          Date date) {
        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                //grantHolidays(leaveConfigIssueRule, userInfo, leaveType, companyLeaveConfig, leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                // 每月固定日额度数据来源替换为页面额度数据来源
                grantHolidaysEveryFull(leaveConfigIssueRule, userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);

                break;
            case 2:
                // 司领递增【这次不放开，后面如果需要，这个代码就可以使用】
                XxlJobLogger.log("error：当前用户：{},当前国家：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，额度类型是司领递增【应该】，属于配置错误，跳过。", userInfo.getUserCode(), country, leaveType);

                // 校验入职日期、是否结转、取整规则是否为空
                if (checkParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule)) {
                    return;
                }
                // 获取员工入职日期
                Date entryDateTwo = userEntryRecord.getConfirmDate();
                String formatEntryDateTwo = DateUtil.format(entryDateTwo, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateTwo);

                handleSlHolidays(country, leaveConfigId
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , entryDateTwo, now, isConvert, issueRoundingRule
                        , userInfo, userEntryRecord, leaveType, companyLeaveConfig
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 3:
                // 不限定额度
                XxlJobLogger.log("error：当前用户：{},假期类型：{},发放频次是周期性发放，发放时间是每月固定日，额度类型是不限定额度，属于配置错误，跳过。"
                        , userInfo.getUserCode(), leaveType);

                break;
            case 4:
                // 无初始额度
                break;
            default:
                break;
        }
    }

    /**
     * 处理每年固定日发放
     *
     * @param country
     * @param leaveConfigId
     * @param userEntryRecord
     * @param leaveConfigIssueRule
     * @param leaveConfigIssueRuleRangeInfoList
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void handleFixedDayEveryYear(Date now, String country, Long leaveConfigId, AttendanceUserEntryRecord userEntryRecord,
                                         CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                         List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                         CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                         List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                         UserInfoDO userInfo,
                                         String leaveType, CompanyLeaveConfigDO companyLeaveConfig,
                                         Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                         Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                         List<UserLeaveDetailDO> addUserLeaveDetailList,
                                         List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                         List<UserLeaveRecordDO> addUserLeaveRecordList,
                                         List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                         Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                         List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                         Date date, String workSeniority) {

        Long isConvert = leaveConfigIssueRule.getIsConvert();
        Integer issueRoundingRule = leaveConfigIssueRule.getIssueRoundingRule();
        // 根据发放规则里面的额度类型，进行额度发放
        switch (leaveConfigIssueRule.getIssueType()) {
            case 1:
                // 固定额度
                // 校验入职日期、是否结转、取整规则是否为空
                if (checkParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule)) {
                    return;
                }
                // 获取员工入职日期
                Date entryDateOne = userEntryRecord.getConfirmDate();
                String formatEntryDateOne = DateUtil.format(entryDateOne, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateOne);

                // 入职年份
                int entryYear = DateUtil.year(entryDateOne);
                // 当前年份
                int nowYear = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, entryYear, nowYear);

                if (entryYear > nowYear) {
                    XxlJobLogger.log("当前用户：{},error：当前国家:{},假期主键id：{},假期类型：{},入职年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (entryYear < nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (entryYear == nowYear) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDiscountHolidays(leaveConfigIssueRule, entryDateOne, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, date);
                }
                break;
            case 2:
                // 司领递增
                // 校验入职日期、是否结转、取整规则是否为空
                if (checkParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule)) {
                    return;
                }
                // 获取员工入职日期
                Date entryDateTwo = userEntryRecord.getConfirmDate();
                String formatEntryDateTwo = DateUtil.format(entryDateTwo, DatePattern.NORM_DATETIME_PATTERN);

                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateTwo);

                handleSlHolidays(country, leaveConfigId
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , entryDateTwo, now, isConvert, issueRoundingRule
                        , userInfo, userEntryRecord, leaveType, companyLeaveConfig
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 3:
                // 不限定额度
                // 校验入职日期、是否结转、取整规则是否为空
                if (checkParam(country, leaveConfigId, userEntryRecord, leaveType, isConvert, issueRoundingRule)) {
                    return;
                }
                // 获取员工入职日期
                Date entryDateThree = userEntryRecord.getConfirmDate();
                String formatEntryDateThree = DateUtil.format(entryDateThree, DatePattern.NORM_DATETIME_PATTERN);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateThree);

                // 入职年份
                int entryYearThree = DateUtil.year(entryDateThree);
                // 当前年份
                int nowYearThree = DateUtil.year(now);
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份：{},当前年份：{}"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType, entryYearThree, nowYearThree);

                if (entryYearThree > nowYearThree) {
                    XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},入职年份大于当前年份，跳过。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    return;
                }
                // 如果入职年份跟小于当前年份，不打折全额发放
                if (entryYearThree < nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份小于当前年份，全额发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    // 如果入职年份和当前年份不相等，直接发放
                    grantHolidays(leaveConfigIssueRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, date);
                }
                // 如果入职年份和当前年份相等，需要折扣
                // 2022年入职、当年是
                if (entryYearThree == nowYearThree) {
                    XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份等于当前年份，折扣发放。"
                            , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                    grantDiscountHolidays(leaveConfigIssueRule, entryDateThree, isConvert, issueRoundingRule
                            , userInfo, userEntryRecord
                            , leaveConfigId, companyLeaveConfig
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                            , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                            , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                            , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                }
                break;
            case 4:
                // 无初始额度
                // 不需要使用入职日期，与是否打折两个字段，所以就使用原来的方法
                XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},无初始额度，全额发放。"
                        , userInfo.getUserCode(), country, leaveConfigId, leaveType);
                grantHolidays(leaveConfigIssueRule
                        , userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveIdToItemConfigMap, leaveTypeToUserLeaveDetailMap
                        , addUserLeaveDetailList, addUserLeaveStageDetailList
                        , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, date);
                break;
            case 6:
                // 随年龄增加
                if (!this.checkAgeGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal leaveDayByRule = this.selectLeaveDayByRule(now, userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                handleRangeHolidays(country, leaveConfigId, leaveConfigIssueRule, leaveDayByRule
                        , userEntryRecord, now, isConvert, issueRoundingRule, userInfo
                        , leaveType, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 7:
                // 随工龄增加
                if (!this.checkWorkSeniorityGrant(userInfo, companyLeaveConfig
                        , workSeniority, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal workSeniorityByRule = this.selectWorkSeniorityByRule(workSeniority, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                handleRangeHolidays(country, leaveConfigId, leaveConfigIssueRule, workSeniorityByRule
                        , userEntryRecord, now, isConvert, issueRoundingRule, userInfo
                        , leaveType, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            case 8:
                // 按省市配置
                if (!this.checkLocationGrant(userInfo, companyLeaveConfig
                        , leaveConfigIssueRule, leaveConfigIssueRuleRangeInfoList)) {
                    return;
                }
                // 获取人员对应发放余额
                BigDecimal locationDayByRule = this.selectLocationDayByRule(userInfo, leaveConfigIssueRuleRangeInfoList);
                // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
                handleRangeHolidays(country, leaveConfigId, leaveConfigIssueRule, locationDayByRule
                        , userEntryRecord, now, isConvert, issueRoundingRule, userInfo
                        , leaveType, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, date);
                break;
            default:
                break;
        }
    }

    /**
     * 发放司龄递增假期
     *
     * @param leaveConfigIssueRuleRangeInfoList
     * @param entryDateTwo
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     */
    private void handleSlHolidays(String country, Long leaveConfigId,
                                  CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                  List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList,
                                  CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                  List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeDOList,
                                  Date entryDateTwo, Date now, Long isConvert, Integer issueRoundingRule,
                                  UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord,
                                  String leaveType, CompanyLeaveConfigDO companyLeaveConfig,
                                  Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                  Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                  List<UserLeaveDetailDO> addUserLeaveDetailList,
                                  List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                  List<UserLeaveRecordDO> addUserLeaveRecordList,
                                  List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                  Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                  List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                  Date date) {

        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是司龄递增，额度类型是司龄递增，额度范围为空，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }

        // 入职年份
        int entryYear = DateUtil.year(entryDateTwo);
        // 当前年份
        int nowYear = DateUtil.year(now);

        if (entryYear > nowYear) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},入职年份大于当前年份，跳过。"
                    , country, leaveConfigId, leaveType);
            return;
        }
        BigDecimal percentSalary = BigDecimal.valueOf(1.00);
        Integer stage = 1;

        // 如果入职年份跟小于等于当前年份，不打折全额发放
        // 计算司龄：入职时间与现在时间之间的天数，需要加1，算上当天
        long betweenDays = DateUtil.betweenDay(entryDateTwo, now, true) + 1;
        for (CompanyLeaveConfigIssueRuleRangeDO leaveConfigIssueRuleRangeInfo : leaveConfigIssueRuleRangeInfoList) {
            Integer symbolLeft = leaveConfigIssueRuleRangeInfo.getSymbolLeft();
            Integer yearLeft = leaveConfigIssueRuleRangeInfo.getYearLeft();
            Integer symbolRight = leaveConfigIssueRuleRangeInfo.getSymbolRight();
            Integer yearRight = leaveConfigIssueRuleRangeInfo.getYearRight();
            BigDecimal issueQuota = leaveConfigIssueRuleRangeInfo.getIssueQuota();

            // 将司领转换为天数，规定一年时间为365天
            long yearLeftDays = yearLeft * 365L;
            long yearRightDays = yearRight * 365L;

            // 特殊情况，左区间为0，且入职年份==当年，才有需要打折的情况，所以这个单独处理
            if (yearLeft == 0 && entryYear == nowYear) {
                // 如果左区间是0，那么就是0到yearRightDays，是需要打折的

                // 左边是大于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                    // (0, yearRightDays)
                    if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(entryDateTwo, isConvert
                                , leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord, leaveConfigId
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                    // (0, yearRightDays]
                    if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(entryDateTwo, isConvert
                                , leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord, leaveConfigId
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList
                                , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                                , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, issueQuota
                                , percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                    // [0, yearRightDays)
                    if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(entryDateTwo, isConvert
                                , leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord, leaveConfigId
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
                // 左边是大于等于，右边是小于等于
                if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                    // [0, yearRightDays]
                    if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                        // 发放假期
                        if (grantDiscountSlHolidays(entryDateTwo, isConvert
                                , leaveConfigIssueRule, issueRoundingRule
                                , userInfo, userEntryRecord, leaveConfigId
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                                , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                                , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , issueQuota, percentSalary, stage, date)) {
                            return;
                        }
                    }
                }
            }
            // 其他情况，都是不需要打折的，全额发放，但是下面需要判断条件

            // 左边是大于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 1)) {
                // (0, yearRightDays)
                if (betweenDays > yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord, leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 1) && ObjectUtil.equal(symbolRight, 2)) {
                // (0, yearRightDays]
                if (betweenDays > yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord, leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 1)) {
                // [0, yearRightDays)
                if (betweenDays >= yearLeftDays && betweenDays < yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord, leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
            // 左边是大于等于，右边是小于等于
            if (ObjectUtil.equal(symbolLeft, 2) && ObjectUtil.equal(symbolRight, 2)) {
                // [0, yearRightDays]
                if (betweenDays >= yearLeftDays && betweenDays <= yearRightDays) {
                    // 发放假期
                    if (grantSlHolidays(userInfo, userEntryRecord, leaveConfigId, leaveConfigIssueRule
                            , leaveConfigCarryOver, leaveConfigCarryOverRangeDOList
                            , companyLeaveConfig
                            , leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList
                            , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                            , updateUserLeaveStageDetailList, issueQuota, percentSalary, stage, date)) {
                        return;
                    }
                }
            }
        }
    }

    /**
     * 发放按范围递增假期
     *
     * @param country
     * @param leaveConfigId
     * @param leaveConfigIssueRule
     * @param issueQuote
     * @param userEntryRecord
     * @param now
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveType
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void handleRangeHolidays(String country, Long leaveConfigId,
                                     CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                     BigDecimal issueQuote, AttendanceUserEntryRecord userEntryRecord,
                                     Date now, Long isConvert, Integer issueRoundingRule, UserInfoDO userInfo,
                                     String leaveType, CompanyLeaveConfigDO companyLeaveConfig,
                                     CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                     List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                     Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                     List<UserLeaveDetailDO> addUserLeaveDetailList,
                                     List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                     List<UserLeaveRecordDO> addUserLeaveRecordList,
                                     List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                     Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                     List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                     Date date) {
        // 获取员工入职日期
        if (Objects.isNull(userEntryRecord)) {
            XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期名称：{}, 没有入职信息"
                    , userInfo.getUserCode(), companyLeaveConfig.getCountry()
                    , companyLeaveConfig.getId(), companyLeaveConfig.getLeaveName());
            return;
        }
        // 当前年份
        int nowYearForAge = DateUtil.year(now);
        int entryDateForAge = DateUtil.year(userEntryRecord.getConfirmDate());
        String formatEntryDateForAge = DateUtil.format(userEntryRecord.getConfirmDate(), DatePattern.NORM_DATETIME_PATTERN);

        XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职日期：{}"
                , userInfo.getUserCode(), country, leaveConfigId, leaveType, formatEntryDateForAge);

        // 发放假期(暂时使用原先司龄发放方法 后续重构抽出)
        if (entryDateForAge > nowYearForAge) {
            XxlJobLogger.log("error：当前用户：{},当前国家:{},假期主键id：{},假期类型：{},入职年份大于当前年份，跳过。"
                    , userInfo.getUserCode(), country, leaveConfigId, leaveType);
            return;
        }
        // 如果入职年份跟小于当前年份，不打折全额发放
        if (entryDateForAge < nowYearForAge) {
            XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份小于当前年份，全额发放。"
                    , userInfo.getUserCode(), country, leaveConfigId, leaveType);
            // 如果入职年份和当前年份不相等，直接发放
            grantSlHolidays(userInfo, userEntryRecord,
                    leaveConfigId, leaveConfigIssueRule,
                    leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList,
                    companyLeaveConfig,
                    leaveTypeToUserLeaveDetailMap, addUserLeaveDetailList, addUserLeaveStageDetailList,
                    addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap,
                    updateUserLeaveStageDetailList, issueQuote, BigDecimal.ONE, BusinessConstant.ONE, date);
        }
        // 如果入职年份和当前年份相等，需要折扣
        if (entryDateForAge == nowYearForAge) {
            XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期类型：{},入职年份等于当前年份，折扣发放。"
                    , userInfo.getUserCode(), country, leaveConfigId, leaveType);
            grantDiscountSlHolidays(userEntryRecord.getConfirmDate(), isConvert, leaveConfigIssueRule, issueRoundingRule
                    , userInfo, userEntryRecord
                    , leaveConfigId, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                    , companyLeaveConfig, leaveTypeToUserLeaveDetailMap
                    , addUserLeaveDetailList, addUserLeaveStageDetailList, addUserLeaveRecordList
                    , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                    , issueQuote, BigDecimal.ONE, BusinessConstant.ONE, date);
        }
    }

    /**
     * 发放全额司龄假期
     *
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param bigDecimalIssueQuota
     * @param percentSalary
     * @param stage
     * @param date
     */
    private boolean grantSlHolidays(UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord
            , Long leaveConfigId, CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule
            , CompanyLeaveConfigCarryOverDO leaveConfigCarryOver
            , List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList
            , CompanyLeaveConfigDO companyLeaveConfig
            , Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap, List<UserLeaveDetailDO> addUserLeaveDetailList
            , List<UserLeaveStageDetailDO> addUserLeaveStageDetailList, List<UserLeaveRecordDO> addUserLeaveRecordList
            , List<UserLeaveDetailDO> updateUserLeaveDetailDOList, Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap
            , List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList, BigDecimal bigDecimalIssueQuota, BigDecimal percentSalary
            , Integer stage, Date date) {
        if (BigDecimal.ZERO.equals(bigDecimalIssueQuota)) {
            log.info("grantSlHolidays：当前用户：{}，假期名称：{}，用户假期范围发放规则匹配余额为0"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
            return false;
        }
        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);

            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

            addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetail, leaveResidueMinutes
                    , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                    , addUserLeaveStageDetailList);

            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            log.info("grantSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，假期详情数据 size：{}，发放余额"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName(), CollUtil.isEmpty(userLeaveStageDetailList) ? 0 : userLeaveStageDetailList.size());

            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                log.info("grantSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，并且存在假期详情数据，已经发放过，跳过"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                if (CollUtil.isNotEmpty(userLeaveStageDetailList)
                        && LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType().equals(leaveConfigIssueRule.getIssueFrequency())
                        && LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType().equals(leaveConfigIssueRule.getIssueTime())) {
                    return false;
                }
                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                    addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                            , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                            , addUserLeaveStageDetailList);
                } else {
                    // 并且用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，因为是每月发放的，所以进行更新假期详情
                    List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                    for (int i = 0; i < stageDetail.size(); i++) {
                        UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                        userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                        updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                    }
                }
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            } else {
                log.info("grantSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，但是为禁用状态，修改该假期类型为启用状态，重新发放余额"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                // 先删除该员工该假期 详情
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                //防止空指针
                userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
                // 获取该假期之前总的分钟数
                BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                // 设置假期详情为删除状态
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//                    stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                    stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
                    oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
                }
                updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
                // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
                if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                    addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
                }

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            }
        }
        return true;
    }

    /**
     * 发放折扣司龄假期
     *
     * @param entryDateTwo
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param bigDecimalIssueQuota
     * @param percentSalary
     * @param stage
     * @return 是否跳过
     */
    private boolean grantDiscountSlHolidays(Date entryDateTwo, Long isConvert,
                                            CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                            Integer issueRoundingRule,
                                            UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord,
                                            Long leaveConfigId,
                                            CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                            List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                            CompanyLeaveConfigDO companyLeaveConfig,
                                            Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                            List<UserLeaveDetailDO> addUserLeaveDetailList,
                                            List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                            List<UserLeaveRecordDO> addUserLeaveRecordList,
                                            List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                            Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                            List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                            BigDecimal bigDecimalIssueQuota, BigDecimal percentSalary, Integer stage, Date date) {
        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);

            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            // 折扣分钟数
            leaveResidueMinutes = convertLeaveResidueDay(entryDateTwo, isConvert, issueRoundingRule, leaveResidueMinutes);

            addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetail, leaveResidueMinutes
                    , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                    , addUserLeaveStageDetailList);
            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true"
                    , date, companyLeaveConfig);
        } else {
            List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
            log.info("grantDiscountSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，假期详情数据 size：{}，发放余额"
                    , userInfo.getUserCode(), companyLeaveConfig.getLeaveName()
                    , CollUtil.isEmpty(userLeaveStageDetailList) ? 0 : userLeaveStageDetailList.size());

            if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 折扣分钟数
                leaveResidueMinutes = convertLeaveResidueDay(entryDateTwo, isConvert, issueRoundingRule, leaveResidueMinutes);

                if (CollUtil.isNotEmpty(userLeaveStageDetailList)
                        && LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType().equals(leaveConfigIssueRule.getIssueFrequency())
                        && LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType().equals(leaveConfigIssueRule.getIssueTime())) {
                    return false;
                }

                if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                    addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                            , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                            , addUserLeaveStageDetailList);
                } else {
                    // 并且用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，因为是每月发放的，所以进行更新假期详情
                    List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                    for (int i = 0; i < stageDetail.size(); i++) {
                        UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                        userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                        updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                    }
                }
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true"
                        , date, companyLeaveConfig);

            } else {
                log.info("grantDiscountSlHolidays：当前用户：{}，假期名称：{}，用户存在该假期，但是为禁用状态，修改该假期类型为启用状态，重新发放余额"
                        , userInfo.getUserCode(), companyLeaveConfig.getLeaveName());
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
                updateUserLeaveDetailDOList.add(userLeaveDetailDO);

                // 先删除该员工该假期类型 详情
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                //防止空指针
                userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
                // 获取该假期之前总的分钟数
                BigDecimal oldTotalMinutes = BigDecimal.ZERO;
                // 设置假期详情为删除状态
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//                    stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
                    stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
                    BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
                    oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
                }
                updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
                // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
                if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
                    addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
                }

                // 获取该假期总的分钟数
                BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 折扣分钟数
                leaveResidueMinutes = convertLeaveResidueDay(entryDateTwo, isConvert, issueRoundingRule, leaveResidueMinutes);

                addRangeLeaveStageDetail(companyLeaveConfig, percentSalary, stage, userLeaveDetailDO, leaveResidueMinutes
                        , userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList, date
                        , addUserLeaveStageDetailList);
                // 新增销假操作记录
                addLeaveRecord(userInfo, leaveConfigId, leaveResidueMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
            }
        }
        return true;
    }

    /**
     * 添加司龄/年龄/工龄递增假期阶段详情
     *
     * @param companyLeaveConfig
     * @param percentSalary
     * @param stage
     * @param userLeaveDetail
     * @param leaveResidueMinutes
     * @param addUserLeaveStageDetailList
     */
    private void addRangeLeaveStageDetail(CompanyLeaveConfigDO companyLeaveConfig,
                                          BigDecimal percentSalary, Integer stage,
                                          UserLeaveDetailDO userLeaveDetail,
                                          BigDecimal leaveResidueMinutes,
                                          AttendanceUserEntryRecord userEntryRecord,
                                          CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                          List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                          Date date,
                                          List<UserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        UserLeaveStageDetailDO detailDO = new UserLeaveStageDetailDO();
        detailDO.setLeaveId(userLeaveDetail.getId());
        detailDO.setLeaveResidueMinutes(leaveResidueMinutes);
        detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        detailDO.setPercentSalary(percentSalary);
        detailDO.setStage(stage);
        detailDO.setStatus(companyLeaveConfig.getStatus());
        // 设置假期详情为未结转，有效状态
        detailDO.setLeaveMark(WhetherEnum.NO.getKey());
        detailDO.setIsInvalid(WhetherEnum.NO.getKey());
        // 设置假期发放日期
        detailDO.setIssueDate(DateUtils.date2Str(date, DateFormatterUtil.FORMAT_YYYYMMDD));
        // 设置失效日期
        detailDO.setInvalidDate(companyLeaveConfigCarryOverService.getUserInvalidDate(date,
                userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList));
        BaseDOUtil.fillDOInsertByUsrOrSystem(detailDO);
        addUserLeaveStageDetailList.add(detailDO);
    }

    private static boolean checkParam(String country, Long leaveId, AttendanceUserEntryRecord userEntryRecord
            , String leaveType, Long isConvert, Integer issueRoundingRule) {
        if (ObjectUtil.isNull(userEntryRecord) || ObjectUtil.isNull(userEntryRecord.getConfirmDate())) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，员工入职记录信息为空或员工确认入职字段为null，跳过。", country, leaveId, leaveType);
            return true;
        }

        if (ObjectUtil.isNull(isConvert)) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，是否打折未配置，跳过。", country, leaveId, leaveType);
            return true;
        }

        if (ObjectUtil.isNull(issueRoundingRule) || (ObjectUtil.equal(isConvert, 1) && ObjectUtil.equal(issueRoundingRule, 0))) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期类型：{},发放频次是周期性发放，发放时间是每年固定日，取整规则未配置或设置了打折未配置取整规则，跳过。", country, leaveId, leaveType);
            return true;
        }
        return false;
    }

    /**
     * 发放折算假期
     *
     * @param entryDate
     * @param isConvert
     * @param issueRoundingRule
     * @param userInfo
     * @param leaveConfigId
     * @param companyLeaveConfig
     * @param leaveIdToItemConfigMap
     * @param leaveTypeToUserLeaveDetailMap
     * @param addUserLeaveDetailList
     * @param addUserLeaveStageDetailList
     * @param addUserLeaveRecordList
     * @param updateUserLeaveDetailDOList
     * @param leaveIdToStageDetailMap
     * @param updateUserLeaveStageDetailList
     * @param date
     */
    private void grantDiscountHolidays(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule, Date entryDate,
                                       Long isConvert, Integer issueRoundingRule,
                                       UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord,
                                       Long leaveConfigId, CompanyLeaveConfigDO companyLeaveConfig,
                                       CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                       List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                       Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                                       Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                                       List<UserLeaveDetailDO> addUserLeaveDetailList,
                                       List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                       List<UserLeaveRecordDO> addUserLeaveRecordList,
                                       List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                                       Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                                       List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                       Date date) {

        // 获取该假期类型的详情
        List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig = leaveIdToItemConfigMap.get(leaveConfigId);

        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);
            // 获取该假期总的分钟数
            BigDecimal totalMinutes = BigDecimal.ZERO;
            for (CompanyLeaveItemConfigDO companyLeaveItemConfigDO : hrmsCompanyLeaveItemConfig) {
                BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                // 增加折扣逻辑
                leaveResidueMinutes = convertLeaveResidueDay(entryDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfigDO
                        , userLeaveDetail, leaveResidueMinutes
                        , date, userEntryRecord
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList);
            }
            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())) {
                log.info("grantDiscountHolidays：一次性发放，假期名称：{}，员工code：{}，用户对假期id：{}", companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                // 一次性发放
                // 如果之前假期记录是激活状态，直接跳过
                if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                    return;
                }
                // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                userLeaveDisabledDiscountOperator(entryDate, isConvert, issueRoundingRule
                        , userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                        , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig
                        , date);
            } else {
                List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());

                Integer issueTime = leaveConfigIssueRule.getIssueTime();
                // 如果周期性发放，发放时间如果是每年固定日
                if (ObjectUtil.equal(issueTime, LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType())) {
                    if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                        if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                            // 如果假期详情不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过
                            log.info("grantDiscountHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                            return;
                        }
                        // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                        log.info("grantDiscountHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                        // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情

                        // 获取该假期总的分钟数
                        BigDecimal totalMinutes = BigDecimal.ZERO;
                        // 遍历该国家下该假期类型的详情
                        for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                            // 获取该阶段假期天数
                            BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                            BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            // 增加折扣逻辑
                            leaveResidueMinutes = convertLeaveResidueDay(entryDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                            totalMinutes = totalMinutes.add(leaveResidueMinutes);
                            // 新增假期详情信息
                            addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                    , userLeaveDetailDO, leaveResidueMinutes
                                    , date, userEntryRecord
                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                    , addUserLeaveStageDetailList);
                        }
                        // 新增销假操作记录
                        addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                    } else {
                        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                        userLeaveDisabledDiscountOperator(entryDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList, addUserLeaveRecordList
                                , updateUserLeaveDetailDOList, leaveIdToStageDetailMap, updateUserLeaveStageDetailList
                                , userLeaveDetailDO, hrmsCompanyLeaveItemConfig, date);
                    }
                } else {
                    // 如果周期性发放，发放时间如果是每月固定日
                    if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                            log.info("grantDiscountHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                            // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                            // 获取该假期总的分钟数
                            BigDecimal totalMinutes = BigDecimal.ZERO;
                            // 遍历该国家下该假期类型的详情
                            for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                                // 获取该阶段假期天数
                                BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                // 增加折扣逻辑
                                leaveResidueMinutes = convertLeaveResidueDay(entryDate, isConvert, issueRoundingRule, leaveResidueMinutes);

                                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                // 新增假期详情信息
                                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                        , userLeaveDetailDO, leaveResidueMinutes
                                        , date, userEntryRecord
                                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                        , addUserLeaveStageDetailList);
                            }
                            // 新增销假操作记录
                            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);

                        } else {
                            log.info("grantDiscountHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每月固定日发放的，所以会累加余额"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());

                            // 获取该假期总的分钟数
                            BigDecimal totalMinutes = BigDecimal.ZERO;
                            List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                            // 将stageDetail 按照薪资比率降序
                            stageDetail.sort(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed());
                            // 将hrmsCompanyLeaveItemConfig 按照薪资比率降序
                            hrmsCompanyLeaveItemConfig.sort(Comparator.comparing(CompanyLeaveItemConfigDO::getPercentSalary).reversed());
                            for (int i = 0; i < stageDetail.size(); i++) {
                                for (int j = 0; j < hrmsCompanyLeaveItemConfig.size(); j++) {
                                    if (i == j) {
                                        CompanyLeaveItemConfigDO companyLeaveItemConfig = hrmsCompanyLeaveItemConfig.get(j);
                                        UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                                        BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                        BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                        totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                        userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                                        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                                        updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                                    }
                                }
                            }
                            // 新增销假操作记录
                            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                        }
                    } else {
                        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                        log.info("grantDiscountHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是禁用状态，需要启用该假期，再重新发放余额"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                        userLeaveDisabledDiscountOperator(entryDate, isConvert, issueRoundingRule
                                , userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveDetailDOList
                                , leaveIdToStageDetailMap, updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig
                                , date);
                    }
                }
            }
        }
    }

    private void userLeaveDisabledDiscountOperator(Date entryDate, Long isConvert, Integer issueRoundingRule
            , UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord
            , Long leaveConfigId, CompanyLeaveConfigDO companyLeaveConfig
            , CompanyLeaveConfigCarryOverDO leaveConfigCarryOver
            , List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList
            , List<UserLeaveStageDetailDO> addUserLeaveStageDetailList, List<UserLeaveRecordDO> addUserLeaveRecordList
            , List<UserLeaveDetailDO> updateUserLeaveDetailDOList
            , Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap
            , List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList, UserLeaveDetailDO userLeaveDetailDO
            , List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig, Date date) {
        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
        userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
        updateUserLeaveDetailDOList.add(userLeaveDetailDO);

        // 先删除该员工该假期类型 详情
        List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
        //防止空指针
        userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
        // 获取该假期之前总的分钟数
        BigDecimal oldTotalMinutes = BigDecimal.ZERO;
        // 设置假期详情为删除状态
        for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//            stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
            stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
            BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
            oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
        }
        updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
        // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
        if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
            addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false", date, companyLeaveConfig);
        }

        // 获取该假期总的分钟数
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历该国家下该假期类型的详情
        for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
            // 获取该阶段假期天数
            BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
            BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            // 增加折扣逻辑
            leaveResidueMinutes = convertLeaveResidueDay(entryDate, isConvert, issueRoundingRule, leaveResidueMinutes);

            totalMinutes = totalMinutes.add(leaveResidueMinutes);
            // 新增假期详情信息
            addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                    , userLeaveDetailDO, leaveResidueMinutes
                    , date, userEntryRecord
                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                    , addUserLeaveStageDetailList);
        }
        // 新增销假操作记录
        addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
    }

    /**
     * 根据取整规则进行取整并折算
     *
     * @param entryDate           入职日期
     * @param isConvert           是否转换
     * @param issueRoundingRule   取整规则
     * @param leaveResidueMinutes 假期剩余分钟数
     * @return 假期剩余分钟数
     */
    private BigDecimal convertLeaveResidueDay(Date entryDate, Long isConvert, Integer issueRoundingRule, BigDecimal leaveResidueMinutes) {
        if (isConvert == 1) {
            // 获取当年总天数
            long daysOfYear = DateUtil.lengthOfYear(DateUtil.year(entryDate));

            // 获取员工入职日到年底的时间,这边需要加1天，算上当天时间
            long between = DateUtil.betweenDay(entryDate, DateUtil.endOfYear(entryDate), true) + 1;
            // 根据取证规则进行取整
            if (issueRoundingRule == 1) {
                // 四舍五入
                leaveResidueMinutes = BigDecimal.valueOf(between).multiply(leaveResidueMinutes).divide(BigDecimal.valueOf(daysOfYear), 0, RoundingMode.HALF_UP);
            } else if (issueRoundingRule == 2) {
                // 向下取整
                leaveResidueMinutes = BigDecimal.valueOf(between).multiply(leaveResidueMinutes).divide(BigDecimal.valueOf(daysOfYear), 0, RoundingMode.DOWN);
            } else if (issueRoundingRule == 3) {
                // 向上取整
                leaveResidueMinutes = BigDecimal.valueOf(between).multiply(leaveResidueMinutes).divide(BigDecimal.valueOf(daysOfYear), 0, RoundingMode.UP);
            }
        }
        return leaveResidueMinutes;
    }

    /**
     * 发放固定额度假期
     *
     * @param userInfo                       用户信息
     * @param leaveConfigId                  假期规则主键
     * @param companyLeaveConfig             假期配置
     * @param leaveIdToItemConfigMap         假期id到假期项配置的map
     * @param leaveTypeToUserLeaveDetailMap  假期类型到用户假期详情的map
     * @param addUserLeaveDetailList         新增用户假期详情
     * @param addUserLeaveStageDetailList    新增用户假期阶段详情
     * @param addUserLeaveRecordList         新增用户假期记录
     * @param updateUserLeaveDetailDOList    更新用户假期详情
     * @param leaveIdToStageDetailMap        假期id到假期阶段详情的map
     * @param updateUserLeaveStageDetailList 更新用户假期阶段详情
     */
    private void grantHolidays(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                               UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord,
                               Long leaveConfigId, CompanyLeaveConfigDO companyLeaveConfig,
                               CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                               List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                               Map<Long, List<CompanyLeaveItemConfigDO>> leaveIdToItemConfigMap,
                               Map<Long, UserLeaveDetailDO> leaveTypeToUserLeaveDetailMap,
                               List<UserLeaveDetailDO> addUserLeaveDetailList,
                               List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                               List<UserLeaveRecordDO> addUserLeaveRecordList,
                               List<UserLeaveDetailDO> updateUserLeaveDetailDOList,
                               Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap,
                               List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                               Date date) {

        // 获取该假期类型的详情
        List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig = leaveIdToItemConfigMap.get(leaveConfigId);

        // 获取该员工该假期类型的假期数据
        UserLeaveDetailDO userLeaveDetailDO = leaveTypeToUserLeaveDetailMap.get(leaveConfigId);
        if (ObjectUtil.isNull(userLeaveDetailDO)) {
            // 新增该员工该假期类型
            UserLeaveDetailDO userLeaveDetail = addLeaveDetail(userInfo, leaveConfigId, companyLeaveConfig, addUserLeaveDetailList);
            // 获取该假期总的分钟数
            BigDecimal totalMinutes = BigDecimal.ZERO;
            for (CompanyLeaveItemConfigDO companyLeaveItemConfigDO : hrmsCompanyLeaveItemConfig) {
                BigDecimal leaveResidueDay = companyLeaveItemConfigDO.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfigDO.getStartDay());
                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfigDO
                        , userLeaveDetail, leaveResidueMinutes
                        , date, userEntryRecord
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList);
            }
            // 新增操作记录
            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
        } else {
            if (ObjectUtil.equal(leaveConfigIssueRule.getIssueFrequency(), LeaveConfigIssueFrequencyEnum.ONE_TIME_ISSUANCE.getType())) {
                // 一次性发放
                // 如果之前假期记录是激活状态，直接跳过
                if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                    return;
                }

                userLeaveDisabledOperator(userInfo, userEntryRecord
                        , leaveConfigId, companyLeaveConfig
                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                        , addUserLeaveStageDetailList
                        , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                        , updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig
                        , date);
            } else {
                List<UserLeaveStageDetailDO> userLeaveStageDetailList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());

                // 非一次性发放，这里指的是周期性发放
                Integer issueTime = leaveConfigIssueRule.getIssueTime();
                // 如果周期性发放，发放时间如果是每年固定日
                if (ObjectUtil.equal(issueTime, LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType())) {
                    // 如果之前假期记录是激活状态
                    if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                        if (CollUtil.isNotEmpty(userLeaveStageDetailList)) {
                            // 如果假期详情不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过
                            log.info("grantHolidays:周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每年固定日发放的，所以会跳过"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                            return;
                        }

                        // 如果假期详情为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                        log.info("grantHolidays：周期性发放，每年固定日，假期类型：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                        // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                        // 获取该假期总的分钟数
                        BigDecimal totalMinutes = BigDecimal.ZERO;
                        // 遍历该国家下该假期类型的详情
                        for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                            // 获取该阶段假期天数
                            BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                            BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                            totalMinutes = totalMinutes.add(leaveResidueMinutes);
                            // 新增假期详情信息
                            addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                    , userLeaveDetailDO, leaveResidueMinutes
                                    , date, userEntryRecord
                                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                    , addUserLeaveStageDetailList);
                        }
                        // 新增销假操作记录
                        addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true"
                                , date, companyLeaveConfig);
                    } else {
                        log.info("grantHolidays：周期性发放，每年固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期被禁用，需要启用该假期，再重新发放余额"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                        userLeaveDisabledOperator(userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList
                                , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                                , updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig, date);
                    }
                } else {
                    // 如果周期性发放，发放时间如果是每月固定日
                    if (userLeaveDetailDO.getStatus().equals(StatusEnum.ACTIVE.getCode())) {
                        log.info("grantHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());

                        // 如果之前假期记录是激活状态
                        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
                            log.info("grantHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，但是用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                            // 并且用户假期详情表数据为空，表示用户有这个假期，但是没有假期详情（假期详情结转了），需要重新发放假期详情
                            // 获取该假期总的分钟数
                            BigDecimal totalMinutes = BigDecimal.ZERO;
                            // 遍历该国家下该假期类型的详情
                            for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
                                // 获取该阶段假期天数
                                BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                // 新增假期详情信息
                                addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                                        , userLeaveDetailDO, leaveResidueMinutes
                                        , date, userEntryRecord
                                        , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                        , addUserLeaveStageDetailList);
                            }
                            // 新增销假操作记录
                            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
                        } else {
                            log.info("grantHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期是激活状态，用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，又因为是每月固定日发放的，所以会累加余额"
                                    , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                            // 获取该假期总的分钟数
                            BigDecimal totalMinutes = BigDecimal.ZERO;

                            // 并且用户假期详情表数据不为空，表示用户有这个假期，也有假期详情，因为是每月发放的，所以进行更新假期详情
                            List<UserLeaveStageDetailDO> stageDetail = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
                            // 将stageDetail 按照薪资比率降序
                            stageDetail.sort(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed());
                            // 将hrmsCompanyLeaveItemConfig 按照薪资比率降序
                            hrmsCompanyLeaveItemConfig.sort(Comparator.comparing(CompanyLeaveItemConfigDO::getPercentSalary).reversed());
                            for (int i = 0; i < stageDetail.size(); i++) {
                                for (int j = 0; j < hrmsCompanyLeaveItemConfig.size(); j++) {
                                    if (i == j) {
                                        CompanyLeaveItemConfigDO companyLeaveItemConfig = hrmsCompanyLeaveItemConfig.get(j);
                                        UserLeaveStageDetailDO userLeaveStageDetail = stageDetail.get(i);
                                        BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
                                        BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
                                        totalMinutes = totalMinutes.add(leaveResidueMinutes);
                                        userLeaveStageDetail.setLeaveResidueMinutes(userLeaveStageDetail.getLeaveResidueMinutes().add(leaveResidueMinutes));
                                        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveStageDetail);
                                        updateUserLeaveStageDetailList.add(userLeaveStageDetail);
                                    }
                                }
                            }

                            // 新增销假操作记录
                            addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true"
                                    , date, companyLeaveConfig);
                        }
                    } else {
                        log.info("grantHolidays：周期性发放，每月固定日，假期名称：{}，员工code：{}，用户对假期id：{}，用户这个假期被禁用，需要启用该假期，再重新发放余额"
                                , companyLeaveConfig.getLeaveName(), userInfo.getUserCode(), userLeaveDetailDO.getId());
                        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
                        userLeaveDisabledOperator(userInfo, userEntryRecord
                                , leaveConfigId, companyLeaveConfig
                                , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                                , addUserLeaveStageDetailList
                                , addUserLeaveRecordList, updateUserLeaveDetailDOList, leaveIdToStageDetailMap
                                , updateUserLeaveStageDetailList, userLeaveDetailDO, hrmsCompanyLeaveItemConfig, date);
                    }
                }
            }
        }
    }

    private void userLeaveDisabledOperator(UserInfoDO userInfo, AttendanceUserEntryRecord userEntryRecord
            , Long leaveConfigId, CompanyLeaveConfigDO companyLeaveConfig
            , CompanyLeaveConfigCarryOverDO leaveConfigCarryOver
            , List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList
            , List<UserLeaveStageDetailDO> addUserLeaveStageDetailList
            , List<UserLeaveRecordDO> addUserLeaveRecordList, List<UserLeaveDetailDO> updateUserLeaveDetailDOList
            , Map<Long, List<UserLeaveStageDetailDO>> leaveIdToStageDetailMap
            , List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList, UserLeaveDetailDO userLeaveDetailDO
            , List<CompanyLeaveItemConfigDO> hrmsCompanyLeaveItemConfig, Date date) {

        // 如果该假期类型为禁用状态，修改该假期类型为启用状态
        userLeaveDetailDO.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(userLeaveDetailDO);
        updateUserLeaveDetailDOList.add(userLeaveDetailDO);

        // 先删除该员工该假期类型 详情
        List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = leaveIdToStageDetailMap.get(userLeaveDetailDO.getId());
        //防止空指针
        userLeaveStageDetailInfoList = userLeaveStageDetailInfoList == null ? Lists.newArrayList() : userLeaveStageDetailInfoList;
        // 获取该假期之前总的分钟数
        BigDecimal oldTotalMinutes = BigDecimal.ZERO;
        // 设置假期详情为删除状态
        for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailInfoList) {
//            stageDetailDO.setIsDelete(IsDeleteEnum.YES.getCode());
            stageDetailDO.setIsInvalid(WhetherEnum.YES.getKey());
            BaseDOUtil.fillDOUpdateByUserOrSystem(stageDetailDO);
            oldTotalMinutes = oldTotalMinutes.add(stageDetailDO.getLeaveResidueMinutes());
        }
        updateUserLeaveStageDetailList.addAll(userLeaveStageDetailInfoList);
        // 先删除之前的非年假假期余额-新增请假操作记录【余额不为0的生成请假操作记录】
        if (oldTotalMinutes.compareTo(BigDecimal.ZERO) != 0) {
            addLeaveRecord(userInfo, leaveConfigId, oldTotalMinutes, addUserLeaveRecordList, "false"
                    , date, companyLeaveConfig);
        }

        // 获取该假期总的分钟数
        BigDecimal totalMinutes = BigDecimal.ZERO;
        // 遍历该国家下该假期类型的详情
        for (CompanyLeaveItemConfigDO companyLeaveItemConfig : hrmsCompanyLeaveItemConfig) {
            // 获取该阶段假期天数
            BigDecimal leaveResidueDay = companyLeaveItemConfig.getEndDay().add(BigDecimal.ONE).subtract(companyLeaveItemConfig.getStartDay());
            BigDecimal leaveResidueMinutes = leaveResidueDay.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            totalMinutes = totalMinutes.add(leaveResidueMinutes);
            // 新增假期详情信息
            addLeaveStageDetail(companyLeaveConfig, companyLeaveItemConfig
                    , userLeaveDetailDO, leaveResidueMinutes
                    , date, userEntryRecord
                    , leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList
                    , addUserLeaveStageDetailList);
        }
        // 新增销假操作记录
        addLeaveRecord(userInfo, leaveConfigId, totalMinutes, addUserLeaveRecordList, "true", date, companyLeaveConfig);
    }

    /**
     * 构建用户假期类型数据
     *
     * @param userInfo               用户信息
     * @param leaveConfigId          假期规则主键
     * @param companyLeaveConfig     假期配置
     * @param addUserLeaveDetailList 用户假期集合
     * @return UserLeaveDetailDO
     */
    @NotNull
    private UserLeaveDetailDO addLeaveDetail(UserInfoDO userInfo, Long leaveConfigId, CompanyLeaveConfigDO companyLeaveConfig, List<UserLeaveDetailDO> addUserLeaveDetailList) {
        UserLeaveDetailDO userLeaveDetail = new UserLeaveDetailDO();
        userLeaveDetail.setId(defaultIdWorker.nextId());
        userLeaveDetail.setUserId(userInfo.getId());
        userLeaveDetail.setUserCode(userInfo.getUserCode());
        userLeaveDetail.setConfigId(leaveConfigId);
        userLeaveDetail.setLeaveName(companyLeaveConfig.getLeaveName());
        userLeaveDetail.setLeaveType(companyLeaveConfig.getLeaveType());
        userLeaveDetail.setStatus(companyLeaveConfig.getStatus());
        BaseDOUtil.fillDOInsertByUsrOrSystem(userLeaveDetail);
        addUserLeaveDetailList.add(userLeaveDetail);
        return userLeaveDetail;
    }

    /**
     * 增加用户假期详情表数据
     *
     * @param companyLeaveConfig          假期配置
     * @param companyLeaveItemConfigDO    假期详情配置
     * @param userLeaveDetail             用户假期数据
     * @param leaveResidueMinutes         假期剩余分钟数
     * @param addUserLeaveStageDetailList 用户假期详情集合
     */
    private void addLeaveStageDetail(CompanyLeaveConfigDO companyLeaveConfig,
                                     CompanyLeaveItemConfigDO companyLeaveItemConfigDO,
                                     UserLeaveDetailDO userLeaveDetail,
                                     BigDecimal leaveResidueMinutes,
                                     Date date,
                                     AttendanceUserEntryRecord userEntryRecord,
                                     CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                     List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList,
                                     List<UserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        UserLeaveStageDetailDO detailDO = new UserLeaveStageDetailDO();
        detailDO.setLeaveId(userLeaveDetail.getId());
        detailDO.setLeaveResidueMinutes(leaveResidueMinutes);
        detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        detailDO.setPercentSalary(companyLeaveItemConfigDO.getPercentSalary());
        detailDO.setStage(companyLeaveItemConfigDO.getStage());
        detailDO.setStatus(companyLeaveConfig.getStatus());
        // 设置假期详情为未结转，有效状态
        detailDO.setLeaveMark(WhetherEnum.NO.getKey());
        detailDO.setIsInvalid(WhetherEnum.NO.getKey());
        // 设置假期发放日期
        detailDO.setIssueDate(DateUtils.date2Str(date, DateFormatterUtil.FORMAT_YYYYMMDD));
        // 设置失效日期
        detailDO.setInvalidDate(companyLeaveConfigCarryOverService.getUserInvalidDate(date,
                userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeList));
        BaseDOUtil.fillDOInsertByUsrOrSystem(detailDO);
        addUserLeaveStageDetailList.add(detailDO);
    }

    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param leaveConfigId          假期规则主键
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param flag                   是否销假【true:销假，false:请假】
     * @param date                   当地时间日期
     * @param companyLeaveConfigDO   假期规则
     */
    private void addLeaveRecord(UserInfoDO userInfo, Long leaveConfigId, BigDecimal totalMinutes
            , List<UserLeaveRecordDO> addUserLeaveRecordList, String flag, Date date
            , CompanyLeaveConfigDO companyLeaveConfigDO) {
        UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
        recordDO.setId(defaultIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(date);
        recordDO.setDayId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        recordDO.setConfigId(leaveConfigId);
        recordDO.setLeaveName(companyLeaveConfigDO.getLeaveName());
        recordDO.setLeaveType(companyLeaveConfigDO.getLeaveType());
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
        } else {
            recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
        }
        recordDO.setLeaveMinutes(totalMinutes);
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setRemark("系统初始化/System initialization");
        } else {
            recordDO.setRemark("系统初始化【用户假期数据之前是DISABLED-删除旧数据】/System initialization");
        }
        BaseDOUtil.fillDOInsertByUsrOrSystem(recordDO);
        // 防止查询时间相同，导致根据创建时间排序出现先后顺序问题【根据创建时间倒序之后，过期清零在新年初始化上面就比较奇怪】，所以这边新年初始化创建时间需要在过期清零后面，
        if (ObjectUtil.equal(flag, "true")) {
            recordDO.setCreateDate(DateUtil.offsetSecond(new Date(), 1));
            recordDO.setLastUpdDate(DateUtil.offsetSecond(new Date(), 1));
        }
        recordDO.setCreateUserCode("admin");
        recordDO.setCreateUserName("admin");
        recordDO.setLastUpdUserCode("admin");
        recordDO.setLastUpdUserName("admin");
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        addUserLeaveRecordList.add(recordDO);
    }

    /**
     * 校验是否满足年龄发假条件
     *
     * @param userInfo
     * @param companyLeaveConfig
     * @param leaveConfigIssueRuleRangeInfoList
     * @return
     */
    private Boolean checkAgeGrant(UserInfoDO userInfo,
                                  CompanyLeaveConfigDO companyLeaveConfig,
                                  List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList) {
        // 获取员工出生日期
        Date birthday = userInfo.getBirthday();
        XxlJobLogger.log("当前用户：{},info：当前国家:{},假期主键id：{},假期名称：{},出生日期：{}"
                , userInfo.getUserCode(), companyLeaveConfig.getCountry()
                , companyLeaveConfig.getId(), companyLeaveConfig.getLeaveName(), birthday);
        if (Objects.isNull(birthday)) {
            return false;
        }
        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期名称：{},发放频次是年龄递增，额度类型是年龄递增，额度范围为空，跳过。"
                    , companyLeaveConfig.getCountry(), companyLeaveConfig.getId()
                    , companyLeaveConfig.getLeaveName());
            return false;
        }
        return true;
    }

    /**
     * 校验是否满足工龄发假条件
     *
     * @param userInfo
     * @param companyLeaveConfig
     * @param workSeniority
     * @param leaveConfigIssueRuleRangeInfoList
     * @return
     */
    private Boolean checkWorkSeniorityGrant(UserInfoDO userInfo,
                                            CompanyLeaveConfigDO companyLeaveConfig,
                                            String workSeniority,
                                            List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList) {
        // 获取员工工龄
        if (Objects.isNull(workSeniority) || Integer.parseInt(workSeniority) < 0) {
            XxlJobLogger.log("当前用户：{},error：当前国家:{},假期主键id：{},假期名称：{},工龄未获取到：{}"
                    , userInfo.getUserCode(), companyLeaveConfig.getCountry()
                    , companyLeaveConfig.getId(), companyLeaveConfig.getLeaveName(), workSeniority);
            return false;
        }
        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期名称：{},发放频次是年龄递增，额度类型是年龄递增，额度范围为空，跳过。"
                    , companyLeaveConfig.getCountry(), companyLeaveConfig.getId()
                    , companyLeaveConfig.getLeaveName());
            return false;
        }
        return true;
    }

    /**
     * 校验是否满足省市发假条件
     *
     * @param userInfo
     * @param companyLeaveConfig
     * @param leaveConfigIssueRuleRangeInfoList
     * @return
     */
    private Boolean checkLocationGrant(UserInfoDO userInfo,
                                       CompanyLeaveConfigDO companyLeaveConfig,
                                       CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                       List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeInfoList) {
        // 获取员工发放规则
        if (Objects.isNull(leaveConfigIssueRule)
                || !LeaveConfigIssueTypeEnum.RELEASE_BY_PROVINCE_OR_CITY.getType().equals(leaveConfigIssueRule.getIssueType())) {
            XxlJobLogger.log("当前用户：{},error：当前国家:{},假期主键id：{},假期名称：{},未配置按省市发放"
                    , userInfo.getUserCode(), companyLeaveConfig.getCountry()
                    , companyLeaveConfig.getId(), companyLeaveConfig.getLeaveName());
            return false;
        }
        if (CollUtil.isEmpty(leaveConfigIssueRuleRangeInfoList)) {
            XxlJobLogger.log("error：当前国家:{},假期主键id：{},假期名称：{},发放频次是年龄递增，额度类型是年龄递增，额度范围为空，跳过。"
                    , companyLeaveConfig.getCountry(), companyLeaveConfig.getId()
                    , companyLeaveConfig.getLeaveName());
            return false;
        }
        return true;
    }

    private BigDecimal getDispatchResidueMinutes(BigDecimal bigDecimalIssueQuota,
                                                 Date entryDate,
                                                 Date dispatchDate,
                                                 Long isConvert,
                                                 Integer issueRoundingRule) {
        // 入职年份
        int entryYear = DateUtil.year(entryDate);
        // 派遣年份
        int dispatchYear = DateUtil.year(dispatchDate);
        // 如果入职年份跟小于当前年份，不打折全额发放
        if (entryYear < dispatchYear) {
            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            return leaveResidueMinutes;
        }
        // 如果入职年份和当前年份相等，需要折扣
        if (entryYear == dispatchYear) {
            // 获取该假期总的分钟数
            BigDecimal leaveResidueMinutes = bigDecimalIssueQuota.multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS).multiply(BusinessConstant.MINUTES);
            // 折扣分钟数
            leaveResidueMinutes = convertLeaveResidueDay(entryDate, isConvert, issueRoundingRule, leaveResidueMinutes);
            return leaveResidueMinutes;
        }
        // log.info("entryYear > dispatchYear");
        return BigDecimal.ZERO;
    }

    private void calcWorkSeniorityMonth(Map<String, String> userMapForWorkSeniority,
                                        Map<String, Date> userEntryDateMap,
                                        Date dateTime) {
        if (userMapForWorkSeniority.isEmpty() || userEntryDateMap.isEmpty() || Objects.isNull(dateTime)) {
            return;
        }
        // 获取员工工龄
        for (String userCode : userMapForWorkSeniority.keySet()) {
            String workSeniority = userMapForWorkSeniority.get(userCode);
            Date entryDate = userEntryDateMap.get(userCode);
            // 校验入职日期和传递日期是否相等，相等就是初始工龄
            if (Objects.isNull(entryDate) || dateTime.compareTo(entryDate) == 0 || StringUtils.isBlank(workSeniority)) {
                continue;
            }
            // 计算工龄月数
            LocalDate hireDate = LocalDate.parse(DateFormatUtils.format(entryDate, DATE));
            LocalDate currentDate = LocalDate.parse(DateFormatUtils.format(dateTime, DATE));
            // 计算相差的月数
            long monthsBetween = ChronoUnit.MONTHS.between(hireDate, currentDate);
            // 调整计算结果以排除未满一个月的部分
            if (hireDate.plusMonths(monthsBetween).isAfter(currentDate)) {
                monthsBetween--;
            }
            Integer initWorkSenior = Optional.ofNullable(Integer.valueOf(workSeniority)).orElse(BusinessConstant.ZERO);
            // 更新工龄
            userMapForWorkSeniority.put(userCode, String.valueOf(Integer.sum(initWorkSenior, (int) monthsBetween)));
        }
    }

    /**
     * 派遣场景：年龄和工龄折算计算范围时间
     */
    private Date getIssueQuoteByDispatch(int entryYear, int dispatchYear,
                                         Date entryDate, Date dispatchDate, Date targetDate,
                                         Integer isRecalculate) {
        Date leaveDayDate = dispatchDate;
        // 入职和派遣同一年,折算非派遣假期且没开开关
        if (entryYear == dispatchYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            // 非派遣假期才进行入职日折算
            leaveDayDate = entryDate;
        }
        // 入职和派遣不同年，折算非派遣假期且没开开关
        if (entryYear != dispatchYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            leaveDayDate = targetDate;
        }
        return leaveDayDate;
    }

    /**
     * 回国补扣派遣假场景：年龄和工龄折算计算范围时间
     */
    private Date getIssueQuoteByBack(int entryYear, int backYear,
                                     Date oldDispatchDate, Date backDate, Date targetDate,
                                     Integer isRecalculate) {
        int oldDispatchYear = DateUtil.year(oldDispatchDate);
        // 入职和回国同一年,折算派遣假期且开了开关
        // 入职和回国不同年，且上次派遣和回国同年，折算派遣假期且开了开关
        // 入职和回国不同年，且上次派遣和回国也不同年，折算非派遣假期且开了开关
        // 都以回国日期计算
        Date leaveDayDate = backDate;
        // 入职和回国同一年,折算派遣假期且没开开关
        if (entryYear == backYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            // 派遣假期没开开关根据上次派遣日期折算
            leaveDayDate = oldDispatchDate;
        }
        // 入职和回国不同年，且上次派遣和回国同年，折算派遣假期且没开开关
        if (entryYear != backYear
                && oldDispatchYear == backYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            leaveDayDate = oldDispatchDate;
        }
        // 入职和回国不同年，且上次派遣和回国也不同年，折算非派遣假期且没开开关
        if (entryYear != backYear
                && oldDispatchYear != backYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            leaveDayDate = targetDate;
        }
        return leaveDayDate;
    }

    /**
     * 回国补发非派遣假场景：年龄和工龄折算计算范围时间
     */
    private Date getIssueQuoteByBackAndReissue(int entryYear, int backYear,
                                               Date entryDate, Date oldDispatchDate, Date targetDate,
                                               Integer isRecalculate) {
        int oldDispatchYear = DateUtil.year(oldDispatchDate);
        // 入职和回国同一年,补发非派遣假期且开了开关
        // 入职和回国不同年，且上次派遣和回国同年，补发非派遣假期且开了开关
        // 都以上次派遣日期计算
        Date leaveDayDate = oldDispatchDate;
        // 入职和回国同一年,补发非派遣假期且没开开关
        if (entryYear == backYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            // 非派遣假没开开关根据入职日期补发
            leaveDayDate = entryDate;
        }
        // 入职和回国不同年，且上次派遣和回国同年，补发非派遣假期且没开开关
        if (entryYear != backYear
                && oldDispatchYear == backYear
                && WhetherEnum.NO.getKey().equals(isRecalculate)) {
            leaveDayDate = targetDate;
        }
        // 入职和回国不同年，且上次派遣和回国也不同年，不存在这种补发情况
        return leaveDayDate;
    }
}
