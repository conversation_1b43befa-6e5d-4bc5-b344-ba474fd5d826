package com.imile.attendance.migration;

/**
 * <AUTHOR> chen
 * @Date 2025/6/18 
 * @Description
 */
public interface RuleMigrationService {

    /**
     * HR考勤组到新考勤规则的迁移
     *
     * @param country 国家代码
     * @return 迁移结果
     */
    Boolean migrateAttendanceRules(String country);

    /**
     * 根据映射记录回滚考勤规则
     *
     * @param country 国家代码
     * @return 回滚结果
     */
    Boolean rollbackAttendanceRules(String country);

    /**
     * 将用户从旧的考勤组移动到新的考勤组
     *
     * @param userId 用户ID
     * @param country 国家
     * @param fromGroupNo 旧的考勤组ID
     * @param toGroupNo 新的考勤组ID
     * @return 是否成功
     */
    Boolean mvUserFromOldToNewHrAttendanceGroups(Long userId, String country, String fromGroupNo, String toGroupNo);
}
