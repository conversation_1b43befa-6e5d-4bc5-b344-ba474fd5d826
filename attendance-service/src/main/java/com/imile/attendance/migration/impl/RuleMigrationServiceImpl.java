package com.imile.attendance.migration.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.migration.RuleMigrationService;
import com.imile.attendance.migration.adapter.ReissueCardConfigAdapter;
import com.imile.attendance.migration.converter.HrAttendanceGroupConverter;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.processor.HrPunchConfigMigrationProcessor;
import com.imile.attendance.migration.service.MappingRuleConfigService;
import com.imile.attendance.migration.service.RollbackService;
import com.imile.attendance.migration.service.RuleCreationService;
import com.imile.attendance.migration.service.RuleRangeCreationService;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description
 */
@Slf4j
@Service
public class RuleMigrationServiceImpl implements RuleMigrationService {

    @Resource
    private HrmsAttendancePunchConfigDao hrmsAttendancePunchConfigDao;
    @Resource
    private HrmsAttendancePunchConfigRangeDao hrmsAttendancePunchConfigRangeDao;
    @Resource
    private HrmsAttendancePunchClassConfigDao hrmsAttendancePunchClassConfigDao;
    @Resource
    private HrmsAttendancePunchClassItemConfigDao hrmsAttendancePunchClassItemConfigDao;
    @Resource
    private HrAttendanceGroupConverter hrAttendanceGroupConverter;
    @Resource
    private HrPunchConfigMigrationProcessor hrPunchConfigMigrationProcessor;
    @Resource
    private RollbackService rollbackService;
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private RuleCreationService ruleCreationService;
    @Resource
    private RuleRangeCreationService ruleRangeCreationService;
    @Resource
    private MappingRuleConfigService mappingRuleConfigService;
    @Resource
    private ReissueCardConfigAdapter reissueCardConfigAdapter;

    /**
     * HR考勤组到新考勤规则的迁移
     */
    @Override
    @DSTransactional
    public Boolean migrateAttendanceRules(String country) {
        log.info("开始迁移HR考勤组到新考勤规则, country: {}", country);
        XxlJobLogger.log("开始迁移HR考勤组到新考勤规则, country: {}", country);

        try {
            // 1. 参数校验
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            XxlJobLogger.log("步骤1: 参数校验通过, country: {}", country);

            // 2. 查询HR考勤组数据
            XxlJobLogger.log("步骤2: 开始查询HR考勤组数据, country: {}", country);
            List<HrAttendanceGroupDTO> hrGroups = queryHrAttendanceGroups(country);
            if (CollectionUtils.isEmpty(hrGroups)) {
                log.warn("未找到HR考勤组数据, country: {}", country);
                XxlJobLogger.log("未找到HR考勤组数据, country: {}", country);
                return true;
            }

            log.info("查询到HR考勤组数据, country: {}, count: {}", country, hrGroups.size());
            XxlJobLogger.log("查询到HR考勤组数据, country: {}, count: {}", country, hrGroups.size());

            // 3. 分离默认考勤组和自定义考勤组
            XxlJobLogger.log("步骤3: 开始分离默认考勤组和自定义考勤组");
            List<HrAttendanceGroupDTO> defaultGroups = hrGroups.stream()
                    .filter(HrAttendanceGroupDTO::isDefaultGroup)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(defaultGroups)) {
                log.error("未找到默认考勤组, country: {}", country);
                XxlJobLogger.log("未找到默认考勤组, country: {}", country);
                return false;
            }

            List<HrAttendanceGroupDTO> customGroups = hrGroups.stream()
                    .filter(HrAttendanceGroupDTO::isCustomGroup)
                    .collect(Collectors.toList());

            log.info("分离默认考勤组和自定义考勤组完成, 默认考勤组数量: {}, 自定义考勤组数量: {}", 1, customGroups.size());
            XxlJobLogger.log("分离默认考勤组和自定义考勤组完成, 默认考勤组数量: {}, 自定义考勤组数量: {}", 1, customGroups.size());

            // 4. 处理默认考勤组
            XxlJobLogger.log("步骤4: 开始处理默认考勤组, 版本数量: {}", defaultGroups.size());
            for (HrAttendanceGroupDTO defaultGroup : defaultGroups) {
                XxlJobLogger.log("处理默认考勤组, groupId: {}, groupName: {}", defaultGroup.getId(), defaultGroup.getPunchConfigName());
                hrPunchConfigMigrationProcessor.processDefaultAttendanceGroup(defaultGroup);
                XxlJobLogger.log("默认考勤组处理完成, groupId: {}", defaultGroup.getId());
            }

            // 5. 处理自定义考勤组
            XxlJobLogger.log("步骤5: 开始处理自定义考勤组, 数量: {}", customGroups.size());
            int processedCount = 0;
            for (HrAttendanceGroupDTO customGroup : customGroups) {
                XxlJobLogger.log("处理自定义考勤组 [{}/{}], groupId: {}, groupName: {}",
                        ++processedCount, customGroups.size(), customGroup.getId(), customGroup.getPunchConfigName());
                hrPunchConfigMigrationProcessor.processCustomAttendanceGroup(customGroup);
                XxlJobLogger.log("自定义考勤组处理完成, groupId: {}", customGroup.getId());
            }

            // 6. 处理补卡规则（根据国家配置选择处理方式）
            XxlJobLogger.log("步骤6: 开始处理补卡规则");
            processReissueCardRulesByCountry(country, hrGroups);
            XxlJobLogger.log("补卡规则处理完成");

            log.info("HR考勤组迁移完成, country: {}", country);
            XxlJobLogger.log("HR考勤组迁移完成, country: {}, 总处理数量: {}", country, hrGroups.size());
            return true;

        } catch (Exception e) {
            log.error("HR考勤组迁移失败, country: {}", country, e);
            XxlJobLogger.log("HR考勤组迁移失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 根据映射记录回滚考勤规则
     */
    @Override
    @DSTransactional
    public Boolean rollbackAttendanceRules(String country) {
        log.info("开始回滚考勤规则, country: {}", country);
        XxlJobLogger.log("开始回滚考勤规则, country: {}", country);

        try {
            // 参数校验
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            XxlJobLogger.log("步骤1: 参数校验通过, country: {}", country);

            // 执行回滚
            XxlJobLogger.log("步骤2: 开始执行回滚操作");
            Boolean result = rollbackService.rollbackAttendanceRulesByCountry(country);

            if (Boolean.TRUE.equals(result)) {
                log.info("考勤规则回滚成功, country: {}", country);
                XxlJobLogger.log("考勤规则回滚成功, country: {}", country);
                return true;
            } else {
                log.error("考勤规则回滚失败, country: {}", country);
                XxlJobLogger.log("考勤规则回滚失败, country: {}", country);
                return false;
            }

        } catch (Exception e) {
            log.error("考勤规则回滚异常, country: {}", country, e);
            XxlJobLogger.log("考勤规则回滚异常, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }


    @Override
    @DSTransactional
    public Boolean mvUserFromOldToNewHrAttendanceGroups(Long userId, String country, String fromGroupNo, String toGroupNo) {
        if (null == userId || null == fromGroupNo || null == toGroupNo) {
            log.info("mvUserFromOldToNewHrAttendanceGroups | 参数异常, userId: {}, oldGroupNo: {}, newGroupNo: {}",
                    userId, fromGroupNo, toGroupNo);
            return false;
        }
        HrAttendanceGroupDTO fromHrGroup = queryHrAttendanceGroup(country, fromGroupNo);
        HrAttendanceGroupDTO toHrGroup = queryHrAttendanceGroup(country, toGroupNo);
        if (null == fromHrGroup || null == toHrGroup) {
            log.info("mvUserFromOldToNewHrAttendanceGroups | 旧或新考勤组不存在, oldGroupNo: {}, newGroupNo: {}",
                    fromGroupNo, toGroupNo);
            return false;
        }
        // 打卡规则范围修改
        List<HrAttendanceGroupRangeDTO> fromRangeList = fromHrGroup.getRangeList();
        if (CollectionUtils.isEmpty(fromRangeList)) {
            log.info("mvUserFromOldToNewHrAttendanceGroups | 旧考勤组没有适用范围, oldGroupNo: {}, userId: {}", fromGroupNo, userId);
            return false;
        }
        List<HrAttendanceGroupRangeDTO> userFromHrGroupRangeDTO = fromRangeList.stream()
                .filter(range -> range.getBizId().equals(userId))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userFromHrGroupRangeDTO)) {
            log.info("mvUserFromOldToNewHrAttendanceGroups | 旧考勤组没有找到用户, oldGroupNo: {}, userId: {}", fromGroupNo, userId);
            return false;
        }
        return null;

    }


    private HrAttendanceGroupDTO queryHrAttendanceGroup(String country, String hrAttendanceGroupNo) {
        HrmsAttendancePunchConfigDO punchConfigDO = hrmsAttendancePunchConfigDao.getLatestAndActiveByPunchConfigNo(hrAttendanceGroupNo);
        if (null == punchConfigDO) {
            log.info("queryHrAttendanceGroup | 未找到考勤组, punchConfigNo: {}", hrAttendanceGroupNo);
            return null;
        }
        // 2. 转换为DTO
        HrAttendanceGroupDTO groupDTO = hrAttendanceGroupConverter.convertToDTO(punchConfigDO);

        // 3. 查询并设置适用范围
        List<Long> configIds = Collections.singletonList(groupDTO.getId());

        List<HrmsAttendancePunchConfigRangeDO> hrRanges = hrmsAttendancePunchConfigRangeDao.listByPunchConfigIds(configIds);
        Map<Long, List<HrmsAttendancePunchConfigRangeDO>> rangeMap = hrRanges.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchConfigRangeDO::getPunchConfigId));

        // 4. 查询并设置班次配置
        List<HrmsAttendancePunchClassConfigDO> hrClasses = hrmsAttendancePunchClassConfigDao.listByPunchConfigIds(configIds);
        Map<Long, List<HrmsAttendancePunchClassConfigDO>> classMap = hrClasses.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchClassConfigDO::getPunchConfigId));

        // 5. 查询班次时间配置
        List<Long> classIds = hrClasses.stream()
                .map(HrmsAttendancePunchClassConfigDO::getId)
                .collect(Collectors.toList());

        List<HrmsAttendancePunchClassItemConfigDO> hrClassItems = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(classIds)) {
            hrClassItems = hrmsAttendancePunchClassItemConfigDao.listByPunchClassIds(classIds);
        }

        Map<Long, List<HrmsAttendancePunchClassItemConfigDO>> classItemMap = hrClassItems.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchClassItemConfigDO::getPunchClassId));

        // 设置适用范围
        List<HrmsAttendancePunchConfigRangeDO> ranges = rangeMap.get(groupDTO.getId());
        if (CollectionUtils.isNotEmpty(ranges)) {
            List<HrAttendanceGroupRangeDTO> rangeDTOs = hrAttendanceGroupConverter.convertRangeToDTO(ranges);
            groupDTO.setRangeList(rangeDTOs);
        }

        // 设置班次配置
        List<HrmsAttendancePunchClassConfigDO> classes = classMap.get(groupDTO.getId());
        if (CollectionUtils.isNotEmpty(classes)) {
            List<HrAttendanceClassConfigDTO> classDTOs = hrAttendanceGroupConverter.convertClassToDTO(classes);

            // 为每个班次设置时间配置
            for (HrAttendanceClassConfigDTO classDTO : classDTOs) {
                List<HrmsAttendancePunchClassItemConfigDO> classItems = classItemMap.get(classDTO.getId());
                if (CollectionUtils.isNotEmpty(classItems)) {
                    List<HrAttendanceClassItemConfigDTO> classItemDTOs = hrAttendanceGroupConverter.convertClassItemToDTO(classItems);
                    classDTO.setItemList(classItemDTOs);
                }
            }

            groupDTO.setClassList(classDTOs);
        }

        // 时区
        CountryDTO countryDTO = countryService.queryCountry(country);
        if (null != countryDTO) {
            groupDTO.setTimeZone(countryDTO.getTimeZone());
        }
        log.info("查询HR考勤组数据完成, country: {}, groupNo: {}", country, hrAttendanceGroupNo);
        return groupDTO;
    }

    /**
     * 查询HR考勤组数据
     */
    private List<HrAttendanceGroupDTO> queryHrAttendanceGroups(String country) {
        log.info("查询HR考勤组数据, country: {}", country);

        // 1. 查询HR考勤组基础数据
        List<HrmsAttendancePunchConfigDO> hrConfigs = hrmsAttendancePunchConfigDao.listByCountry(country);
        if (CollectionUtils.isEmpty(hrConfigs)) {
            log.warn("未找到HR考勤组数据, country: {}", country);
            return Collections.emptyList();
        }

        // 2. 转换为DTO
        List<HrAttendanceGroupDTO> groupDTOs = hrAttendanceGroupConverter.convertToDTO(hrConfigs);

        // 3. 查询并设置适用范围
        List<Long> configIds = hrConfigs.stream()
                .map(HrmsAttendancePunchConfigDO::getId)
                .collect(Collectors.toList());

        List<HrmsAttendancePunchConfigRangeDO> hrRanges = hrmsAttendancePunchConfigRangeDao.listByPunchConfigIds(configIds);
        Map<Long, List<HrmsAttendancePunchConfigRangeDO>> rangeMap = hrRanges.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchConfigRangeDO::getPunchConfigId));

        // 4. 查询并设置班次配置
        List<HrmsAttendancePunchClassConfigDO> hrClasses = hrmsAttendancePunchClassConfigDao.listByPunchConfigIds(configIds);
        Map<Long, List<HrmsAttendancePunchClassConfigDO>> classMap = hrClasses.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchClassConfigDO::getPunchConfigId));

        // 5. 查询班次时间配置
        List<Long> classIds = hrClasses.stream()
                .map(HrmsAttendancePunchClassConfigDO::getId)
                .collect(Collectors.toList());

        List<HrmsAttendancePunchClassItemConfigDO> hrClassItems = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(classIds)) {
            hrClassItems = hrmsAttendancePunchClassItemConfigDao.listByPunchClassIds(classIds);
        }

        Map<Long, List<HrmsAttendancePunchClassItemConfigDO>> classItemMap = hrClassItems.stream()
                .collect(Collectors.groupingBy(HrmsAttendancePunchClassItemConfigDO::getPunchClassId));

        // 6. 组装完整数据
        for (HrAttendanceGroupDTO groupDTO : groupDTOs) {
            // 设置适用范围
            List<HrmsAttendancePunchConfigRangeDO> ranges = rangeMap.get(groupDTO.getId());
            if (CollectionUtils.isNotEmpty(ranges)) {
                List<HrAttendanceGroupRangeDTO> rangeDTOs = hrAttendanceGroupConverter.convertRangeToDTO(ranges);
                groupDTO.setRangeList(rangeDTOs);
            }

            // 设置班次配置
            List<HrmsAttendancePunchClassConfigDO> classes = classMap.get(groupDTO.getId());
            if (CollectionUtils.isNotEmpty(classes)) {
                List<HrAttendanceClassConfigDTO> classDTOs = hrAttendanceGroupConverter.convertClassToDTO(classes);

                // 为每个班次设置时间配置
                for (HrAttendanceClassConfigDTO classDTO : classDTOs) {
                    List<HrmsAttendancePunchClassItemConfigDO> classItems = classItemMap.get(classDTO.getId());
                    if (CollectionUtils.isNotEmpty(classItems)) {
                        List<HrAttendanceClassItemConfigDTO> classItemDTOs = hrAttendanceGroupConverter.convertClassItemToDTO(classItems);
                        classDTO.setItemList(classItemDTOs);
                    }
                }

                groupDTO.setClassList(classDTOs);
            }
        }

        // 时区
        CountryDTO countryDTO = countryService.queryCountry(country);
        if (null != countryDTO) {
            groupDTOs.forEach(groupDTO -> groupDTO.setTimeZone(countryDTO.getTimeZone()));
        }
        log.info("查询HR考勤组数据完成, country: {}, count: {}", country, groupDTOs.size());
        return groupDTOs;
    }

    /**
     * 处理补卡规则的特殊迁移逻辑
     * 从Apollo配置中读取国家级补卡规则配置，为每个国家创建补卡规则和用户范围
     *
     * @param country 当前处理的国家
     */
    private void processReissueCardRules(String country) {
        log.info("开始处理补卡规则, country: {}", country);
        XxlJobLogger.log("开始处理补卡规则, country: {}", country);

        try {
            // 1. 从Apollo配置读取国家级补卡规则配置
            String countryReissueCardConfigStr = attendanceProperties.getAttendance().getCountryReissueCardConfig();
            if (StringUtils.isBlank(countryReissueCardConfigStr) || "{}".equals(countryReissueCardConfigStr.trim())) {
                log.info("未配置国家级补卡规则，跳过处理, country: {}", country);
                XxlJobLogger.log("未配置国家级补卡规则，跳过处理, country: {}", country);
                return;
            }

            XxlJobLogger.log("读取到补卡规则配置: {}", countryReissueCardConfigStr);

            // 2. 解析JSON配置
            Map<String, Integer> countryConfigMap;
            try {
                countryConfigMap = JSON.parseObject(countryReissueCardConfigStr, new TypeReference<Map<String, Integer>>() {
                });
            } catch (Exception e) {
                log.error("解析补卡规则配置失败, config: {}", countryReissueCardConfigStr, e);
                XxlJobLogger.log("解析补卡规则配置失败, config: {}, error: {}", countryReissueCardConfigStr, e.getMessage());
                return;
            }

            if (countryConfigMap == null || countryConfigMap.isEmpty()) {
                log.info("补卡规则配置为空，跳过处理, country: {}", country);
                XxlJobLogger.log("补卡规则配置为空，跳过处理, country: {}", country);
                return;
            }

            // 3. 检查当前国家是否在配置中
            Integer maxRepunchNumber = countryConfigMap.get(country);
            if (maxRepunchNumber == null) {
                log.info("当前国家未配置补卡规则，跳过处理, country: {}", country);
                XxlJobLogger.log("当前国家未配置补卡规则，跳过处理, country: {}", country);
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "country" + country + " not in config maxRepunchNumber");
            }

            XxlJobLogger.log("当前国家补卡规则配置: country={}, maxRepunchNumber={}", country, maxRepunchNumber);

            // 4. 检查是否已存在该国家的补卡规则（使用适配器）
            List<ReissueCardConfigDO> existingConfigs = reissueCardConfigAdapter.getByCountry(country);
            boolean hasCountryLevelConfig = existingConfigs.stream()
                    .anyMatch(config -> config.areCountryLevel() && config.areLatest() && config.areActive());

            if (hasCountryLevelConfig) {
                log.info("该国家已存在国家级补卡规则，跳过创建, country: {}", country);
                XxlJobLogger.log("该国家已存在国家级补卡规则，跳过创建, country: {}", country);
                return;
            }

            // 5. 创建补卡规则
            XxlJobLogger.log("开始创建补卡规则, country: {}, maxRepunchNumber: {}", country, maxRepunchNumber);
            ReissueCardConfigDO reissueCardConfig = ruleCreationService.createReissueCardConfig(country, maxRepunchNumber);
            if (reissueCardConfig == null) {
                log.error("创建补卡规则失败, country: {}", country);
                XxlJobLogger.log("创建补卡规则失败, country: {}", country);
                return;
            }

            XxlJobLogger.log("补卡规则创建成功, configId: {}, configNo: {}",
                    reissueCardConfig.getId(), reissueCardConfig.getConfigNo());

            // 6. 获取国家时区信息
            CountryDTO countryDTO = countryService.queryCountry(country);
            String timeZone = countryDTO != null ? countryDTO.getTimeZone() : "UTC+8";

            // 7. 创建补卡规则范围（为该国所有符合条件的用户）
            XxlJobLogger.log("开始创建补卡规则范围, country: {}", country);
            List<ReissueCardConfigRangeDO> ranges =
                    ruleRangeCreationService.createReissueCardConfigRanges(reissueCardConfig, country, timeZone);

            if (CollectionUtils.isNotEmpty(ranges)) {
                XxlJobLogger.log("补卡规则范围创建成功, country: {}, 用户数量: {}", country, ranges.size());
            } else {
                XxlJobLogger.log("补卡规则范围创建完成，但未找到符合条件的用户, country: {}", country);
            }

            // 8. 创建映射记录（使用虚拟的HR考勤组信息）
            HrAttendanceGroupDTO virtualGroup = createVirtualHrGroup(country);
            MappingRuleConfigDO mappingConfig = mappingRuleConfigService.createMapping(
                    virtualGroup, RuleConfigTypeEnum.REISSUE_CARD_CONFIG.name(), reissueCardConfig.getId());

            if (mappingConfig != null) {
                XxlJobLogger.log("补卡规则映射记录创建成功, mappingId: {}", mappingConfig.getId());
            }

            log.info("补卡规则处理完成, country: {}, configId: {}, 用户数量: {}",
                    country, reissueCardConfig.getId(), CollectionUtils.size(ranges));
            XxlJobLogger.log("补卡规则处理完成, country: {}, configId: {}, 用户数量: {}",
                    country, reissueCardConfig.getId(), CollectionUtils.size(ranges));

        } catch (Exception e) {
            log.error("处理补卡规则异常, country: {}", country, e);
            XxlJobLogger.log("处理补卡规则异常, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 根据国家配置选择补卡规则处理方式
     * CHN使用Apollo配置的国家级规则，其他国家使用考勤组级别的规则
     *
     * @param country  国家代码
     * @param hrGroups HR考勤组列表
     */
    private void processReissueCardRulesByCountry(String country, List<HrAttendanceGroupDTO> hrGroups) {
        log.info("根据国家配置处理补卡规则, country: {}", country);
        XxlJobLogger.log("根据国家配置处理补卡规则, country: {}", country);

        try {
            // 判断是否为中国，中国使用Apollo配置的国家级规则
            if (CountryCodeEnum.CHN.getCode().equalsIgnoreCase(country)) {
                log.info("中国使用Apollo配置的国家级补卡规则, country: {}", country);
                XxlJobLogger.log("中国使用Apollo配置的国家级补卡规则, country: {}", country);
                processReissueCardRules(country);
            } else {
                log.info("其他国家使用考勤组级别的补卡规则, country: {}", country);
                XxlJobLogger.log("其他国家使用考勤组级别的补卡规则, country: {}", country);
                processAttendanceGroupReissueCardRulesForCountry(country, hrGroups);
            }

            log.info("补卡规则处理完成, country: {}", country);
            XxlJobLogger.log("补卡规则处理完成, country: {}", country);

        } catch (Exception e) {
            log.error("根据国家配置处理补卡规则失败, country: {}", country, e);
            XxlJobLogger.log("根据国家配置处理补卡规则失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 为指定国家处理所有考勤组的补卡规则
     *
     * @param country  国家代码
     * @param hrGroups HR考勤组列表
     */
    private void processAttendanceGroupReissueCardRulesForCountry(String country, List<HrAttendanceGroupDTO> hrGroups) {
        log.info("开始为国家处理考勤组级别的补卡规则, country: {}, 考勤组数量: {}", country, hrGroups.size());
        XxlJobLogger.log("开始为国家处理考勤组级别的补卡规则, country: {}, 考勤组数量: {}", country, hrGroups.size());

        try {
            int processedCount = 0;
            int successCount = 0;

            for (HrAttendanceGroupDTO attendanceGroup : hrGroups) {
                try {
                    processedCount++;
                    XxlJobLogger.log("处理考勤组补卡规则 [{}/{}], groupId: {}, groupName: {}",
                            processedCount, hrGroups.size(), attendanceGroup.getId(), attendanceGroup.getPunchConfigName());

                    processAttendanceGroupReissueCardRules(attendanceGroup);
                    successCount++;

                    XxlJobLogger.log("考勤组补卡规则处理成功, groupId: {}", attendanceGroup.getId());

                } catch (Exception e) {
                    log.error("处理考勤组补卡规则失败, groupId: {}, groupName: {}",
                            attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), e);
                    XxlJobLogger.log("处理考勤组补卡规则失败, groupId: {}, error: {}",
                            attendanceGroup.getId(), e.getMessage());
                    throw e;
                }
            }

            log.info("国家考勤组补卡规则处理完成, country: {}, 总数: {}, 成功: {}, 失败: {}",
                    country, processedCount, successCount, processedCount - successCount);
            XxlJobLogger.log("国家考勤组补卡规则处理完成, country: {}, 总数: {}, 成功: {}, 失败: {}",
                    country, processedCount, successCount, processedCount - successCount);

        } catch (Exception e) {
            log.error("为国家处理考勤组级别的补卡规则失败, country: {}", country, e);
            XxlJobLogger.log("为国家处理考勤组级别的补卡规则失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    /**
     * 处理单个考勤组的补卡规则创建
     * 从考勤组的 HrAttendanceGroupDTO 对象中获取 maxRepunchNumber 字段作为新规则的最大补卡次数
     *
     * @param attendanceGroup 考勤组信息
     */
    private void processAttendanceGroupReissueCardRules(HrAttendanceGroupDTO attendanceGroup) {
        log.info("开始处理考勤组级别的补卡规则, groupId: {}, groupName: {}, maxRepunchNumber: {}",
                attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), attendanceGroup.getMaxRepunchNumber());
        XxlJobLogger.log("开始处理考勤组级别的补卡规则, groupId: {}, groupName: {}, maxRepunchNumber: {}",
                attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), attendanceGroup.getMaxRepunchNumber());

        try {
            // 1. 参数验证
            if (attendanceGroup.getMaxRepunchNumber() == null || attendanceGroup.getMaxRepunchNumber() <= 0) {
                log.info("考勤组最大补卡次数无效，跳过创建补卡规则, groupId: {}, maxRepunchNumber: {}",
                        attendanceGroup.getId(), attendanceGroup.getMaxRepunchNumber());
                XxlJobLogger.log("考勤组最大补卡次数无效，跳过创建补卡规则, groupId: {}, maxRepunchNumber: {}",
                        attendanceGroup.getId(), attendanceGroup.getMaxRepunchNumber());
                return;
            }

            // 2. 获取关联的适用范围
            List<HrAttendanceGroupRangeDTO> rangeList = attendanceGroup.getRangeList();
            if (CollectionUtils.isEmpty(rangeList)) {
                log.warn("考勤组没有适用范围，跳过创建补卡规则, groupId: {}", attendanceGroup.getId());
                XxlJobLogger.log("考勤组没有适用范围，跳过创建补卡规则, groupId: {}", attendanceGroup.getId());
                return;
            }

            log.info("考勤组适用范围数量: {}, groupId: {}", rangeList.size(), attendanceGroup.getId());
            XxlJobLogger.log("考勤组适用范围数量: {}, groupId: {}", rangeList.size(), attendanceGroup.getId());

            // 3. 检查是否已存在该考勤组的补卡规则（避免重复创建）

            // 4. 创建补卡规则配置
            ReissueCardConfigDO reissueCardConfig = ruleCreationService.createAttendanceGroupReissueCardConfig(attendanceGroup);
            if (reissueCardConfig == null) {
                log.error("创建考勤组级别的补卡规则失败, groupId: {}", attendanceGroup.getId());
                XxlJobLogger.log("创建考勤组级别的补卡规则失败, groupId: {}", attendanceGroup.getId());
                return;
            }

            log.info("考勤组级别的补卡规则创建成功, configId: {}, configNo: {}, groupId: {}",
                    reissueCardConfig.getId(), reissueCardConfig.getConfigNo(), attendanceGroup.getId());
            XxlJobLogger.log("考勤组级别的补卡规则创建成功, configId: {}, configNo: {}, groupId: {}",
                    reissueCardConfig.getId(), reissueCardConfig.getConfigNo(), attendanceGroup.getId());

            // 5. 创建补卡规则范围（为该考勤组的所有适用范围）
            List<ReissueCardConfigRangeDO> ranges = ruleRangeCreationService.createAttendanceGroupReissueCardConfigRanges(
                    reissueCardConfig, attendanceGroup, rangeList);

            if (CollectionUtils.isNotEmpty(ranges)) {
                log.info("考勤组级别的补卡规则范围创建成功, groupId: {}, configId: {}, 范围数量: {}",
                        attendanceGroup.getId(), reissueCardConfig.getId(), ranges.size());
                XxlJobLogger.log("考勤组级别的补卡规则范围创建成功, groupId: {}, configId: {}, 范围数量: {}",
                        attendanceGroup.getId(), reissueCardConfig.getId(), ranges.size());
            } else {
                log.warn("考勤组级别的补卡规则范围创建完成，但未创建任何范围, groupId: {}, configId: {}",
                        attendanceGroup.getId(), reissueCardConfig.getId());
                XxlJobLogger.log("考勤组级别的补卡规则范围创建完成，但未创建任何范围, groupId: {}, configId: {}",
                        attendanceGroup.getId(), reissueCardConfig.getId());
            }

            log.info("考勤组级别的补卡规则处理完成, groupId: {}, configId: {}, 范围数量: {}",
                    attendanceGroup.getId(), reissueCardConfig.getId(), CollectionUtils.size(ranges));
            XxlJobLogger.log("考勤组级别的补卡规则处理完成, groupId: {}, configId: {}, 范围数量: {}",
                    attendanceGroup.getId(), reissueCardConfig.getId(), CollectionUtils.size(ranges));

        } catch (Exception e) {
            log.error("处理考勤组级别的补卡规则失败, groupId: {}, groupName: {}",
                    attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), e);
            XxlJobLogger.log("处理考勤组级别的补卡规则失败, groupId: {}, error: {}",
                    attendanceGroup.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 创建虚拟的HR考勤组信息，用于补卡规则映射
     *
     * @param country 国家代码
     * @return 虚拟HR考勤组
     */
    private HrAttendanceGroupDTO createVirtualHrGroup(String country) {
        HrAttendanceGroupDTO virtualGroup = new HrAttendanceGroupDTO();
        virtualGroup.setId(-1L); // 使用-1表示虚拟HR配置
        virtualGroup.setCountry(country);
        virtualGroup.setPunchConfigName(country + "国家级补卡规则（迁移生成）");

        // 设置生效时间：从2023-01-01 00:00:00开始
        Date effectTime = DateUtil.parse("2023-01-01 00:00:00", DateConvertUtils.FORMAT_DATE_TIME);
        virtualGroup.setEffectTime(effectTime);
        virtualGroup.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        virtualGroup.setIsLatest(BusinessConstant.Y);
        virtualGroup.setStatus(StatusEnum.ACTIVE.getCode());

        return virtualGroup;
    }
}
