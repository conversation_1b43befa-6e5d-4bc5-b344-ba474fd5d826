package com.imile.attendance.form.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.constants.BpmMqTagsConstant;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.driver.DriverAttendanceManage;
import com.imile.attendance.driver.DriverPunchRecordManage;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceSourceTypeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.enums.warehouse.WarehouseTypeEnum;
import com.imile.attendance.form.AttendanceApprovalFormManage;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.BpmAttendanceMqService;
import com.imile.attendance.form.biz.overtime.OvertimeApprovalService;
import com.imile.attendance.form.bo.AttendanceApprovalFormDetailBO;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.DayDurationInfoDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailAbnormalDao;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.publish.AttendanceReportEventPublisher;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.warehouse.WarehouseAttendanceHandlerService;
import com.imile.attendance.warehouse.WarehouseClassesService;
import com.imile.attendance.warehouse.WarehouseUserService;
import com.imile.attendance.warehouse.vo.ClassesDetailVO;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.rpc.constant.RpcConstant;
import com.imile.rpc.utils.InvisibleParamsUtils;
import com.imile.ucenter.api.authenticate.UcenterUtils;
import com.imile.ucenter.api.dto.user.UserInfoDTO;
import com.imile.util.lang.I18nUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/23
 * @Description
 */
@Slf4j
@Service
public class BpmAttendanceMqServiceImpl implements BpmAttendanceMqService {
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private AttendanceApprovalFormManage attendanceApprovalFormManage;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private EmployeePunchRecordService employeePunchRecordService;
    @Resource
    private EmployeeAbnormalAttendanceService employeeAbnormalAttendanceService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private OvertimeApprovalService overtimeApprovalService;
    @Resource
    private DriverPunchRecordManage driverPunchRecordManage;
    @Resource
    private DriverAttendanceManage driverAttendanceManage;
    @Resource
    private AttendanceEmployeeDetailManage employeeDetailManage;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private Executor bizTaskThreadPool;
    @Resource
    private MigrationService migrationService;
    @Resource
    private AttendanceReportEventPublisher publisher;
    @Resource
    private WarehouseAttendanceHandlerService warehouseAttendanceHandlerService;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private WarehouseDetailAbnormalDao warehouseDetailAbnormalDao;
    @Resource
    private WarehouseUserService warehouseUserService;
    @Resource
    private WarehouseClassesService warehouseClassesService;

    @Override
    public void attendanceMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO,
                                    String operateTags) {
        if (approvalPushStatusMsgDTO == null) {
            return;
        }
        if (StringUtils.equalsIgnoreCase(approvalPushStatusMsgDTO.getApprovalPushStatusType(), "NODE")) {
            //只监听整个流程完成
            return;
        }
        if (StringUtils.isBlank(approvalPushStatusMsgDTO.getBizId())) {
            return;
        }
        AttendanceFormDetailBO formManage = attendanceFormManage.getFormDetailById(Long.valueOf(approvalPushStatusMsgDTO.getBizId()));
        if (formManage == null || formManage.getFormDO() == null) {
            log.info("attendanceMqHandler | 申请单据不存在, formId:{}" + approvalPushStatusMsgDTO.getBizId());
            return;
        }
        AttendanceFormDO formDO = formManage.getFormDO();
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(formDO.getUserId())) {
            log.info("attendanceMqHandler | userInfo is on old attendance, userCode:{}" + formDO.getUserCode());
            return;
        }
        formDO.setApprovalProcessInfo(JSON.toJSONString(approvalPushStatusMsgDTO));
        this.updateUserBuild(approvalPushStatusMsgDTO, formDO);
        //-1拒绝
        if (approvalPushStatusMsgDTO.getStatus().equals(-1)) {
             /*
               请假申请终止按钮--->返还假期：增加假期余额、扣减假期已使用余额
                   请假之后的终止按钮：返还假期：增加假期余额、扣减假期已使用余额
                   驳回之后的终止按钮：不操作【驳回操作的时候会，增加假期余额、扣减假期已使用余额，终止就不需要了】
                   销假之后的终止按钮：不操作【销假只有在审核通过的时候才增加假期余额、扣减假期已使用余额】
            */
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            // 处理用户请假记录
            UserLeaveRecordDO userLeaveRecord = commonFormOperationService.getUserLeaveStageDetailList(formManage, userLeaveStageDetailInfoList, approvalPushStatusMsgDTO.getStatus());
            // 处理补卡次数
            UserCycleReissueCardCountDO userCardConfigDO = commonFormOperationService.reissueCardCountHandler(formManage);
            // 处理异常状态
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = this.abnormalStatusHandler(formManage, AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode(), operateTags);
            formDO.setFormStatus(FormStatusEnum.TERMINATED.getCode());
            // 落库
            attendanceFormManage.updateMqForm(formDO, userCardConfigDO, abnormalAttendanceDO, userLeaveStageDetailInfoList, userLeaveRecord);
            return;
        }
        //-2驳回
        if (approvalPushStatusMsgDTO.getStatus().equals(-2)) {
            /*
               请假申请驳回按钮--->直接返还假期：增加假期余额、扣减假期已使用余额
                   请假之后的驳回按钮：返还假期：增加假期余额、扣减假期已使用余额
                   驳回之后重新提交的驳回按钮：不操作【驳回操作的时候会，增加假期余额、扣减假期已使用余额，终止就不需要了】
                   销假之后的驳回按钮：不操作【销假只有在审核通过的时候才增加假期余额、扣减假期已使用余额】
            */
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            // 处理用户请假记录
            UserLeaveRecordDO userLeaveRecord = commonFormOperationService.getUserLeaveStageDetailList(formManage, userLeaveStageDetailInfoList, approvalPushStatusMsgDTO.getStatus());
            formDO.setFormStatus(FormStatusEnum.REJECT.getCode());
            // 处理补卡次数
            UserCycleReissueCardCountDO userCardConfigDO = commonFormOperationService.reissueCardCountHandler(formManage);
            // 处理异常状态
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalStatusHandler(formManage, AbnormalAttendanceStatusEnum.REJECT.getCode(), operateTags);
            if (abnormalAttendanceDO != null) {
                //特殊逻辑，如果请假单是从异常处理发起的，如果拒绝了，那么这个请假单就是终止状态，异常如果再次处理，会生成新的单据
                formDO.setFormStatus(FormStatusEnum.TERMINATED.getCode());
            }
            // 落库
            attendanceFormManage.updateMqForm(formDO, userCardConfigDO, abnormalAttendanceDO, userLeaveStageDetailInfoList, userLeaveRecord);
            return;
        }
        //-3撤回
        if (approvalPushStatusMsgDTO.getStatus().equals(-3)) {
            /*
               请假申请撤销按钮--->返还假期：增加假期余额、扣减假期已使用余额
                   请假之后的撤销按钮：返还假期：增加假期余额、扣减假期已使用余额
                   驳回之后的撤销按钮：不操作【驳回操作的时候会，增加假期余额、扣减假期已使用余额，撤销就不需要了】
                   销假之后的撤销按钮：不操作【销假只有在审核通过的时候才增加假期余额、扣减假期已使用余额】
            */
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            // 处理用户请假记录
            UserLeaveRecordDO userLeaveRecord = commonFormOperationService.getUserLeaveStageDetailList(formManage, userLeaveStageDetailInfoList, approvalPushStatusMsgDTO.getStatus());
            // 处理补卡次数
            // 已终止、已驳回 撤回时不需要再返还补卡次数
            UserCycleReissueCardCountDO userCardConfigDO = null;
            if (!FormStatusEnum.getCancelFormStatusList().contains(formDO.getFormStatus())) {
                userCardConfigDO = commonFormOperationService.reissueCardCountHandler(formManage);
            }
            formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
            // 处理异常状态
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalStatusHandler(formManage, AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode(), operateTags);
            // 落库
            attendanceFormManage.updateMqForm(formDO, userCardConfigDO, abnormalAttendanceDO, userLeaveStageDetailInfoList, userLeaveRecord);
            return;
        }
        //2审批通过
        if (approvalPushStatusMsgDTO.getStatus().equals(2)) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                leaveMqPassHandler(formManage);
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                leaveMqPassHandler(formManage);
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD.getCode()) ||
                    StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.WAREHOUSE_REISSUE_CARD.getCode())) {
                reissueCardMqPassHandler(formManage);
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.ADD_DURATION.getCode())) {
                warehouseAttendanceHandlerService.warehouseAddDurationPassHandler(formManage);
            }
        }
    }

    @Override
    public void attendanceRevokeMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO) {
        if (approvalPushStatusMsgDTO == null) {
            return;
        }
        if (StringUtils.equalsIgnoreCase(approvalPushStatusMsgDTO.getApprovalPushStatusType(), "NODE")) {
            //只监听整个流程完成
            return;
        }
        if (StringUtils.isBlank(approvalPushStatusMsgDTO.getBizId())) {
            return;
        }
        AttendanceFormDetailBO formDetailBO = attendanceFormManage.getFormDetailById(Long.valueOf(approvalPushStatusMsgDTO.getBizId()));
        if (formDetailBO == null || formDetailBO.getFormDO() == null) {
            log.info("attendanceMqHandler | 申请单据不存在, formId:{}" + approvalPushStatusMsgDTO.getBizId());
            return;
//            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
//                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(formDO.getUserId())) {
            log.info("attendanceRevokeMqHandler | userInfo is on old attendance, userCode:{}" + formDO.getUserCode());
            return;
        }
        formDO.setApprovalProcessInfo(JSON.toJSONString(approvalPushStatusMsgDTO));
        this.updateUserBuild(approvalPushStatusMsgDTO, formDO);
        //-1拒绝
        if (approvalPushStatusMsgDTO.getStatus().equals(-1)) {
            // 销假情况：撤回、终止、驳回三种状态 不操作假期余额和已使用假期余额,所以也不需要新增用户请假销假记录
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            formDO.setFormStatus(FormStatusEnum.TERMINATED.getCode());
            attendanceFormManage.updateMqForm(formDO, null, null, userLeaveStageDetailInfoList, null);
            return;
        }
        //-2驳回
        if (approvalPushStatusMsgDTO.getStatus().equals(-2)) {
            // 销假情况：撤回、终止、驳回三种状态 不操作假期余额和已使用假期余额
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            formDO.setFormStatus(FormStatusEnum.REJECT.getCode());
            attendanceFormManage.updateMqForm(formDO, null, null, userLeaveStageDetailInfoList, null);
            return;
        }
        //-3撤回
        if (approvalPushStatusMsgDTO.getStatus().equals(-3)) {
            // 销假情况：撤回、终止、驳回三种状态 不操作假期余额和已使用假期余额
            List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList = Lists.newArrayList();
            formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
            attendanceFormManage.updateMqForm(formDO, null, null, userLeaveStageDetailInfoList, null);
            return;
        }
        //2审批通过
        if (approvalPushStatusMsgDTO.getStatus().equals(2)) {
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE_REVOKE.getCode())) {
                revokeLeaveMqPassHandler(formDetailBO);
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE_REVOKE.getCode())) {
                revokeLeaveMqPassHandler(formDetailBO);
            }
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD_REVOKE.getCode())) {
                revokeReissueCardMqPassHandler(formDetailBO);
            }
        }
    }

    @Override
    public void attendanceOvertimeMqHandler(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO) {
        if (ObjectUtil.isNull(approvalPushStatusMsgDTO)) {
            return;
        }
        log.info("HR监听考勤(加班)返回结果处理:{}", approvalPushStatusMsgDTO);
        if (StringUtils.equalsIgnoreCase(approvalPushStatusMsgDTO.getApprovalPushStatusType(), "NODE")) {
            //只监听整个流程完成
            return;
        }
        if (StringUtils.isBlank(approvalPushStatusMsgDTO.getBizId())) {
            return;
        }
        log.info("HR监听考勤(加班)返回结果处理:加班单据id：{}", approvalPushStatusMsgDTO.getBizId());
        AttendanceApprovalFormDetailBO approvalFormDetail = attendanceApprovalFormManage.getApprovalFormDetailById(Long.valueOf(approvalPushStatusMsgDTO.getBizId()));
        if (ObjectUtil.isNull(approvalFormDetail)
                || ObjectUtil.isNull(approvalFormDetail.getApprovalForm())) {
            log.info("HR监听考勤(加班)返回结果处理:加班单据不存在");
            return;
        }

        AttendanceApprovalFormDO approvalForm = approvalFormDetail.getApprovalForm();
        // 不在灰度人员里走旧系统，新系统不处理
        AttendanceUser userInfo = userService.getByUserCode(approvalForm.getApplyUserCode());
        if (Objects.isNull(userInfo)) {
            log.info("attendanceOvertimeMqHandler | userInfo is not exist userCode:{}" + approvalForm.getApplyUserCode());
            return;
        }
        if (!migrationService.verifyUserIsEnableNewAttendance(userInfo.getId())) {
            log.info("attendanceOvertimeMqHandler | userInfo is on old attendance, userCode:{}" + userInfo.getUserCode());
            return;
        }
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();

        approvalForm.setApprovalProcessInfo(JSON.toJSONString(approvalPushStatusMsgDTO));
        this.updateApprovalUserBuild(approvalPushStatusMsgDTO, approvalForm);
        Integer status = approvalPushStatusMsgDTO.getStatus();
        switch (status) {
            case -1:
                // 终止
                approvalForm.setFormStatus(FormStatusEnum.TERMINATED.getCode());
                attendanceApprovalManage.approvalFormUpdate(approvalForm, null);
                break;
            case -2:
                // 驳回
                approvalForm.setFormStatus(FormStatusEnum.REJECT.getCode());
                attendanceApprovalManage.approvalFormUpdate(approvalForm, null);
                break;
            case -3:
                // 撤回
                approvalForm.setFormStatus(FormStatusEnum.CANCEL.getCode());
                attendanceApprovalManage.approvalFormUpdate(approvalForm, null);
                break;
            case 2:
                // 审批通过
                if (ObjectUtil.equal(approvalForm.getFormType(), FormTypeEnum.OVER_TIME.getCode())) {
                    overTimeHandler(approvalForm, approvalFormUserInfoList);
                }
                break;
            default:
                break;
        }
    }

    /**
     * 处理用户信息
     *
     * @param approvalPushStatusMsgDTO
     * @param formDO
     */
    private void updateUserBuild(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO,
                                 AttendanceFormDO formDO) {
        String userCode = approvalPushStatusMsgDTO.getApplyUserCode();
        if (CollectionUtils.isNotEmpty(approvalPushStatusMsgDTO.getApprovalProcessInfoList())) {
            userCode = approvalPushStatusMsgDTO.getApprovalProcessInfoList().get(0).getApprovalUserCode();
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserCode(userCode);
        userInfo.setUserName("admin");
        AttendanceUser byUserCode = userService.getByUserCode(userCode);
        if (Objects.nonNull(byUserCode)) {
            formDO.setLastUpdUserName(byUserCode.getUserName());
            userInfo.setUserName(byUserCode.getUserName());
        }
        userInfo.setOrgId(10L);
        InvisibleParamsUtils.set(RpcConstant.LOCAL_USER_KEY, JSON.toJSONString(userInfo));
        UcenterUtils.setUserInfo(userInfo);
        formDO.setLastUpdDate(new Date());
        formDO.setLastUpdUserCode(userCode);
    }

    /**
     * 更新审批用户构建
     *
     * @param approvalPushStatusMsgDTO 审批结果
     * @param approvalForm             审批单
     */
    private void updateApprovalUserBuild(ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO,
                                         AttendanceApprovalFormDO approvalForm) {
        String userCode = approvalPushStatusMsgDTO.getApplyUserCode();
        if (CollectionUtils.isNotEmpty(approvalPushStatusMsgDTO.getApprovalProcessInfoList())) {
            userCode = approvalPushStatusMsgDTO.getApprovalProcessInfoList().get(0).getApprovalUserCode();
        }

        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setUserCode(userCode);
        userInfo.setUserName("admin");
        List<AttendanceUser> userInfoDOList = userService.listUsersByUserCodes(Collections.singletonList(userCode));
        if (CollectionUtils.isNotEmpty(userInfoDOList)) {
            approvalForm.setLastUpdUserName(userInfoDOList.get(0).getUserName());
            userInfo.setUserName(userInfoDOList.get(0).getUserName());
        }
        userInfo.setOrgId(10L);
        InvisibleParamsUtils.set(RpcConstant.LOCAL_USER_KEY, JSON.toJSONString(userInfo));
        UcenterUtils.setUserInfo(userInfo);
        approvalForm.setLastUpdDate(new Date());
        approvalForm.setLastUpdUserCode(userCode);
    }

    /**
     * 处理异常状态
     *
     * @param formDetailBO
     * @param status
     * @param operateTags
     * @return
     */
    private EmployeeAbnormalAttendanceDO abnormalStatusHandler(AttendanceFormDetailBO formDetailBO,
                                                               String status,
                                                               String operateTags) {
        //可以为空，单独的请假审批被驳回，不是从异常考勤那发起的
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        List<AttendanceFormRelationDO> abnormalRelationList = relationDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(),
                        ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalRelationList)) {
            return null;
        }
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(abnormalRelationList.get(0).getRelationId()));
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
        }
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
        if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(),
                AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
            if (Objects.equals(BpmMqTagsConstant.WAREHOUSE_REISSUE_CARD, operateTags)
                    || Objects.equals(BpmMqTagsConstant.REISSUE_CARD, operateTags)) {
                return abnormalAttendanceDO;
            }
            throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
        }
        abnormalAttendanceDO.setStatus(status);
        BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
        return abnormalAttendanceDO;
    }

    /**
     * 请假/外勤审批通过
     * 注意:审批通过并不扣除假期，只有当天考勤结算才真正扣除假期
     * 请假周期可能会跨域历史和未来，所以历史天数的异常数据需要删除，需要看历史天数的排班
     */
    private void leaveMqPassHandler(AttendanceFormDetailBO formDetailBO) {
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        formDO.setFormStatus(FormStatusEnum.PASS.getCode());

        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        if (CollectionUtils.isEmpty(attrDOS)) {
            attrDOS = new ArrayList<>();
        }
        //如果是异常处理发起的，需要将异常状态置为处理完成
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        if (CollectionUtils.isEmpty(relationDOS)) {
            relationDOS = new ArrayList<>();
        }
        List<AttendanceFormRelationDO> abnormalRelationList = relationDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(),
                        ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalRelationList)) {
            attendanceApprovalManage.leaveMqPassUpdate(formDO, null);
        } else {
            List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(abnormalRelationList.get(0).getRelationId()));
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
            if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
            }
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
            BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalAttendanceDO);
            attendanceApprovalManage.leaveMqPassUpdate(formDO, abnormalAttendanceDO);
        }

        //重新计算当天的考勤，是在另一个事物中，不要放在一起
        //看看需不需要计算考勤,如果你请的是未来的假，肯定不需要，历史的需要立刻计算考勤，注解请假周期包含今天的情况
        attendanceHandler(formDO, attrDOS);

        // 计算司机考勤，如果你请的是未来的假，肯定不需要，历史的需要立刻计算考勤，注解请假周期包含今天的情况
        attendanceDriverHandler(formDO, attrDOS);
    }

    /**
     * 补卡审批通过(补卡申请是一定要关联异常考勤的)
     */
    private void reissueCardMqPassHandler(AttendanceFormDetailBO formDetailBO) {
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        formDO.setFormStatus(FormStatusEnum.PASS.getCode());
        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> attrMap = attrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        AttendanceFormAttrDO reissueCardDayIdAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode());
        AttendanceFormAttrDO correctPunchTimeAttrDO = attrMap.get(ApplicationFormAttrKeyEnum.correctPunchTime.getLowerCode());

        if (reissueCardDayIdAttrDO == null || correctPunchTimeAttrDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ATTENDANCE_APPROVAL_BASIC_INFO_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ATTENDANCE_APPROVAL_BASIC_INFO_EMPTY.getDesc()));
        }
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        Long reissueCardDayId = Long.valueOf(reissueCardDayIdAttrDO.getAttrValue());
        Date correctPunchTime = DateUtil.parse(correctPunchTimeAttrDO.getAttrValue(), "yyyy-MM-dd HH:mm:ss");

        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        List<AttendanceFormRelationDO> abnormalRelationList = relationDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(),
                        ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        EmployeeAbnormalAttendanceDO abnormalAttendanceDO = null;
        //自由补卡异常类型可以为空
        if (CollectionUtils.isNotEmpty(abnormalRelationList)) {
            List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(abnormalRelationList.get(0).getRelationId()));
            if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
                throw BusinessException.get(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.USER_NOT_HAVE_ABNORMAL_ATTENDANCE_RECORD_ON_THE_CURRENT_DAY.getDesc()));
            }
            abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
            if (!StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                //说明单据状态已完结，不抛异常直接退出
                return;
            }
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.PASS.getCode());
            BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalAttendanceDO);
            reissueCardDayId = abnormalAttendanceDO.getDayId();
        }

        //打卡表新增打卡记录
        EmployeePunchRecordDO employeePunchRecordDO = new EmployeePunchRecordDO();
        employeePunchRecordDO.setId(defaultIdWorker.nextId());
        employeePunchRecordDO.setSourceType(SourceTypeEnum.REISSUE_CARD.name());
        employeePunchRecordDO.setUserCode(formDO.getUserCode());
        employeePunchRecordDO.setCountry(userInfo.getLocationCountry());
        employeePunchRecordDO.setDayId(reissueCardDayId.toString());
        employeePunchRecordDO.setPunchTime(correctPunchTime);
        employeePunchRecordDO.setDeptId(userInfo.getDeptId());
        employeePunchRecordDO.setEmployeeType(userInfo.getEmployeeType());
        employeePunchRecordDO.setFormId(formDO.getId());
        employeePunchRecordDO.setLongitude(BigDecimal.ZERO);
        employeePunchRecordDO.setLatitude(BigDecimal.ZERO);
        employeePunchRecordDO.setClassId(Objects.nonNull(abnormalAttendanceDO) ? abnormalAttendanceDO.getPunchClassConfigId() : null);
        BaseDOUtil.fillDOInsertByUsrOrSystem(employeePunchRecordDO);
        employeePunchRecordDO.setCreateUserCode(formDO.getUserCode());
        employeePunchRecordDO.setCreateUserName(formDO.getUserName());
        employeePunchRecordDO.setLastUpdUserCode(formDO.getUserCode());
        employeePunchRecordDO.setLastUpdUserName(formDO.getUserName());

        //生成仓内打卡记录
        WarehouseRecordDO warehouseRecordDO = null;
        if (warehouseUserService.isWarehouseSupportUser(userInfo)) {
            log.info("单据号:{},【补卡】审核通过，同步仓内打卡记录", formDO.getId());
            WarehouseDetailDO warehouseDetailDO = null;
            //兼容自由补卡逻辑 无异常关联单据
            if (Objects.nonNull(abnormalAttendanceDO)) {
                WarehouseDetailAbnormalDO warehouseDetailAbnormalDO = warehouseDetailAbnormalDao.selectByAbnormalId(abnormalAttendanceDO.getId());
                if (Objects.nonNull(warehouseDetailAbnormalDO)) {
                    warehouseDetailDO = warehouseDetailDao.selectById(warehouseDetailAbnormalDO.getWarehouseDetailId());
                }
            } else {
                List<WarehouseDetailDO> warehouseDetailDOS = warehouseDetailDao.selectByWarehouseDateAndUserId(DateUtil.parse(reissueCardDayId.toString(), DateFormatterUtil.FORMAT_YYYYMMDD), userInfo.getId());
                if (CollectionUtils.isNotEmpty(warehouseDetailDOS)) {
                    warehouseDetailDO = warehouseDetailDOS.get(0);
                }
            }

            if (Objects.nonNull(warehouseDetailDO)) {
                List<ClassesDetailVO> classesDetailVOS = warehouseClassesService.getClassesDetail(warehouseDetailDO.getClassesId());
                warehouseRecordDO = new WarehouseRecordDO();
                warehouseRecordDO.setId(defaultIdWorker.nextId());
                warehouseRecordDO.setCountry(userInfo.getLocationCountry());
                warehouseRecordDO.setCity(userInfo.getLocationCity());
                warehouseRecordDO.setOcId(userInfo.getOcId());
                warehouseRecordDO.setUserOcId(userInfo.getOcId());
                warehouseRecordDO.setVendorId(userInfo.getVendorId());
                warehouseRecordDO.setVendorCode(userInfo.getVendorCode());
                warehouseRecordDO.setUserVendorId(userInfo.getVendorId());
                warehouseRecordDO.setUserVendorCode(userInfo.getVendorCode());
                warehouseRecordDO.setUserId(userInfo.getId());
                warehouseRecordDO.setUserCode(userInfo.getUserCode());
                warehouseRecordDO.setWarehouseTime(correctPunchTime);
                warehouseRecordDO.setRecordType(getRecordType(classesDetailVOS, correctPunchTime));
                warehouseRecordDO.setWarehouseDate(DateUtil.parseDate(DateUtil.format(correctPunchTime, DateFormatterUtil.FORMAT_YYYY_MM_DD)));
                warehouseRecordDO.setWarehouseDetailId(warehouseDetailDO.getId());
                warehouseRecordDO.setSource(BusinessConstant.ONE);
                warehouseRecordDO.setIsEarlyOut(BusinessConstant.ZERO);
                BaseDOUtil.fillDOInsert(warehouseRecordDO);
            }
        }
        attendanceApprovalManage.reissueCardMqPassUpdate(formDO, employeePunchRecordDO, abnormalAttendanceDO, warehouseRecordDO);
        //重新计算当天的考勤，是在另一个事物中，不要放在一起
        try {
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO
                    .builder()
                    .userCodes(formDO.getUserCode())
                    .attendanceDayId(reissueCardDayId)
                    .build();
            attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);

            // 补卡审核通过仓内考勤处理
            warehouseAttendanceHandlerService.warehouseAttendanceReissueAuditPassHandler(userInfo, Objects.nonNull(abnormalAttendanceDO) ? abnormalAttendanceDO.getId() : null, reissueCardDayId);
        } catch (Exception e) {
            e.getStackTrace();
            log.info("补卡申请通过，重新计算当天考勤失败，单据号:{},当天:{},异常信息为:{}", formDO.getApplicationCode(), reissueCardDayId, e.getMessage());
        }

    }

    private Integer getRecordType(List<ClassesDetailVO> classesDetailVOS, Date correctPunchTime) {
        LocalTime attendanceTime = LocalTime.parse(DateUtil.formatTime(correctPunchTime));
        for (ClassesDetailVO classesDetailVO : classesDetailVOS) {
            LocalTime punchInTime = LocalTime.parse(classesDetailVO.getPunchInTime());
            LocalTime earliestPunchInTime = LocalTime.parse(classesDetailVO.getEarliestPunchInTime());
            LocalTime punchOutTime = LocalTime.parse(classesDetailVO.getPunchOutTime());
            BigDecimal elasticTime = classesDetailVO.getElasticTime();

            LocalTime punchInElasticTime = punchInTime.plusMinutes(Convert.toLong(new BigDecimal("60").multiply(elasticTime)));
            if (attendanceTime.compareTo(earliestPunchInTime) > -1 && attendanceTime.compareTo(punchInElasticTime) < 1) {
                return WarehouseTypeEnum.IN.getCode();
            }

            if (attendanceTime.compareTo(punchOutTime) > -1) {
                return WarehouseTypeEnum.OUT.getCode();
            }
        }
        return WarehouseTypeEnum.INIT.getCode();
    }

    /**
     * 请假/外勤 销假审批通过
     *
     * @param formDetailBO
     */
    private void revokeLeaveMqPassHandler(AttendanceFormDetailBO formDetailBO) {
        // 获取销假申请单据
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        formDO.setFormStatus(FormStatusEnum.PASS.getCode());

        // 获取销假申请单据详情信息
        List<AttendanceFormAttrDO> attrDOS = formDetailBO.getAttrDOList();
        if (CollectionUtils.isEmpty(attrDOS)) {
            attrDOS = new ArrayList<>();
        }
        //找到这笔撤销申请所关联的申请单
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        if (CollectionUtils.isEmpty(relationDOS)) {
            relationDOS = new ArrayList<>();
        }
        // 销假申请单对应的原始请假单关系数据
        List<AttendanceFormRelationDO> formRelationList = relationDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(),
                        ApplicationRelationTypeEnum.APPLICATION_FORM.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formRelationList)) {
            throw BusinessException.get(ErrorCodeEnum.REVOKE_FORM_RELATION_FORM_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.REVOKE_FORM_RELATION_FORM_EMPTY.getDesc()));
        }
        //发起申请单信息
        AttendanceFormDetailBO applyFormDetailBO = attendanceFormManage.getFormDetailById(formRelationList.get(0).getRelationId());
        if (applyFormDetailBO == null || applyFormDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO applyFormDO = applyFormDetailBO.getFormDO();
        applyFormDO.setFormStatus(FormStatusEnum.WITHDRAWN.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(applyFormDO);
        List<AttendanceFormAttrDO> applyAttrDOS = applyFormDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> applyAttrMap = applyAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        AttendanceFormAttrDO isRevokeAttrDO = applyAttrMap.get(ApplicationFormAttrKeyEnum.isRevoke.getLowerCode());
        isRevokeAttrDO.setAttrValue(BusinessConstant.Y.toString());
        BaseDOUtil.fillDOUpdateByUserOrSystem(isRevokeAttrDO);

        //删除正常考勤表的数据
        List<AttendanceEmployeeDetailDO> employeeDetailDOS = employeeDetailManage.selectByFormIdList(Arrays.asList(applyFormDetailBO.getFormDO().getId()));
        employeeDetailDOS.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            item.setRemark("申请单据撤销删除");
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });
        List<UserLeaveStageDetailDO> updateStageList = new ArrayList<>();
        List<UserLeaveRecordDO> addLeaveRecordList = new ArrayList<>();

        // 请假-销假的情况才会返回假期、生成用户假期记录【根据销假单找到请假申请单，然后根据原请假申请单，返还假期、生成用户假期记录】
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE_REVOKE.getCode())) {
            UserLeaveRecordDO userLeaveRecord = commonFormOperationService.handlerUserLeaveStageDetailList(applyFormDetailBO, updateStageList, "【clover审批】审核通过审核中销假申请单");
            addLeaveRecordList.add(userLeaveRecord);
        }
        // 审批通过处理
        attendanceApprovalManage.revokeLeaveMqPassUpdate(Arrays.asList(formDO, applyFormDO),
                isRevokeAttrDO, updateStageList, addLeaveRecordList, employeeDetailDOS);

        //重新计算当天的考勤，是在另一个事物中，不要放在一起
        //看看需不需要计算考勤,如果你请的是未来的假，肯定不需要，历史的需要立刻计算考勤，注解请假周期包含今天的情况
        attendanceHandler(applyFormDO, applyAttrDOS);

        // 计算司机考勤，如果你请的是未来的假，肯定不需要，历史的需要立刻计算考勤，注解请假周期包含今天的情况
        attendanceDriverRevokeLeaveHandler(applyFormDO, applyAttrDOS);
    }


    /**
     * 补卡-撤销审批通过
     */
    private void revokeReissueCardMqPassHandler(AttendanceFormDetailBO formDetailBO) {
        AttendanceFormDO formDO = formDetailBO.getFormDO();
        formDO.setFormStatus(FormStatusEnum.PASS.getCode());

        //找到这笔撤销申请所关联的申请单
        List<AttendanceFormRelationDO> relationDOS = formDetailBO.getRelationDOList();
        List<AttendanceFormRelationDO> formRelationList = relationDOS
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(),
                        ApplicationRelationTypeEnum.APPLICATION_FORM.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formRelationList)) {
            throw BusinessException.get(ErrorCodeEnum.REVOKE_FORM_RELATION_FORM_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.REVOKE_FORM_RELATION_FORM_EMPTY.getDesc()));
        }
        //发起申请单信息
        AttendanceFormDetailBO applyFormDetailBO = attendanceFormManage.getFormDetailById(formRelationList.get(0).getRelationId());
        if (applyFormDetailBO == null || applyFormDetailBO.getFormDO() == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        AttendanceFormDO applyFormDO = applyFormDetailBO.getFormDO();
        applyFormDO.setFormStatus(FormStatusEnum.WITHDRAWN.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(applyFormDO);
        List<AttendanceFormAttrDO> applyAttrDOS = applyFormDetailBO.getAttrDOList();
        Map<String, AttendanceFormAttrDO> applyAttrMap = applyAttrDOS.stream().collect(Collectors.toMap(o -> o.getAttrKey(), o -> o, (v1, v2) -> v1));
        AttendanceFormAttrDO isRevokeAttrDO = applyAttrMap.get(ApplicationFormAttrKeyEnum.isRevoke.getLowerCode());
        isRevokeAttrDO.setAttrValue(BusinessConstant.Y.toString());
        BaseDOUtil.fillDOUpdateByUserOrSystem(isRevokeAttrDO);
        List<AttendanceFormRelationDO> applyRelationDOS = applyFormDetailBO.getRelationDOList();
        if (CollectionUtils.isEmpty(applyRelationDOS)) {
            applyRelationDOS = new ArrayList<>();
        }
        Long reissueCardDayId = null;
        List<AttendanceFormRelationDO> applyAbnormalRelationList = applyRelationDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(applyAbnormalRelationList)) {
            List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceService.selectByIdList(Arrays.asList(applyAbnormalRelationList.get(0).getRelationId()));
            if (CollectionUtils.isNotEmpty(abnormalAttendanceDOList)) {
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalAttendanceDOList.get(0);
                if (!AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormalAttendanceDO.getStatus())) {
                    throw BusinessException.get(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ABNORMAL_HAS_BEEN_HANDLER.getDesc()));
                }
                reissueCardDayId = abnormalAttendanceDO.getDayId();
            }
        }

        List<AttendanceFormAttrDO> reissueCardDayIdDO = formDetailBO.getAttrDOList()
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                        ApplicationFormAttrKeyEnum.reissueCardDayId.getLowerCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reissueCardDayIdDO)) {
            reissueCardDayId = Long.valueOf(reissueCardDayIdDO.get(0).getAttrValue());
        }

        //撤销审批通过，补卡剩余次数需要加1
        UserCycleReissueCardCountDO cardConfigDO = commonFormOperationService.reissueCardCountHandler(applyFormDetailBO);

        //删除已经落库的打卡数据
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setFormId(applyFormDetailBO.getFormDO().getId());
        List<EmployeePunchRecordDO> punchRecordDOS = employeePunchRecordService.listRecords(punchCardRecordQuery);
        punchRecordDOS.forEach(item -> {
            item.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });
        attendanceApprovalManage.revokeReissueCardMqPassUpdate(Arrays.asList(formDO, applyFormDO),
                isRevokeAttrDO, punchRecordDOS, cardConfigDO);

        //重新计算当天的考勤，是在另一个事务中，不要放在一起
        try {
            if (Objects.isNull(reissueCardDayId)) {
                return;
            }
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = AttendanceCalculateHandlerDTO
                    .builder()
                    .userCodes(formDO.getUserCode())
                    .attendanceDayId(reissueCardDayId)
                    .build();
            attendanceGenerateService.attendanceCalculateHandler(dayAttendanceHandlerDTO);
        } catch (Exception e) {
            e.getStackTrace();
            log.info("补卡撤销通过，重新计算当天考勤失败，单据号:{},异常信息为:{}", formDO.getApplicationCode(), e.getMessage());
        }
    }

    /**
     * 请假/外勤审批通过，计算考勤
     *
     * @param formDO
     * @param attrDOS
     */
    private void attendanceHandler(AttendanceFormDO formDO,
                                   List<AttendanceFormAttrDO> attrDOS) {
        List<AttendanceFormAttrDO> startDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> endDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> configId = new ArrayList<>();
        List<AttendanceFormAttrDO> leaveName = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            configId = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
            leaveName = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        }
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(startDateDO) || CollectionUtils.isEmpty(endDateDO)) {
            log.info("attendanceHandler：请假/外勤申请通过：开始时间或结束时间为空，单据号：{}", formDO.getApplicationCode());
            return;
        }
        if (ObjectUtil.equal(formDO.getFormType(), FormTypeEnum.LEAVE.getCode()) && (CollUtil.isEmpty(leaveName))) {
            log.info("attendanceHandler：请假申请通过：请假类型为空，单据号：{}", formDO.getApplicationCode());
            return;
        }
        String leaveNameString = "";
        if (CollUtil.isNotEmpty(leaveName)) {
            leaveNameString = leaveName.get(0).getAttrValue();
        }
        Date startDate = DateUtil.parse(startDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(endDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));

        CompanyLeaveConfigDO companyLeaveConfigDO = null;
        if (CollectionUtils.isNotEmpty(configId)
                && StringUtils.isNotBlank(configId.get(0).getAttrValue())) {
            companyLeaveConfigDO = companyLeaveConfigService.getById(Long.valueOf(configId.get(0).getAttrValue()));
        }

        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(formDO.getUserId(), startDate, endDate,
                companyLeaveConfigDO, dayDurationInfoDTOList);
        // 将dayDurationInfoDTOList按照dayId倒序排序
        List<DayDurationInfoDTO> reversedDurationInfoList = dayDurationInfoDTOList.stream().sorted(Comparator.comparing(DayDurationInfoDTO::getDayId).reversed()).collect(Collectors.toList());
        // 获取dayDurationInfoDTOList最大的dayId
        if (CollUtil.isEmpty(dayDurationInfoDTOList)) {
            log.info("attendanceHandler：请假/外勤申请通过：计算每天请假时长为空，单据号：{}", formDO.getApplicationCode());
            return;
        }
        Map<Long, DayDurationInfoDTO> dayDurationInfoMap = dayDurationInfoDTOList
                .stream()
                .collect(Collectors.toMap(DayDurationInfoDTO::getDayId, Function.identity(), (v1, v2) -> v1));
        List<AttendanceCalculateHandlerDTO> targetDayAttendanceHandlerList = Lists.newArrayList();
        //只有前2天之前的数据才可以立刻计算
        Long effectDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(new Date(), -2), "yyyyMMdd"));
        // 因为天为单位的请假是：比如你请假两天：开始和结束时间是：2024-01-27 00:00:00 - 2024-01-29 00:00:00，所以需要减一天 ，这边没有按照上面那方式处理，是因为dayDurationInfoDTOList是准确的，不需要减一天
        Long endDayId = reversedDurationInfoList.get(0).getDayId();
        Long tempDayId = startDayId;
        log.info("请假/外勤申请通过，重新计算考勤，单据号：{},startDayId:{},endDayId:{}", formDO.getApplicationCode(), startDayId, endDayId);
        while (tempDayId <= endDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //未来的不用计算
            //if (finalTempDayId > effectDayId) {
            //    continue;
            //}
            // 循环里面不计算考勤了，拼装数据，多线程调用
            AttendanceCalculateHandlerDTO dayAttendanceHandlerDTO = new AttendanceCalculateHandlerDTO();
            dayAttendanceHandlerDTO.setUserCodes(formDO.getUserCode());
            dayAttendanceHandlerDTO.setAttendanceDayId(finalTempDayId);
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                dayAttendanceHandlerDTO.setLeaveHours(calculateLeaveHours(dayDurationInfoMap, finalTempDayId));
                dayAttendanceHandlerDTO.setLeaveType(leaveNameString);
            }
            targetDayAttendanceHandlerList.add(dayAttendanceHandlerDTO);
        }
        //实时计算考勤
        calculateAttendance(targetDayAttendanceHandlerList, formDO);

    }

    /**
     * 请假审批通过-计算司机考勤
     *
     * @param formDO  申请单
     * @param attrDOS 申请单属性
     */
    private void attendanceDriverHandler(AttendanceFormDO formDO,
                                         List<AttendanceFormAttrDO> attrDOS) {
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());

        // 如果查询不到该用户信息，或者该用户不是司机，不需要计算司机考勤
        if (ObjectUtil.isNull(userInfo)) {
            log.info("请假申请通过：被申请人不存在hr");
            return;
        }
        if (ObjectUtil.isNotNull(userInfo.getIsDriver()) && ObjectUtil.notEqual(userInfo.getIsDriver(), BusinessConstant.Y)) {
            log.info("请假申请通过：被申请人不是司机,被申请人信息：{} ", JSON.toJSONString(userInfo));
            return;
        }
        List<AttendanceFormAttrDO> startDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> endDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> configId = new ArrayList<>();
        List<AttendanceFormAttrDO> leaveName = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            configId = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
            leaveName = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(startDateDO) || CollectionUtils.isEmpty(endDateDO) || CollectionUtils.isEmpty(leaveName)) {
            return;
        }
//        List<DriverPunchRecordDO> addDriverPunchRecordList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList = Lists.newArrayList();

        String leaveNameString = leaveName.get(0).getAttrValue();
        Date startDate = DateUtil.parse(startDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(endDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));

        //查询该假期配置
        CompanyLeaveConfigDO companyLeaveConfigDO = null;
        if (CollectionUtils.isNotEmpty(configId)
                && StringUtils.isNotBlank(configId.get(0).getAttrValue())) {
            companyLeaveConfigDO = companyLeaveConfigService.getById(Long.valueOf(configId.get(0).getAttrValue()));
        }

        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(formDO.getUserId(), startDate, endDate,
                companyLeaveConfigDO, dayDurationInfoDTOList);
        // 将dayDurationInfoDTOList按照dayId分组
        Map<Long, List<DayDurationInfoDTO>> dayIdToDayMap = dayDurationInfoDTOList
                .stream()
                .collect(Collectors.groupingBy(DayDurationInfoDTO::getDayId));
        // 将dayDurationInfoDTOList按照dayId倒序排序
        List<DayDurationInfoDTO> reversedDurationInfoList = dayDurationInfoDTOList
                .stream()
                .sorted(Comparator.comparing(DayDurationInfoDTO::getDayId).reversed())
                .collect(Collectors.toList());
        // 获取dayDurationInfoDTOList最大的dayId
        if (CollUtil.isEmpty(dayDurationInfoDTOList)) {
            log.info("attendanceDriverHandler：请假申请通过：计算每天请假时长为空，单据号：{}", formDO.getApplicationCode());
            return;
        }

        //只有前1天之前的数据才可以立刻计算
        Long effectDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyyMMdd"));

        // 这边需要将结束时间减一天，因为假如你请假时间是到2024-01-26 但是endDate存在的是2024-01-27 00:00:00，所以需要减一天  【如果请假是单位是天的，特殊处理】
        //if (ObjectUtil.isNotNull(companyLeaveConfigDO) && ObjectUtil.equal(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
        //    endDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(endDate, -1), DatePattern.PURE_DATE_PATTERN));
        //}
        // 因为天为单位的请假是：比如你请假两天：开始和结束时间是：2024-01-27 00:00:00 - 2024-01-29 00:00:00，所以需要减一天 ，这边没有按照上面那方式处理，是因为dayDurationInfoDTOList是准确的，不需要减一天
        Long endDayId = reversedDurationInfoList.get(0).getDayId();
        Long tempDayId = startDayId;
        while (tempDayId <= endDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //未来的不用计算司机考勤，但是需要新增打卡数据
            if (finalTempDayId > effectDayId) {
                buildDriverPunchRecord(formDO, dayIdToDayMap, leaveNameString, finalTempDayId);
                continue;
            }
            try {
                // 即需要落打卡数据，也需要计算司机考勤
                buildDriverPunchRecord(formDO, dayIdToDayMap, leaveNameString, finalTempDayId);
                // 实时计算司机考勤
                driverAttendanceManage.buildDriverAttendanceDetailData(finalTempDayId, formDO.getUserCode(), formDO.getCountry(), addDriverAttendanceDetailInfoList, delDriverAttendanceDetailInfoList);
            } catch (Exception e) {
                e.getStackTrace();
                log.info("请假申请通过，重新计算当天司机考勤失败，单据号:{},当天:{},异常信息为:{}", formDO.getApplicationCode(), finalTempDayId, e.getMessage());
            }
        }
        // 3.3 生成司机考勤信息
        driverAttendanceManage.batchSaveOrUpdateDriverAttendanceDetail(addDriverAttendanceDetailInfoList,
                delDriverAttendanceDetailInfoList, null);
    }

    /**
     * 司机考勤：销假申请撤销审批通过
     *
     * @param formDO  申请单
     * @param attrDOS 申请单属性
     */
    private void attendanceDriverRevokeLeaveHandler(AttendanceFormDO formDO,
                                                    List<AttendanceFormAttrDO> attrDOS) {
        AttendanceUser userInfo = userService.getByUserId(formDO.getUserId());

        // 如果查询不到该用户信息，或者该用户不是司机，不需要计算司机考勤
        if (ObjectUtil.isNull(userInfo)) {
            log.info("销假申请通过：被申请人不存在hr");
            return;
        }
        if (ObjectUtil.isNotNull(userInfo.getIsDriver()) && ObjectUtil.notEqual(userInfo.getIsDriver(), BusinessConstant.Y)) {
            log.info("销假申请通过：被申请人不是司机,被申请人信息：{} ", JSON.toJSONString(userInfo));
            return;
        }
        List<AttendanceFormAttrDO> startDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> endDateDO = new ArrayList<>();
        List<AttendanceFormAttrDO> configId = new ArrayList<>();
        List<AttendanceFormAttrDO> leaveName = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
            startDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
            endDateDO = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
            configId = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.configID.getLowerCode())).collect(Collectors.toList());
            leaveName = attrDOS.stream().filter(item -> StringUtils.equalsIgnoreCase(item.getAttrKey(),
                    ApplicationFormAttrKeyEnum.leaveType.getLowerCode())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(startDateDO) || CollectionUtils.isEmpty(endDateDO) || CollectionUtils.isEmpty(leaveName)) {
            return;
        }
//        List<DriverPunchRecordDO> addDriverPunchRecordList = Lists.newArrayList();
//        List<DriverPunchRecordDO> delDriverPunchRecordList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList = Lists.newArrayList();

//        String leaveNameString = leaveName.get(0).getAttrValue();
        Date startDate = DateUtil.parse(startDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Date endDate = DateUtil.parse(endDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
        Long startDayId = Long.valueOf(DateUtil.format(startDate, "yyyyMMdd"));

        //查询该假期配置
        CompanyLeaveConfigDO companyLeaveConfigDO = null;
        if (CollectionUtils.isNotEmpty(configId)
                && StringUtils.isNotBlank(configId.get(0).getAttrValue())) {
            companyLeaveConfigDO = companyLeaveConfigService.getById(Long.valueOf(configId.get(0).getAttrValue()));
        }

        //重新计算每天请假时间
        List<DayDurationInfoDTO> dayDurationInfoDTOList = new ArrayList<>();
        commonFormOperationService.dayDurationInfoHandler(formDO.getUserId(), startDate, endDate, companyLeaveConfigDO, dayDurationInfoDTOList);
        // 将dayDurationInfoDTOList按照dayId分组
        Map<Long, List<DayDurationInfoDTO>> dayIdToDayMap = dayDurationInfoDTOList.stream().collect(Collectors.groupingBy(DayDurationInfoDTO::getDayId));
        // 将dayDurationInfoDTOList按照dayId倒序排序
        List<DayDurationInfoDTO> reversedDurationInfoList = dayDurationInfoDTOList.stream().sorted(Comparator.comparing(DayDurationInfoDTO::getDayId).reversed()).collect(Collectors.toList());
        // 获取dayDurationInfoDTOList最大的dayId
        if (CollUtil.isEmpty(dayDurationInfoDTOList)) {
            log.info("attendanceDriverHandler：销假申请通过：计算每天请假时长为空，单据号：{}", formDO.getApplicationCode());
            return;
        }
        // 先删除打卡记录以及打卡记录详情数据
        this.buildRevokeLeaveDriverPunchRecord(formDO, dayDurationInfoDTOList);

        //只有前1天之前的数据才可以立刻计算
        Long effectDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(new Date(), -1), "yyyyMMdd"));

        // 这边需要将结束时间减一天，因为假如你请假时间是到2024-01-26 但是endDate存在的是2024-01-27 00:00:00，所以需要减一天  【如果请假是单位是天的，特殊处理】
        //if (ObjectUtil.isNotNull(companyLeaveConfigDO) && ObjectUtil.equal(companyLeaveConfigDO.getLeaveUnit(), LeaveUnitEnum.DAYS.getCode())) {
        //    endDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(endDate, -1), DatePattern.PURE_DATE_PATTERN));
        //}
        // 因为天为单位的请假是：比如你请假两天：开始和结束时间是：2024-01-27 00:00:00 - 2024-01-29 00:00:00，所以需要减一天 ，这边没有按照上面那方式处理，是因为dayDurationInfoDTOList是准确的，不需要减一天
        Long endDayId = reversedDurationInfoList.get(0).getDayId();
        Long tempDayId = startDayId;
        while (tempDayId <= endDayId) {
            Long finalTempDayId = tempDayId;
            //后移一天
            tempDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(tempDayId.toString()), 1), "yyyyMMdd"));
            //未来的不用计算司机考勤，但是需要删除打卡记录以及打卡记录详情数据
            if (finalTempDayId > effectDayId) {
                //buildDriverPunchRecord(formDO, dayIdToDayMap, leaveTypeString, finalTempDayId, addDriverPunchRecordList);
                continue;
            }
            try {
                // 即需要删除打卡记录、打卡记录详情数据，也需要计算司机考勤
                //buildDriverPunchRecord(formDO, dayIdToDayMap, leaveTypeString, finalTempDayId, addDriverPunchRecordList);
                // 实时计算司机考勤
                //calculateDriverAttendance(formDO.getUserCode(), finalTempDayId, addDriverAttendanceDetailInfoList, delDriverAttendanceDetailInfoList);
                driverAttendanceManage.buildDriverAttendanceDetailData(finalTempDayId, formDO.getUserCode(), formDO.getCountry(), addDriverAttendanceDetailInfoList, delDriverAttendanceDetailInfoList);
            } catch (Exception e) {
                e.getStackTrace();
                log.info("请假申请通过，重新计算当天司机考勤失败，单据号:{},当天:{},异常信息为:{}", formDO.getApplicationCode(), finalTempDayId, e.getMessage());
            }
        }
        // 3.3 生成司机考勤信息
        driverAttendanceManage.batchSaveOrUpdateDriverAttendanceDetail(addDriverAttendanceDetailInfoList, delDriverAttendanceDetailInfoList, null);
    }


    /**
     * 构建司机打卡记录
     *
     * @param formDO          审批单
     * @param dayIdToDayMap   请假每一天的时间段
     * @param leaveTypeString 请假类型
     * @param finalTempDayId  请假申请日
     */
    private void buildDriverPunchRecord(AttendanceFormDO formDO,
                                        Map<Long, List<DayDurationInfoDTO>> dayIdToDayMap,
                                        String leaveTypeString,
                                        Long finalTempDayId) {
        if (ObjectUtil.isNull(formDO)) {
            log.info("审批单对象为null");
            return;
        }
        List<DayDurationInfoDTO> dayDurationInfo = dayIdToDayMap.getOrDefault(finalTempDayId, new ArrayList<>());
        // 计算每天请假总时长-单位分钟
        BigDecimal totalLeaveTime = commonFormOperationService.handlerLeaveTotalTime(dayDurationInfo);
        // 一天的分钟数
        BigDecimal dayMinutes = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES);
        // 天数
        BigDecimal day = totalLeaveTime.divide(dayMinutes, 2, RoundingMode.HALF_UP);
        String operationContent = leaveTypeString +
                "," +
                day + "d" +
                "," + "单据编号：" + formDO.getApplicationCode();

        String operationContentEn = leaveTypeString +
                "," +
                day + "d" +
                "," + "Application Code：" + formDO.getApplicationCode();

        DriverPunchRecordDO driverPunchRecord = new DriverPunchRecordDO();
        driverPunchRecord.setId(defaultIdWorker.nextId());
        driverPunchRecord.setUserCode(formDO.getUserCode());
        driverPunchRecord.setUserName(formDO.getUserName());
        driverPunchRecord.setDayId(finalTempDayId);
        driverPunchRecord.setSourceType(DriverAttendanceSourceTypeEnum.AS.getType());
        driverPunchRecord.setOperationType(DriverAttendanceOperationTypeEnum.LEAVE.getType());
        driverPunchRecord.setOperationContent(operationContent);
        driverPunchRecord.setOperationContentEn(operationContentEn);
        // 这边保存OperatingTime【该字段暂时没用】 为请假当天的时间 比如“2024-01-23 00:00:00，因为请假一个范围不能具体时间
        DateTime dateTime = DateUtil.beginOfDay(DateUtil.parse(finalTempDayId.toString()));
        driverPunchRecord.setOperatingTime(dateTime);
        // number、modify_attendance_type数据库默认值
        driverPunchRecord.setFormId(formDO.getId());
        BaseDOUtil.fillDOInsertByUsrOrSystem(driverPunchRecord);

        // 保存打卡记录详情表：目前只有请假会用到【因为轨迹打卡、签收、修改考勤在打卡记录里面都是一条数据，请假场景：在计算月报的时候，需要每月的不同假期类型的请假时常，所以需要记录请假的详情】

        DriverPunchRecordDetailDO driverPunchRecordDetail = new DriverPunchRecordDetailDO();
        driverPunchRecordDetail.setId(defaultIdWorker.nextId());
        driverPunchRecordDetail.setPunchRecordId(driverPunchRecord.getId());
        driverPunchRecordDetail.setUserCode(formDO.getUserCode());
        driverPunchRecordDetail.setDayId(finalTempDayId);
        driverPunchRecordDetail.setSourceType(DriverAttendanceSourceTypeEnum.AS.getType());
        driverPunchRecordDetail.setOperationType(DriverAttendanceOperationTypeEnum.LEAVE.getType());
        driverPunchRecordDetail.setLeaveType(leaveTypeString);
        driverPunchRecordDetail.setLeaveMinutes(totalLeaveTime);
        BaseDOUtil.fillDOInsertByUsrOrSystem(driverPunchRecordDetail);

        // 这边直接提交保存
        driverPunchRecordManage.savePunchRecordAndDetail(driverPunchRecord, driverPunchRecordDetail);
    }

    /**
     * 删除司机打卡记录以及打卡记录详情数据
     *
     * @param formDO                 申请单
     * @param dayDurationInfoDTOList 每天请假时长
     */
    private void buildRevokeLeaveDriverPunchRecord(AttendanceFormDO formDO,
                                                   List<DayDurationInfoDTO> dayDurationInfoDTOList) {
        if (ObjectUtil.isNull(formDO)) {
            log.info("审批单对象为null");
            return;
        }
        // 获取申请单dayId集合
        List<Long> dayIdList = dayDurationInfoDTOList.stream().map(DayDurationInfoDTO::getDayId).collect(Collectors.toList());
        if (CollUtil.isEmpty(dayIdList)) {
            log.info("attendanceDriverHandler：销假申请通过：dayId集合为空，单据号：{}", formDO.getApplicationCode());
            return;
        }
        // 根据user_code + dayIdList + DriverAttendanceOperationTypeEnum.LEAVE.getType()查询司机请假类型的打卡记录
        DriverPunchRecordDetailQuery query = new DriverPunchRecordDetailQuery();
        query.setDayIdList(dayIdList);
        query.setUserCode(formDO.getUserCode());
        query.setOperationType(DriverAttendanceOperationTypeEnum.LEAVE.getType());
        List<DriverPunchRecordDO> driverPunchRecordList = driverPunchRecordManage.selectPunchRecordDetail(query);
        // 逻辑删除driverPunchRecordList
        if (CollUtil.isNotEmpty(driverPunchRecordList)) {
            driverPunchRecordList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(item);
            });
        }
        // 获取主键id集合
        List<Long> punchRecordIdList = driverPunchRecordList.stream().map(DriverPunchRecordDO::getId).collect(Collectors.toList());

        // 根据punchRecordIdList 查询打卡记录详情
        DriverPunchRecordDetailInfoQuery punchRecordDetailInfoQuery = DriverPunchRecordDetailInfoQuery.builder().punchRecordIdList(punchRecordIdList).build();
        List<DriverPunchRecordDetailDO> driverPunchRecordDetailList = driverPunchRecordManage.queryDriverPunchRecordDetailByCondition(punchRecordDetailInfoQuery);
        // 删除打卡记录详情
        if (CollUtil.isNotEmpty(driverPunchRecordDetailList)) {
            driverPunchRecordDetailList.forEach(item -> {
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(item);
            });
        }
        // 删除数据
        driverPunchRecordManage.updatePunchRecordAndDetail(driverPunchRecordList, driverPunchRecordDetailList);
    }


    /**
     * 加班审批通过处理
     *
     * @param approvalForm             审批单
     * @param approvalFormUserInfoList 审批单详情
     */
    private void overTimeHandler(AttendanceApprovalFormDO approvalForm,
                                 List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        // 获取当天时间
        DateTime date = DateUtil.date();
        // 获取当天时间的dayId
        Long nowDayId = Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN));
        // 审批通过
        approvalForm.setFormStatus(FormStatusEnum.PASS.getCode());
        log.info("HR监听考勤(加班)返回结果处理:加班单据数据来源:{}", approvalForm.getDataSource());
        // 获取用户信息
        List<String> userCodeList = approvalFormUserInfoList
                .stream()
                .map(AttendanceApprovalFormUserInfoDO::getUserCode)
                .collect(Collectors.toList());
        userCodeList.add(approvalForm.getApplyUserCode());
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(userCodeList);
        Map<String, AttendanceUser> userInfoMap = userInfoList
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));

        // 需要计算考勤的人员数据
        List<UserLeaveStageDetailDO> updateUserLeaveStageDetail = Lists.newArrayList();
        List<UserLeaveRecordDO> addUserLeaveRecord = Lists.newArrayList();
        AttendanceUser applyUserInfo = userInfoMap.get(approvalForm.getApplyUserCode());
        log.info("HR监听考勤(加班)返回结果处理:加班单据主键id:{}", approvalForm.getId());
        // 遍历单据详情信息
        for (AttendanceApprovalFormUserInfoDO approvalFormUserInfo : approvalFormUserInfoList) {
            // 判断hrms是否存在该用户
            AttendanceUser userInfo = userInfoMap.get(approvalFormUserInfo.getUserCode());

            if (ObjectUtil.isNull(userInfo)) {
                log.info("HR监听考勤(加班)返回结果处理:hrms不存在该用户");
                continue;
            }

            // 获取用户的加班时长,单位分钟
            BigDecimal overtimeMinutes = approvalFormUserInfo.getEstimateDuration();
            // 获取加班开始时间
            Date startDate = approvalFormUserInfo.getStartDate();
            // 获取加班结束时间
            Date endDate = approvalFormUserInfo.getEndDate();
            // 获取用户的加班日
            Long dayId = approvalFormUserInfo.getDayId();
            // 获取用户code
            String userCode = approvalFormUserInfo.getUserCode();
            // 获取人员国家
            String country = approvalFormUserInfo.getCountry();
            if (ObjectUtil.isNull(dayId) || ObjectUtil.isEmpty(userCode) || ObjectUtil.isEmpty(country)) {
                log.info("HR监听考勤(加班)返回结果处理:加班单据详情信息为空");
                continue;
            }
            log.info("HR监听考勤(加班)返回结果处理:加班单据详情信息:userCode:{},country:{},dayId：{},nowDayId:{}", userCode, country, dayId, nowDayId);
            // 增加用户调休假期余额，也需要根据时间判断
            if (ObjectUtil.equal(approvalForm.getDataSource(), 1) && dayId < nowDayId) {
                // 导入的全部需要计算考勤
                overtimeApprovalService.addOverTime(userInfo, applyUserInfo, userCode, overtimeMinutes, updateUserLeaveStageDetail, date, nowDayId, startDate, endDate, addUserLeaveRecord, "【clover审批】导入类型加班：增加调休假期余额");
            }
            if (ObjectUtil.notEqual(approvalForm.getDataSource(), 1) && dayId < nowDayId) {
                /**
                 * 单人目前也使用预计加班时长计入调休
                 */
                overtimeApprovalService.addOverTime(userInfo, applyUserInfo, userCode, overtimeMinutes, updateUserLeaveStageDetail, date, nowDayId, startDate, endDate, addUserLeaveRecord, "【clover审批】审批通过：增加调休假期余额");
            }
        }
        // 落库
        attendanceApprovalManage.approvalFormOrLeaveUpdate(approvalForm, approvalFormUserInfoList,
                updateUserLeaveStageDetail, addUserLeaveRecord);
        // 日报生成 目前没有批量导入加班，暂时适配一张单据
        if (CollectionUtils.isNotEmpty(approvalFormUserInfoList)) {
            AttendanceApprovalFormUserInfoDO formUserInfoDO = approvalFormUserInfoList.get(0);
            DayReportJobParam param = DayReportJobParam.builder()
                    .userCodeList(userCodeList)
                    .employeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                    .countryArrayList(Arrays.asList(formUserInfoDO.getCountry()))
                    .isUseCustomLocalTime(true)
                    .localDate(DateHelper.getString(formUserInfoDO.getDayId()))
                    .build();
            publisher.sendReportEvent(Arrays.asList(param));
        }
    }

    /**
     * 计算请假小时数
     *
     * @param dayDurationInfoMap
     * @param finalTempDayId
     * @return
     */
    private BigDecimal calculateLeaveHours(Map<Long, DayDurationInfoDTO> dayDurationInfoMap,
                                           Long finalTempDayId) {
        DayDurationInfoDTO dayDurationInfoDTO = dayDurationInfoMap.getOrDefault(finalTempDayId, new DayDurationInfoDTO());
        BigDecimal leaveHours = BigDecimal.ZERO;
        if (Objects.nonNull(dayDurationInfoDTO.getDays()) && dayDurationInfoDTO.getDays().compareTo(BigDecimal.ZERO) > 0) {
            leaveHours = leaveHours.add(dayDurationInfoDTO.getDays().multiply(dayDurationInfoDTO.getLegalWorkingHours()));
        }
        if (Objects.nonNull(dayDurationInfoDTO.getHours()) && dayDurationInfoDTO.getHours().compareTo(BigDecimal.ZERO) > 0) {
            leaveHours = leaveHours.add(dayDurationInfoDTO.getHours());
        }
        if (Objects.nonNull(dayDurationInfoDTO.getMinutes()) && dayDurationInfoDTO.getMinutes().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal minutesConvertHours = dayDurationInfoDTO.getMinutes().divide(new BigDecimal("60"), 2, RoundingMode.HALF_UP);
            leaveHours = leaveHours.add(minutesConvertHours);
        }
        return leaveHours;
    }


    /**
     * 多线程实时计算考勤
     *
     * @param targetDayAttendanceHandlerList 考勤计算数据
     * @param formDO                         申请单
     */
    private void calculateAttendance(List<AttendanceCalculateHandlerDTO> targetDayAttendanceHandlerList,
                                     AttendanceFormDO formDO) {
        // 多线程调用
        long startTime = System.currentTimeMillis();
        log.info("【请假、外勤】【申请、销假】审核通过，重新计算考勤" + "--->开始");
        AtomicInteger successCount = new AtomicInteger();
        List<AttendanceCalculateHandlerDTO> failAttendanceHandlerList = Lists.newArrayList();
        CountDownLatch countDownLatch = new CountDownLatch(targetDayAttendanceHandlerList.size());

        // 3. 调用考勤计算接口
        targetDayAttendanceHandlerList.forEach(targetDayAttendanceHandlerDTO -> {
            bizTaskThreadPool.execute(() -> {
                try {
                    // 计算考勤
                    attendanceGenerateService.attendanceCalculateHandler(targetDayAttendanceHandlerDTO);

                    // 请假通过仓内考勤逻辑处理
                    if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                        warehouseAttendanceHandlerService.warehouseAttendanceLeaveAuditPassHandler(formDO, targetDayAttendanceHandlerDTO);
                    }
                    successCount.getAndIncrement();
                } catch (Exception e) {
                    failAttendanceHandlerList.add(targetDayAttendanceHandlerDTO);
                    log.info("单据号:{}，【请假、外勤】【申请、销假】审核通过，计算考勤失败，targetDayAttendanceHandlerDTO:{},异常信息为:{}", formDO.getApplicationCode(), JSON.toJSONString(targetDayAttendanceHandlerDTO), e.getMessage());
                } finally {
                    countDownLatch.countDown();
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            XxlJobLogger.log("单据号:{}，{} CountDownLatch wait InterruptedException", formDO.getApplicationCode(), "【请假、外勤】【申请、销假】审核通过，计算考勤");
            Thread.currentThread().interrupt();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        XxlJobLogger.log("单据号:{}，【请假、外勤】【申请、销假】审核通过，计算考勤" + "--->结束，耗时 {} 毫秒，其中成功{}个,失败{}个,失败的信息为:{}", formDO.getApplicationCode(), duration, successCount.get(), failAttendanceHandlerList.size(), failAttendanceHandlerList);
    }

}
