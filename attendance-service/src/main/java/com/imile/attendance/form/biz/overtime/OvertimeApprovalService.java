package com.imile.attendance.form.biz.overtime;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ApprovalNoPrefixEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.approval.OverTimeCustomFieldEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.vacation.LeaveCodeAssociateEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.AttendanceApprovalFormManage;
import com.imile.attendance.form.AttendanceApprovalManage;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.overtime.param.OverTimeAddParam;
import com.imile.attendance.form.biz.overtime.param.OverTimeListParam;
import com.imile.attendance.form.biz.overtime.param.UserOverTimeAddParam;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormDetailVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormExportVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormListVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFromVO;
import com.imile.attendance.form.bo.AttendanceApprovalFormDetailBO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.dto.UserAuthDTO;
import com.imile.attendance.form.mapstruct.AttendanceFormMapstruct;
import com.imile.attendance.form.param.UserAuthParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.idwork.IdWorkUtils;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormDao;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceApprovalFormUserInfoDao;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.ApprovalFormUserInfoQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.bpm.enums.ApprovalClientTypeEnum;
import com.imile.bpm.enums.ApprovalDataSourceEnum;
import com.imile.bpm.enums.ApprovalOrgEnum;
import com.imile.bpm.enums.LanguageTypeEnum;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiDTO;
import com.imile.bpm.mq.dto.ApprovalEmptyRecordApiQuery;
import com.imile.bpm.mq.dto.ApprovalInfoCreateResultDTO;
import com.imile.bpm.mq.dto.ApprovalInitInfoApiDTO;
import com.imile.bpm.mq.dto.ApprovalPushStatusMsgDTO;
import com.imile.bpm.mq.dto.ApprovalTypeFieldApiDTO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.idwork.IdWorkerUtil;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/15
 * @Description 加班审批服务
 */
@Slf4j
@Service
public class OvertimeApprovalService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private RpcBpmApprovalClient bpmApprovalClient;
    @Resource
    private AttendanceApprovalManage attendanceApprovalManage;
    @Resource
    private AttendanceApprovalFormManage formManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private AttendanceApprovalFormDao approvalFormDao;
    @Resource
    private AttendanceApprovalFormUserInfoDao approvalFormUserInfoDao;
    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private EmployeePunchRecordService employeePunchRecordService;
    @Resource
    private IdWorkUtils idWorkUtils;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private MigrationService migrationService;

    /**
     * 加班列表
     *
     * @param param
     * @return
     */
    public PaginationResult<OverTimeApprovalFormListVO> list(OverTimeListParam param) {
        log.info("list | param :{}", JSON.toJSONString(param));
        PageInfo<OverTimeApprovalFormListVO> pageInfo = this.listPage(param);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        return PageUtil.getPageResult(pageInfo.getList(), param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    private PageInfo<OverTimeApprovalFormListVO> listPage(OverTimeListParam param) {
        // 设置权限
        OverTimeListQuery query = buildQuery(param);
        // 构建查询参数
        ApprovalFormQuery approvalFormQuery = buildApprovalFormQuery(query);
        if (Objects.isNull(approvalFormQuery)) {
            return null;
        }

        Page<AttendanceApprovalFormDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<AttendanceApprovalFormDO> pageInfo = page.doSelectPageInfo(() -> approvalFormDao.selectByCondition(approvalFormQuery));
        List<AttendanceApprovalFormDO> approvalFormList = pageInfo.getList();
        if (CollectionUtils.isEmpty(approvalFormList)) {
            return null;
        }

        // 封装VO
        List<OverTimeApprovalFormListVO> targetList = this.buildFormListVO(approvalFormList);
        PageInfo<OverTimeApprovalFormListVO> pageInfoResult = BeanUtils.convert(pageInfo, PageInfo.class);
        pageInfoResult.setList(targetList);
        return pageInfoResult;
    }

    /**
     * 加班列表(clover)
     *
     * @param param
     * @return
     */
    public PaginationResult<OverTimeApprovalListDTO> cloverList(OverTimeListParam param) {
        OverTimeListQuery query = buildQuery(param);

        Page<OverTimeApprovalListDTO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<OverTimeApprovalListDTO> pageInfo = page.doSelectPageInfo(() -> formManage.selectListByCondition(query));

        List<OverTimeApprovalListDTO> pageInfoList = pageInfo.getList();
        if (CollUtil.isEmpty(pageInfoList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> deptIdList = pageInfoList
                .stream()
                .filter(item -> Objects.nonNull(item.getDeptId()))
                .map(item -> item.getDeptId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendanceDept> deptDOList = deptService.selectDeptByIds(deptIdList);
        Map<Long, List<AttendanceDept>> deptIdMap = deptDOList
                .stream()
                .collect(Collectors.groupingBy(AttendanceDept::getId));

        List<Long> postIdList = pageInfo.getList()
                .stream()
                .filter(item -> Objects.nonNull(item.getPostId()))
                .map(item -> item.getPostId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendancePost> entPostDOList = postService.listByPostList(postIdList);
        Map<Long, List<AttendancePost>> postIdMap = entPostDOList.stream().collect(Collectors.groupingBy(AttendancePost::getId));

        for (OverTimeApprovalListDTO listDTO : pageInfoList) {

            List<AttendanceDept> userDeptList = deptIdMap.get(listDTO.getDeptId());
            if (CollectionUtils.isNotEmpty(userDeptList)) {
                listDTO.setDeptName(RequestInfoHolder.isChinese()
                        ? userDeptList.get(0).getDeptNameCn()
                        : userDeptList.get(0).getDeptNameEn());
            }

            List<AttendancePost> userPostList = postIdMap.get(listDTO.getPostId());
            if (CollectionUtils.isNotEmpty(userPostList)) {
                listDTO.setPostName(RequestInfoHolder.isChinese()
                        ? userPostList.get(0).getPostNameCn()
                        : userPostList.get(0).getPostNameEn());
            }

            if (StringUtils.isNotBlank(listDTO.getFormStatus())) {
                FormStatusEnum statusEnum = FormStatusEnum.getInstance(listDTO.getFormStatus());
                listDTO.setFormStatusDesc(Objects.isNull(statusEnum) ? "" : statusEnum.getDesc());
            }

            listDTO.setApplicationFormId(listDTO.getId());
            listDTO.setEstimateDuration(Objects.isNull(listDTO.getEstimateDuration()) ? null : listDTO.getEstimateDuration().divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
        }

        return PageUtil.getPageResult(pageInfoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 构建查询条件
     *
     * @param param 入参
     * @return OverTimeListQuery
     */
    private OverTimeListQuery buildQuery(OverTimeListParam param) {
        OverTimeListQuery query = BeanUtils.convert(param, OverTimeListQuery.class);
        if (ObjectUtil.equal(param.getSystemSource(), "ATTENDANCE")) {
            // 获取部门权限
            UserAuthParam userAuthParam = UserAuthParam.builder().userId(RequestInfoHolder.getUserId()).build();
            UserAuthDTO userAuthDTO = commonFormOperationService.userDeptAuthList(userAuthParam);
            if (!this.checkDeptAuth(query, userAuthDTO)) {
                return null;
            }
        }
        if (ObjectUtil.equal(param.getSystemSource(), "CLOVER")) {
            query.setUserCode(RequestInfoHolder.getUserCode());
        }

        return query;
    }

    /**
     * 构建查询条件
     *
     * @param query 入参
     * @return ApprovalFormQuery
     */
    private ApprovalFormQuery buildApprovalFormQuery(OverTimeListQuery query) {
        if (Objects.isNull(query)) {
            return null;
        }
        ApprovalFormQuery approvalFormQuery = AttendanceFormMapstruct.INSTANCE.mapQueryToApprovalFormQuery(query);

        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfo = formManage.selectByCondition(query);
        if (CollectionUtils.isEmpty(approvalFormUserInfo)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(approvalFormUserInfo)) {
            List<Long> formIdList = approvalFormUserInfo
                    .stream()
                    .map(AttendanceApprovalFormUserInfoDO::getFormId)
                    .distinct()
                    .collect(Collectors.toList());
            approvalFormQuery.setFormIdList(formIdList);
        }
        return approvalFormQuery;
    }

    /**
     * 加班申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO overTimeAdd(OverTimeAddParam param) {
        log.info("overTimeAdd | overTimeAddParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1.构建基本信息
        UserOverTimeAddParam userOverTimeAddParam = this.userBaseInfoBuild(param);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(userOverTimeAddParam.getUserId())) {
            log.info("overTimeAdd | userInfo is on old attendance, userCode:{}" + userOverTimeAddParam.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        AttendanceApprovalFormDO formDO = new AttendanceApprovalFormDO();
        List<AttendanceApprovalFormUserInfoDO> formUserInfoList = Lists.newArrayList();

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 1)) {
            // 2.保存校验
            overTimeAddDataCheck(userOverTimeAddParam);
            formDO.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 3.构建单据对象
        this.overTimeDataAddBuild(userOverTimeAddParam, formDO, formUserInfoList);

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 0)) {
            // 暂存功能，预留，后续如果需要可添加逻辑
            attendanceApprovalManage.approvalFormAdd(formDO, formUserInfoList);
            return resultVO;
        }
        // 4.构建审批信息
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        this.overTimeAddApprovalDataBuild(initInfoApiDTO, formDO, formUserInfoList, userOverTimeAddParam);
        // 5.创建审批流
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        formDO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 6.落库
        attendanceApprovalManage.approvalFormAdd(formDO, formUserInfoList);
        // 设置返回值
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 加班申请
     *
     * @param param
     * @return
     */
    public ApprovalResultVO overTimeUpdate(OverTimeAddParam param) {
        log.info("overTimeUpdate | overTimeUpdateParam :{}", JSON.toJSONString(param));
        ApprovalResultVO resultVO = new ApprovalResultVO();
        if (param.getOperationType() == 2) {
            return resultVO;
        }
        // 1.构建用户基本信息
        UserOverTimeAddParam userOverTimeAddParam = this.userBaseInfoBuild(param);
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(userOverTimeAddParam.getUserId())) {
            log.info("overTimeUpdate | userInfo is on old attendance, userCode:{}" + userOverTimeAddParam.getUserCode());
            throw BusinessException.get(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getCode()
                    , I18nUtils.getMessage(ErrorCodeEnum.THE_GRAY_SCALE_PERSONNEL_NEED_TO_OPERATE_IN_THE_NEW_SYSTEM.getDesc()));
        }
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(param.getFormId());
        AttendanceApprovalFormDO approvalForm = approvalFormDetail.getApprovalForm();
        if (ObjectUtil.isNull(approvalForm)) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }

        List<AttendanceApprovalFormUserInfoDO> formUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        // 2.保存
        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 1)) {
            // 保存功能
            this.overTimeAddDataCheck(userOverTimeAddParam);
            approvalForm.setFormStatus(FormStatusEnum.IN_REVIEW.getCode());
        }
        // 构建对象
        this.overTimeDataUpdateBuild(userOverTimeAddParam, approvalForm, formUserInfoList);

        if (ObjectUtil.equal(userOverTimeAddParam.getOperationType(), 0)) {
            // 暂存功能，预留，后续如果需要可添加逻辑
            attendanceApprovalManage.approvalFormAddAndUpdate(approvalForm, formUserInfoList);
            return resultVO;
        }
        // 构建审批信息
        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        List<AttendanceApprovalFormUserInfoDO> formUserInfoList_effect = formUserInfoList
                .stream()
                .filter(item -> IsDeleteEnum.NO.getCode() == item.getIsDelete())
                .collect(Collectors.toList());
        this.overTimeAddApprovalDataBuild(initInfoApiDTO, approvalForm, formUserInfoList_effect, userOverTimeAddParam);
        //本次保存是否是驳回后重提
        if (approvalForm.getApprovalId() != null
                && StringUtils.isNotBlank(approvalForm.getApprovalProcessInfo())) {
            ApprovalPushStatusMsgDTO approvalPushStatusMsgDTO = JSON.parseObject(approvalForm.getApprovalProcessInfo(), ApprovalPushStatusMsgDTO.class);
            if (approvalPushStatusMsgDTO != null
                    && approvalPushStatusMsgDTO.getStatus().equals(-2)) {
                initInfoApiDTO.setResubmitApprovalId(approvalForm.getApprovalId());
            }
        }
        // 创建审批流
        ApprovalInfoCreateResultDTO approvalInfoCreateResultDTO = bpmApprovalClient.addApprovalInfo(initInfoApiDTO);
        approvalForm.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        // 落库
        attendanceApprovalManage.approvalFormAddAndUpdate(approvalForm, formUserInfoList);
        // 设置返回值
        resultVO.setApprovalCode(approvalInfoCreateResultDTO.getApprovalCode());
        resultVO.setApprovalId(approvalInfoCreateResultDTO.getApprovalId());
        return resultVO;
    }

    /**
     * 加班详情
     *
     * @param approvalFormId
     * @return
     */
    public OverTimeApprovalFromVO getApprovalFromDetail(Long approvalFormId) {
        log.info("getApprovalFromDetail | approvalFormId :{}", approvalFormId);
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(approvalFormId);
        AttendanceApprovalFormDO approvalForm = approvalFormDetail.getApprovalForm();
        if (ObjectUtil.isNull(approvalForm)) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        if (CollectionUtils.isEmpty(approvalFormUserInfoList)) {
            approvalFormUserInfoList = Lists.newArrayList();
        }
        for (AttendanceApprovalFormUserInfoDO formUserInfoDO : approvalFormUserInfoList) {
            if (Objects.isNull(formUserInfoDO.getEstimateDuration())) continue;
            formUserInfoDO.setEstimateDuration(formUserInfoDO.getEstimateDuration().divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
        }
        // 获取所有的用户code
        List<String> userCodeList = approvalFormUserInfoList
                .stream()
                .map(AttendanceApprovalFormUserInfoDO::getUserCode)
                .collect(Collectors.toList());
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(userCodeList);
        Map<String, AttendanceUser> userCodeMap = userInfoList
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));
        // 获取部门信息
        List<Long> deptIdList = userInfoList
                .stream()
                .map(AttendanceUser::getDeptId)
                .collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
        Map<Long, AttendanceDept> deptMap = deptList.stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
        // 获取岗位信息
        List<Long> postIdList = userInfoList
                .stream()
                .map(AttendanceUser::getPostId)
                .collect(Collectors.toList());
        List<AttendancePost> postList = postService.listByPostList(postIdList);
        Map<Long, AttendancePost> postMap = postList
                .stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        // 获取人员绑定的加班规则
        List<Long> userIdList = userInfoList.stream().map(AttendanceUser::getId).collect(Collectors.toList());
        Map<Long, OverTimeConfigDO> configMapByUserIdList = overTimeConfigManage.getConfigMapByUserIdList(userIdList);

        // 封装数据
        OverTimeApprovalFromVO approvalFromVo = BeanUtils.convert(approvalForm, OverTimeApprovalFromVO.class);
        List<OverTimeApprovalFormDetailVO> detail = Lists.newArrayList();
        String remark = "";
        for (AttendanceApprovalFormUserInfoDO formUserInfo : approvalFormUserInfoList) {
            // 获取打卡记录
            EmployeePunchCardRecordQuery employeePunchCardRecordQuery = EmployeePunchCardRecordQuery
                    .builder()
                    .userCode(String.valueOf(formUserInfo.getDayId()))
                    .dayId(String.valueOf(formUserInfo.getDayId()))
                    .build();
            List<EmployeePunchRecordDO> employeePunchRecordList = employeePunchRecordService.listRecords(employeePunchCardRecordQuery);

            AttendanceUser userInfo = userCodeMap.get(formUserInfo.getUserCode());
            OverTimeApprovalFormDetailVO fromDetailVo = BeanUtils.convert(formUserInfo, OverTimeApprovalFormDetailVO.class);
            remark = formUserInfo.getRemark();
            if (ObjectUtil.isNotNull(userInfo)) {
                boolean chinese = RequestInfoHolder.isChinese();
                fromDetailVo.setUserId(userInfo.getId());
                fromDetailVo.setUserName(chinese ? userInfo.getUserName() : userInfo.getUserNameEn());
                fromDetailVo.setDeptId(userInfo.getDeptId());
                AttendanceDept entDept = deptMap.get(userInfo.getDeptId());
                if (ObjectUtil.isNotNull(entDept)) {
                    fromDetailVo.setDeptName(chinese ? entDept.getDeptNameCn() : entDept.getDeptNameEn());
                }
                fromDetailVo.setPostId(userInfo.getPostId());
                AttendancePost entPost = postMap.get(userInfo.getPostId());
                if (ObjectUtil.isNotNull(entPost)) {
                    fromDetailVo.setPostName(chinese ? entPost.getPostNameCn() : entPost.getPostNameEn());
                }
                // 设置加班规则
                OverTimeConfigDO overTimeConfig = configMapByUserIdList.get(userInfo.getId());
                // 加班规则
                if (ObjectUtil.isNotNull(overTimeConfig)) {
                    fromDetailVo.setWorkingOutStartTime(overTimeConfig.getWorkingOutStartTime());

                    fromDetailVo.setWorkingEffectiveTime(overTimeConfig.getWorkingEffectiveTime());
                    fromDetailVo.setWorkingSubsidyType(overTimeConfig.getWorkingSubsidyType());

                    fromDetailVo.setRestEffectiveTime(overTimeConfig.getRestEffectiveTime());
                    fromDetailVo.setRestSubsidyType(overTimeConfig.getRestSubsidyType());

                    fromDetailVo.setHolidayEffectiveTime(overTimeConfig.getHolidayEffectiveTime());
                    fromDetailVo.setHolidaySubsidyType(overTimeConfig.getHolidaySubsidyType());
                }
                // 打卡记录
                if (CollectionUtils.isNotEmpty(employeePunchRecordList)) {
                    if (employeePunchRecordList.size() > 1) {
                        // 获取最早最晚打卡记录
                        // 将employeePunchRecordList按照打卡时间正序
                        employeePunchRecordList.sort((o1, o2) -> o1.getPunchTime().compareTo(o2.getPunchTime()));
                        EmployeePunchRecordDO earliestEmployeePunchRecord = employeePunchRecordList.get(0);
                        EmployeePunchRecordDO latestEmployeePunchRecord = employeePunchRecordList.get(employeePunchRecordList.size() - 1);
                        if (ObjectUtil.isNotNull(earliestEmployeePunchRecord)) {
                            fromDetailVo.setEarliestPunchTime(DateUtil.format(earliestEmployeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                        if (ObjectUtil.isNotNull(latestEmployeePunchRecord)) {
                            fromDetailVo.setLatestPunchTime(DateUtil.format(latestEmployeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                    } else {
                        EmployeePunchRecordDO employeePunchRecord = employeePunchRecordList.get(0);
                        if (ObjectUtil.isNotNull(employeePunchRecord)) {
                            fromDetailVo.setEarliestPunchTime(DateUtil.format(employeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                            fromDetailVo.setLatestPunchTime(DateUtil.format(employeePunchRecord.getPunchTime(), DatePattern.NORM_DATETIME_PATTERN));
                        }
                    }
                }
            }
            detail.add(fromDetailVo);
        }
        approvalFromVo.setRemark(remark);
        approvalFromVo.setDetail(detail);

        return approvalFromVo;
    }

    /**
     * 加班预览
     *
     * @param param
     * @return
     */
    public List<ApprovalDetailStepRecordDTO> overtimePreview(OverTimeAddParam param) {
        List<ApprovalDetailStepRecordDTO> resultDTOList = new ArrayList<>();
        if (param.getOperationType() != 2) {
            return resultDTOList;
        }
        //校验必填数据
        UserOverTimeAddParam userOverTimeAddParam = userBaseInfoBuild(param);
        overTimeAddDataCheck(userOverTimeAddParam);
        AttendanceApprovalFormDO formDO = new AttendanceApprovalFormDO();
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = Lists.newArrayList();
        //暂存不校验任何信息，直接落库成功，提交时校验
        overTimeDataAddBuild(userOverTimeAddParam, formDO, approvalFormUserInfoList);

        ApprovalInitInfoApiDTO initInfoApiDTO = new ApprovalInitInfoApiDTO();
        overTimeAddApprovalDataBuild(initInfoApiDTO, formDO, approvalFormUserInfoList, userOverTimeAddParam);

        ApprovalEmptyRecordApiQuery query = BeanUtils.convert(initInfoApiDTO, ApprovalEmptyRecordApiQuery.class);
        List<ApprovalEmptyRecordApiDTO> recordApiDTOList = bpmApprovalClient.getEmptyApprovalRecords(query);
        if (CollectionUtils.isEmpty(recordApiDTOList)) {
            return resultDTOList;
        }
        commonFormOperationService.previewDTOBuildContainsErrors(recordApiDTOList, resultDTOList, param.getUserCode());
        return resultDTOList;
    }

    /**
     * 加班取消
     *
     * @param formId
     */
    public void cancel(Long formId) {
        //查询当前单据是否存在
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(formId);
        AttendanceApprovalFormDO formDO = approvalFormDetail.getApprovalForm();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(), I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //非这些状态的单据不处理
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.IN_REVIEW.getCode(), formDO.getFormStatus())
                && !StringUtils.equalsIgnoreCase(FormStatusEnum.REJECT.getCode(), formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.FORM_STATUS_NOT_CANCEL.getDesc()));
        }
        bpmApprovalClient.backApply(formDO.getApprovalId());
        formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
        BaseDOUtil.fillDOUpdate(formDO);

        attendanceApprovalManage.approvalFormUpdate(formDO, null);
    }

    /**
     * 加班删除
     *
     * @param formId
     */
    public void delete(Long formId) {
        //查询当前单据是否存在
        AttendanceApprovalFormDetailBO approvalFormDetail = formManage.getApprovalFormDetailById(formId);
        AttendanceApprovalFormDO formDO = approvalFormDetail.getApprovalForm();
        if (formDO == null) {
            throw BusinessException.get(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.APPLICATION_FORM_IS_EMPTY.getDesc()));
        }
        //只有暂存状态的单据可以被删除
        if (!StringUtils.equalsIgnoreCase(FormStatusEnum.STAGING.getCode(),
                formDO.getFormStatus())) {
            throw BusinessException.get(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ONLY_STAGE_STATUS_CAN_BE_DELETE.getDesc()));
        }
        formDO.setIsDelete(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(formDO);
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormDetail.getApprovalFormUserInfoList();
        approvalFormUserInfoList.forEach(item -> {
            item.setIsDelete(BusinessConstant.Y);
            BaseDOUtil.fillDOUpdate(item);
        });
        attendanceApprovalManage.approvalFormUpdate(formDO, approvalFormUserInfoList);
    }

    /**
     * 加班单据信息导出
     *
     * @param param
     * @return
     */
    public PaginationResult<OverTimeApprovalFormExportVO> listExport(OverTimeListParam param) {
        // 获取加班单列表(主表)
        PageInfo<OverTimeApprovalFormListVO> pageInfo = this.listPage(param);
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), param);
        }
        List<OverTimeApprovalFormListVO> formList = pageInfo.getList();
        List<Long> formIdList = formList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<Long> deptIdList = formList.stream().map(item -> item.getApplyDeptId()).collect(Collectors.toList());
        List<Long> postIdList = formList.stream().map(item -> item.getApplyPostId()).collect(Collectors.toList());
        List<OverTimeApprovalFormExportVO> exportVOList = AttendanceFormMapstruct.INSTANCE.toExportVO(formList);
        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = formManage.selectDetailByFormIdList(formIdList);
        Map<Long, List<AttendanceApprovalFormUserInfoDO>> formDetailMap = approvalFormUserInfoList.stream()
                .collect(Collectors.groupingBy(AttendanceApprovalFormUserInfoDO::getFormId));
        // 获取申请人部门、岗位信息
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
        List<AttendancePost> postList = postService.listByPostList(postIdList);
        for (OverTimeApprovalFormExportVO exportVO : exportVOList) {
            FormTypeEnum formTypeEnum = FormTypeEnum.getInstance(exportVO.getFormType());
            if (Objects.nonNull(formTypeEnum)) {
                exportVO.setFormType(RequestInfoHolder.isChinese()
                        ? formTypeEnum.getDesc() : formTypeEnum.getDescEn());
            }
            FormStatusEnum formStatusEnum = FormStatusEnum.getInstance(exportVO.getFormStatus());
            if (Objects.nonNull(formStatusEnum)) {
                exportVO.setFormStatus(RequestInfoHolder.isChinese()
                        ? formStatusEnum.getDesc() : formStatusEnum.getDescEn());
            }
            List<AttendanceApprovalFormUserInfoDO> formDetail = formDetailMap.get(exportVO.getId());
            if (CollectionUtils.isEmpty(formDetail)) {
                continue;
            }
            AttendanceApprovalFormUserInfoDO formUserInfo = formDetail.get(0);
            exportVO.setStartDate(DateHelper.formatYYYYMMDDHHMMSS(formUserInfo.getStartDate()));
            exportVO.setEndDate(DateHelper.formatYYYYMMDDHHMMSS(formUserInfo.getEndDate()));
            exportVO.setCountry(formUserInfo.getCountry());
            BigDecimal overTimeMinutes = formUserInfo.getEstimateDuration();
            if (Objects.nonNull(overTimeMinutes)) {
                exportVO.setOverTimeHours(String.valueOf(overTimeMinutes.divide(BusinessConstant.MINUTES,2, RoundingMode.HALF_UP)));
            }
            List<AttendanceDept> existDeptDOList = deptList
                    .stream()
                    .filter(item -> item.getId().equals(formUserInfo.getDeptId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptDOList)) {
                exportVO.setDeptName(existDeptDOList.get(0).getDeptNameEn());
            }
            List<AttendancePost> existPostDOList = postList
                    .stream()
                    .filter(item -> item.getId().equals(formUserInfo.getPostId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existPostDOList)) {
                exportVO.setPostName(existPostDOList.get(0).getPostNameEn());
            }
        }
        return PageUtil.getPageResult(exportVOList, param, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 保存功能校验
     *
     * @param param 入参
     */
    private void overTimeAddDataCheck(UserOverTimeAddParam param) {
        // 1. 校验该用户加班当天是不是工作日，如果是工作日，则不允许提加班申请
        log.info("userId：{}", param.getUserId());
        List<UserShiftConfigDO> userClassConfigDOList = userShiftConfigManage.selectBatchUserRecord(Arrays.asList(param.getUserId()),
                Collections.singletonList(param.getDayId()));
        if (CollectionUtils.isNotEmpty(userClassConfigDOList)) {
            // 这里查询的是一天的数据，每个人一天只会存在一个排班
            UserShiftConfigDO userClassConfigDO = userClassConfigDOList.get(0);
            log.info("day_id：{},班次为：{}", param.getDayId(), userClassConfigDO.getShiftType());
            if (ObjectUtil.isNotNull(userClassConfigDO) && ObjectUtil.isNotNull(userClassConfigDO.getPunchClassConfigId())) {
                // 说明排班了，不能提加班申请
                throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_ON_WEEKDAYS_ERROR);
            }
        }
        // 2. 校验当天是否提交过加班申请
        ApprovalFormUserInfoQuery approvalFormUserInfoQuery = new ApprovalFormUserInfoQuery();
        approvalFormUserInfoQuery.setUserCode(param.getUserCode());
        approvalFormUserInfoQuery.setDayId(param.getDayId());

        List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList = approvalFormUserInfoDao.selectByCondition(approvalFormUserInfoQuery);
        if (CollectionUtils.isNotEmpty(approvalFormUserInfoList)) {
            List<Long> formIdList = approvalFormUserInfoList.stream().map(AttendanceApprovalFormUserInfoDO::getFormId).distinct().collect(Collectors.toList());
            List<AttendanceApprovalFormDO> FormDOList = formManage.selectExistForm(formIdList);
            if (CollectionUtils.isNotEmpty(FormDOList)) {
                // 说明该天已经提交过加班申请了
                throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_REPEAT_ERROR);
            }
        }
    }

    /**
     * 校验选中部门和用户部门权限
     *
     * @param query
     * @param userAuthDTO
     * @return
     */
    private Boolean checkDeptAuth(OverTimeListQuery query,
                                  UserAuthDTO userAuthDTO) {
        if (Objects.isNull(userAuthDTO)) {
            return Boolean.FALSE;
        }
        List<String> countryAuthList = userAuthDTO.getCountryList();
        List<Long> deptAuthList = userAuthDTO.getDeptIds();
        // 无常驻国权限也无部门权限
        if (CollectionUtils.isEmpty(countryAuthList)
                && CollectionUtils.isEmpty(deptAuthList)) {
            return Boolean.FALSE;
        }
        query.setAuthLocationCountryList(countryAuthList);
        query.setAuthDeptIdList(deptAuthList);
        return Boolean.TRUE;
    }

    // 构建实体

    /**
     * 构建用户基本信息
     *
     * @param param 入参
     */
    private UserOverTimeAddParam userBaseInfoBuild(OverTimeAddParam param) {
        UserOverTimeAddParam userOverTimeAddParam = BeanUtils.convert(param, UserOverTimeAddParam.class);

        if (ObjectUtil.isNull(param)) {
            throw BusinessException.get(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PARAM_NOT_NULL.getDesc()));
        }
        if (ObjectUtil.equal(param.getUserCode(), "") || ObjectUtil.equal(param.getApplyUserCode(), "")) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(Arrays.asList(param.getUserCode(),
                param.getApplyUserCode()));
        // 被申请人
        List<AttendanceUser> userCodeInfo = userInfoList.stream()
                .filter(e -> ObjectUtil.equal(e.getUserCode()
                        , param.getUserCode()))
                .collect(Collectors.toList());
        // 申请人
        List<AttendanceUser> applyUserCodeInfo = userInfoList.stream()
                .filter(e -> ObjectUtil.equal(e.getUserCode()
                        , param.getApplyUserCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoList) || CollectionUtils.isEmpty(userCodeInfo) || CollectionUtils.isEmpty(applyUserCodeInfo)) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        // 校验加班开始时间、结束时间的年月日是否相同，如果不相同，返回fail
        if (!DateUtil.isSameDay(param.getOverTimeStartDate(), param.getOverTimeEndDate())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.OVER_TIME_NOT_ON_THE_SAME_DAY);
        }
        // 如果是同一天，则设置day_id
        String dayIdString = DateUtil.format(param.getOverTimeStartDate(), DatePattern.PURE_DATE_PATTERN);
        // 设置dayId 不由excel传入
        param.setDayId(Long.parseLong(dayIdString));

        // 被申请人
        AttendanceUser userInfo = userCodeInfo.get(0);
        // 申请人
        AttendanceUser applyUserInfo = applyUserCodeInfo.get(0);

        userOverTimeAddParam.setUserId(userInfo.getId());
        userOverTimeAddParam.setUserCode(userInfo.getUserCode());
        userOverTimeAddParam.setDayId(param.getDayId());
        userOverTimeAddParam.setUserName(userInfo.getUserName());
        userOverTimeAddParam.setDeptId(userInfo.getDeptId());
        userOverTimeAddParam.setPostId(userInfo.getPostId());
        userOverTimeAddParam.setUserCountry(userInfo.getLocationCountry());
        userOverTimeAddParam.setUserOriginCountry(userInfo.getOriginCountry());
        userOverTimeAddParam.setApplyUserCountry(applyUserInfo.getLocationCountry());

        return userOverTimeAddParam;
    }

    /**
     * 构建对象
     *
     * @param userOverTimeAddParam 入参
     * @param formDO               对象
     * @param formUserInfoList     列表
     */
    private void overTimeDataAddBuild(UserOverTimeAddParam userOverTimeAddParam,
                                      AttendanceApprovalFormDO formDO,
                                      List<AttendanceApprovalFormUserInfoDO> formUserInfoList) {
        formDO.setId(IdWorkerUtil.getId());
        formDO.setApplyUserCode(userOverTimeAddParam.getUserCode());
        formDO.setApplicationCode(idWorkUtils.nextNo(ApprovalNoPrefixEnum.OVER_TIME));
        formDO.setFormType(FormTypeEnum.OVER_TIME.getCode());
        formDO.setDataSource(0);
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOInsert(formDO);

        // 构建详情表
        formUserInfoList.add(this.buildApprovalFormUserInfo(formDO, userOverTimeAddParam));
    }

    /**
     * 构建更新对象
     *
     * @param userOverTimeAddParam     入参
     * @param formDO                   对象
     * @param approvalFormUserInfoList 列表
     */
    private void overTimeDataUpdateBuild(UserOverTimeAddParam userOverTimeAddParam,
                                         AttendanceApprovalFormDO formDO,
                                         List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList) {
        formDO.setFormType(FormTypeEnum.OVER_TIME.getCode());
        formDO.setDataSource(0);
        if (StringUtils.isBlank(formDO.getFormStatus())) {
            //为暂存
            formDO.setFormStatus(FormStatusEnum.STAGING.getCode());
        }
        BaseDOUtil.fillDOUpdate(formDO);
        //删除旧用户详情表数据
        for (AttendanceApprovalFormUserInfoDO oldUserInfoDO : approvalFormUserInfoList) {
            oldUserInfoDO.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdate(oldUserInfoDO);
        }
        // 构建详情表
        approvalFormUserInfoList.add(buildApprovalFormUserInfo(formDO, userOverTimeAddParam));
    }

    /**
     * 构建申请单详情信息
     *
     * @param formDO               申请单表单
     * @param userOverTimeAddParam 入参
     * @return approvalFormUserInfoDO
     */
    private AttendanceApprovalFormUserInfoDO buildApprovalFormUserInfo(AttendanceApprovalFormDO formDO,
                                                                       UserOverTimeAddParam userOverTimeAddParam) {
        AttendanceApprovalFormUserInfoDO approvalFormUserInfo = new AttendanceApprovalFormUserInfoDO();
        approvalFormUserInfo.setId(IdWorkerUtil.getId());
        approvalFormUserInfo.setFormId(formDO.getId());
        approvalFormUserInfo.setDayId(userOverTimeAddParam.getDayId());
        approvalFormUserInfo.setUserCode(userOverTimeAddParam.getUserCode());
        approvalFormUserInfo.setUserName(userOverTimeAddParam.getUserName());
        approvalFormUserInfo.setDeptId(userOverTimeAddParam.getDeptId());
        approvalFormUserInfo.setPostId(userOverTimeAddParam.getPostId());
        approvalFormUserInfo.setCountry(userOverTimeAddParam.getUserCountry());
        approvalFormUserInfo.setOriginCountry(userOverTimeAddParam.getUserOriginCountry());
        approvalFormUserInfo.setStartDate(userOverTimeAddParam.getOverTimeStartDate());
        approvalFormUserInfo.setEndDate(userOverTimeAddParam.getOverTimeEndDate());
        approvalFormUserInfo.setEstimateDuration(userOverTimeAddParam.getOverTimeDuration());
        approvalFormUserInfo.setRemark(userOverTimeAddParam.getRemark());
        BaseDOUtil.fillDOInsert(approvalFormUserInfo);
        return approvalFormUserInfo;
    }

    /**
     * 构建审批信息
     *
     * @param initInfoApiDTO   审批信息
     * @param formDO           审批单据
     * @param formUserInfoList 审批单据详情
     */
    private void overTimeAddApprovalDataBuild(ApprovalInitInfoApiDTO initInfoApiDTO,
                                              AttendanceApprovalFormDO formDO,
                                              List<AttendanceApprovalFormUserInfoDO> formUserInfoList,
                                              UserOverTimeAddParam userOverTimeAddParam) {

        initInfoApiDTO.setBizId(formDO.getId().toString());
        initInfoApiDTO.setApprovalType(formDO.getFormType());
        initInfoApiDTO.setClientType(ApprovalClientTypeEnum.PC.getCode());
        initInfoApiDTO.setOrgId(ApprovalOrgEnum.IMILE.getOrgId());
        initInfoApiDTO.setCountry(userOverTimeAddParam.getApplyUserCountry());
        initInfoApiDTO.setDataSource(ApprovalDataSourceEnum.HRMS.getCode());
        initInfoApiDTO.setApplyUserCode(userOverTimeAddParam.getUserCode());
        initInfoApiDTO.setApplyDate(DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
        initInfoApiDTO.setAppointApprovalCode(formDO.getApplicationCode());

        List<ApprovalTypeFieldApiDTO> fieldApiDTOList = new ArrayList<>();
        // 构建BPM审批流信息
        this.buildBpmInfoData(formUserInfoList, fieldApiDTOList);

        initInfoApiDTO.setFieldApiDTOList(fieldApiDTOList);
        log.info("overTimeAddApprovalDataBuild||调用BPM入参值为:{}", JSON.toJSONString(initInfoApiDTO));
    }

    /**
     * 构建bpm信息
     *
     * @param formUserInfoList 审批单局详情
     * @param fieldApiDTOList  属性值映射
     */
    private void buildBpmInfoData(List<AttendanceApprovalFormUserInfoDO> formUserInfoList,
                                  List<ApprovalTypeFieldApiDTO> fieldApiDTOList) {
        if (CollectionUtils.isNotEmpty(formUserInfoList)) {
            List<String> approvalUserCodeList = formUserInfoList
                    .stream()
                    .map(AttendanceApprovalFormUserInfoDO::getUserCode)
                    .collect(Collectors.toList());
            // 查询用户
            List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(approvalUserCodeList);
            Map<String, AttendanceUser> userCodeMap = userInfoList
                    .stream()
                    .collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));
            // 查询部门
            List<Long> deptIdList = userInfoList
                    .stream()
                    .map(AttendanceUser::getDeptId)
                    .collect(Collectors.toList());
            List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIdList);
            Map<Long, AttendanceDept> deptInfoMap = deptList
                    .stream()
                    .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
            // 查询岗位
            List<Long> postIdList = userInfoList
                    .stream()
                    .map(AttendanceUser::getPostId)
                    .collect(Collectors.toList());
            List<AttendancePost> entPostList = postService.listByPostList(postIdList);
            Map<Long, AttendancePost> postInfoMap = entPostList.stream().collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

            // 被申请人存在多个：导入入口
            if (formUserInfoList.size() > 1) {
                List<String> userNameList = Lists.newArrayList();
                List<String> userCodeList = Lists.newArrayList();
                List<String> deptInfoList = Lists.newArrayList();
                List<Long> deptIdInfoList = Lists.newArrayList();
                List<String> postInfoList = Lists.newArrayList();
                List<Long> dayIdList = Lists.newArrayList();
                List<String> startDateList = Lists.newArrayList();
                List<String> endDateList = Lists.newArrayList();
                List<String> estimateDurationList = Lists.newArrayList();
                List<String> remarkList = Lists.newArrayList();
                //Map<String, String> deptMap = new HashMap<>();

                for (AttendanceApprovalFormUserInfoDO approvalFormUserInfo : formUserInfoList) {
                    AttendanceUser userInfo = userCodeMap.get(approvalFormUserInfo.getUserCode());
                    if (ObjectUtil.isNull(userInfo)) {
                        log.info("userCode ：{} not found", approvalFormUserInfo.getUserCode());
                        continue;
                    }
                    userNameList.add(userInfo.getUserName());
                    userCodeList.add(approvalFormUserInfo.getUserCode());

                    AttendanceDept deptInfo = deptInfoMap.get(userInfo.getDeptId());
                    deptInfoList.add("");
                    if (ObjectUtil.isNotNull(deptInfo)) {
                        deptInfoList.add(deptInfo.getDeptNameEn());
                        deptIdInfoList.add(deptInfo.getId());
                    }

                    AttendancePost postInfo = postInfoMap.get(userInfo.getPostId());
                    postInfoList.add("");
                    if (ObjectUtil.isNotNull(postInfo)) {
                        postInfoList.add(postInfo.getPostNameEn());
                    }

                    dayIdList.add(approvalFormUserInfo.getDayId());
                    startDateList.add(String.valueOf(approvalFormUserInfo.getStartDate()));
                    endDateList.add(String.valueOf(approvalFormUserInfo.getEndDate()));
                    estimateDurationList.add(String.valueOf(approvalFormUserInfo.getEstimateDuration()));
                    remarkList.add(approvalFormUserInfo.getRemark());
                }
                // 将deptInfoList去重
                deptInfoList = deptInfoList.stream().distinct().collect(Collectors.toList());
                // 将deptIdInfoList去重
                Long deptId = deptIdInfoList.get(0);

                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_NAME_LIST.getCode(), JSON.toJSONString(userNameList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_CODE_LIST.getCode(), JSON.toJSONString(userCodeList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_DEPT_LIST.getCode(), JSON.toJSONString(deptInfoList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_POST_LIST.getCode(), JSON.toJSONString(postInfoList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DAY_ID_LIST.getCode(), JSON.toJSONString(dayIdList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_START_DATE_LIST.getCode(), JSON.toJSONString(startDateList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_END_DATE_LIST.getCode(), JSON.toJSONString(endDateList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_ESTIMATE_DURATION_List.getCode(), JSON.toJSONString(estimateDurationList), null);
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.REMARK_LIST.getCode(), JSON.toJSONString(remarkList), null);

                // 打包人员的只需要部门负责人审核就好了，都是同一个部门负责人
                commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_ID.getCode(), deptId.toString(), null);
                return;
            }


            // 存在一个被申请人：手动添加入口
            AttendanceApprovalFormUserInfoDO approvalFormUserInfo = formUserInfoList.get(0);
            AttendanceUser userInfo = userCodeMap.get(approvalFormUserInfo.getUserCode());
            if (ObjectUtil.isNull(userInfo)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
            }

            //被申请人姓名
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_NAME.getCode(), userInfo.getUserName(), null);
            //被申请人编码
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_CODE.getCode(), userInfo.getUserCode(), null);
            //被申请人部门
            AttendanceDept AttendanceDept = deptInfoMap.get(userInfo.getDeptId());
            if (ObjectUtil.isNull(AttendanceDept)) {
                throw BusinessException.get(ErrorCodeEnum.DEPT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DEPT_NOT_EXITS.getDesc()));
            }
            Map<String, String> deptMap = new HashMap<>();
            deptMap.put(LanguageTypeEnum.zh_CN.getCode(), AttendanceDept.getDeptNameCn());
            deptMap.put(LanguageTypeEnum.en_US.getCode(), AttendanceDept.getDeptNameEn());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_NAME.getCode(), AttendanceDept.getDeptNameEn(), deptMap);

            //被申请人岗位
            AttendancePost AttendancePost = postInfoMap.get(userInfo.getPostId());
            if (ObjectUtil.isNull(AttendancePost)) {
                throw BusinessException.get(ErrorCodeEnum.POST_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.POST_NOT_EXITS.getDesc()));
            }
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.POST_NAME.getCode(), AttendancePost.getPostNameEn(), null);

            // 加班日
            String overTimeDay = String.valueOf(approvalFormUserInfo.getDayId());
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_DAY.getCode(), overTimeDay, null);

            // 加班开始时间
            String overTimeStartDate = DateUtil.format(approvalFormUserInfo.getStartDate(), DatePattern.NORM_DATETIME_PATTERN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_START_DATE.getCode(), overTimeStartDate, null);

            // 加班开始时间
            String overTimeEndDate = DateUtil.format(approvalFormUserInfo.getEndDate(), DatePattern.NORM_DATETIME_PATTERN);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_END_DATE.getCode(), overTimeEndDate, null);

            // 预计加班时长
            BigDecimal estimateDuration = approvalFormUserInfo.getEstimateDuration();
            BigDecimal estimateDurationHour = BigDecimal.ZERO;
            if (Objects.nonNull(estimateDuration)) {
                estimateDurationHour = estimateDuration.divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
            }
            String overTimeEstimateDuration = String.valueOf(estimateDurationHour);
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.OVER_TIME_ESTIMATE_DURATION.getCode(), overTimeEstimateDuration, null);

            //备注
            String remark = approvalFormUserInfo.getRemark();
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.REMARK.getCode(), remark, null);

            //被申请人部门ID
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.DEPT_ID.getCode(), AttendanceDept.getId() != null ? AttendanceDept.getId().toString() : null, null);

            //被申请人常驻国
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.USER_COUNTRY.getCode(), approvalFormUserInfo.getCountry() != null ? approvalFormUserInfo.getCountry() : null, null);

            //被申请人结算国
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.ORIGIN_COUNTRY.getCode(), approvalFormUserInfo.getOriginCountry() != null ? approvalFormUserInfo.getOriginCountry() : null, null);

            //被审批人ID
            commonFormOperationService.customFieldBuild(fieldApiDTOList, OverTimeCustomFieldEnum.BE_APPROVERED_USER_ID.getCode(), userInfo.getId() != null ? userInfo.getId().toString() : null, null);
        }
    }


    /**
     * 封装数据
     *
     * @param userInfo                   用户信息
     * @param applyUserInfo              申请人用户信息
     * @param userCode                   用户code
     * @param overtimeMinutes            加班时长
     * @param updateUserLeaveStageDetail 需要修改的用户假期余额数据列表
     * @param date                       当前时间
     * @param nowDayId                   当前时间的dayId
     * @param startDate                  加班开始时间
     * @param endDate                    加班结束时间
     * @param addUserLeaveRecord         需要新增的用户请假记录数据列表
     * @param remark                     备注
     */
    public void addOverTime(AttendanceUser userInfo,
                            AttendanceUser applyUserInfo,
                            String userCode,
                            BigDecimal overtimeMinutes,
                            List<UserLeaveStageDetailDO> updateUserLeaveStageDetail,
                            DateTime date, Long nowDayId,
                            Date startDate, Date endDate,
                            List<UserLeaveRecordDO> addUserLeaveRecord,
                            String remark) {
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(userInfo.getId())) {
            log.info("userEntryAddLeaveInfo | userInfo is on old attendance, userCode:{}" + userInfo.getUserCode());
            return;
        }
        // 查询用户调休假期余额
        UserLeaveDetailQuery query = new UserLeaveDetailQuery();
        // param参数在前面已经校验了
        query.setUserId(userInfo.getId());
        // 什么假期类型是调休假期，需要有一个定义。
        query.setLeaveType(LeaveCodeAssociateEnum.COMPENSATORY_LEAVE.getFullCode());
        // userId+leaveType确定一条该假期类型的数据
        List<UserLeaveDetailDO> userLeaveDetail = userLeaveDetailService.selectUserLeaveDetail(query);
        if (CollUtil.isEmpty(userLeaveDetail)) {
            log.info("addOverTime | 考勤(加班)返回结果处理:userCode:{},没有调休假期", userCode);
            return;
        }
        List<Long> leaveIdList = userLeaveDetail
                .stream()
                .map(UserLeaveDetailDO::getId)
                .collect(Collectors.toList());
        log.info("addOverTime | 考勤(加班)返回结果处理:userCode:{},调休假期id:{}", userCode, leaveIdList);
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectByLeaveId(leaveIdList);
        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
            log.info("addOverTime | 考勤(加班)返回结果处理:userCode:{},调休假期详情表没有数据", userCode);
            return;
        }
        // 将假期按照阶段倒序
        userLeaveStageDetailList = userLeaveStageDetailList.stream()
                .filter(item -> !WhetherEnum.YES.getKey().equals(item.getLeaveMark()))
                .sorted(Comparator.comparing(UserLeaveStageDetailDO::getPercentSalary).reversed())
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(userLeaveStageDetailList)) {
            log.info("addOverTime | 考勤(加班)返回结果处理:userCode:{},调休假期详情表没有未结转数据", userCode);
            return;
        }
        // 更新调休假期余额
        UserLeaveStageDetailDO userLeaveStageDetail = userLeaveStageDetailList.get(0);
        // 调休假期余额
        BigDecimal leaveResidueMinutes = userLeaveStageDetail.getLeaveResidueMinutes();
        BigDecimal totalMinutes = leaveResidueMinutes.add(overtimeMinutes);
        log.info("addOverTime | 考勤(加班)返回结果处理:userCode:{},调休假期余额:{},加班时间:{},调休假期余额+加班时间:{}", userCode, leaveResidueMinutes, overtimeMinutes, totalMinutes);
        userLeaveStageDetail.setLeaveResidueMinutes(totalMinutes);
        BaseDOUtil.fillDOUpdate(userLeaveStageDetail);
        updateUserLeaveStageDetail.add(userLeaveStageDetail);

        // 生成假期记录
        UserLeaveRecordDO userLeaveRecord = new UserLeaveRecordDO();
        userLeaveRecord.setId(defaultIdWorker.nextId());
        userLeaveRecord.setUserId(userInfo.getId());
        userLeaveRecord.setUserCode(userCode);
        userLeaveRecord.setDate(date);
        userLeaveRecord.setDayId(nowDayId);
        userLeaveRecord.setConfigId(userLeaveDetail.get(0).getConfigId());
        userLeaveRecord.setLeaveName(userLeaveDetail.get(0).getLeaveName());
        userLeaveRecord.setLeaveType(LeaveCodeAssociateEnum.COMPENSATORY_LEAVE.getFullCode());
        userLeaveRecord.setType(LeaveTypeEnum.CANCEL.getCode());
        userLeaveRecord.setLeaveStartDay(startDate);
        userLeaveRecord.setLeaveEndDay(endDate);
        userLeaveRecord.setLeaveMinutes(overtimeMinutes);
        userLeaveRecord.setRemark(remark);
        BaseDOUtil.fillDOInsert(userLeaveRecord);
        userLeaveRecord.setOperationUserCode(Objects.isNull(applyUserInfo)
                ? userInfo.getUserCode() : applyUserInfo.getUserCode());
        userLeaveRecord.setOperationUserName(Objects.isNull(applyUserInfo)
                ? userInfo.getUserName() : applyUserInfo.getUserName());
        addUserLeaveRecord.add(userLeaveRecord);
    }

    /**
     * 构建实体
     * @param approvalFormList
     * @return
     */
    private List<OverTimeApprovalFormListVO> buildFormListVO(List<AttendanceApprovalFormDO> approvalFormList) {
        List<String> applyUserCodeList = approvalFormList.stream().map(AttendanceApprovalFormDO::getApplyUserCode).collect(Collectors.toList());
        List<AttendanceUser> userInfoList = userService.listUsersByUserCodes(applyUserCodeList);
        // 将userInfoList按照userCode 转为map
        Map<String, AttendanceUser> userCodeMap = userInfoList.stream().collect(Collectors.toMap(AttendanceUser::getUserCode, Function.identity()));

        List<Long> formIds = approvalFormList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceApprovalFormUserInfoDO> formDetailList = formManage.selectDetailByFormIdList(formIds);
        Map<Long, List<AttendanceApprovalFormUserInfoDO>> formDetailMap = Optional.ofNullable(formDetailList).orElse(Collections.emptyList())
                .stream().collect(Collectors.groupingBy(AttendanceApprovalFormUserInfoDO::getFormId));
        // 获取部门、岗位
        List<Long> deptIdList = formDetailList
                .stream()
                .filter(item -> Objects.nonNull(item.getDeptId()))
                .map(item -> item.getDeptId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendanceDept> deptDOList = deptService.selectDeptByIds(deptIdList);
        Map<Long, List<AttendanceDept>> deptIdMap = deptDOList
                .stream()
                .collect(Collectors.groupingBy(AttendanceDept::getId));

        List<Long> postIdList = formDetailList
                .stream()
                .filter(item -> Objects.nonNull(item.getPostId()))
                .map(item -> item.getPostId())
                .distinct()
                .collect(Collectors.toList());
        List<AttendancePost> entPostDOList = postService.listByPostList(postIdList);
        Map<Long, List<AttendancePost>> postIdMap = entPostDOList.stream().collect(Collectors.groupingBy(AttendancePost::getId));
        List<OverTimeApprovalFormListVO> targetList = Lists.newArrayList();
        for (AttendanceApprovalFormDO approvalForm : approvalFormList) {
            OverTimeApprovalFormListVO overTimeApprovalFormVO = AttendanceFormMapstruct.INSTANCE.toListVO(approvalForm);
            AttendanceUser userInfo = userCodeMap.get(approvalForm.getApplyUserCode());
            if (ObjectUtil.isNotNull(userInfo)) {
                overTimeApprovalFormVO.setApplyUserName(userInfo.getUserName());
                overTimeApprovalFormVO.setApplyDeptId(userInfo.getDeptId());
                overTimeApprovalFormVO.setApplyPostId(userInfo.getPostId());
                overTimeApprovalFormVO.setLocationCountry(userInfo.getLocationCountry());
            }

            List<AttendanceApprovalFormUserInfoDO> formUserInfoList = formDetailMap.get(approvalForm.getId());
            if (CollectionUtils.isNotEmpty(formUserInfoList)) {
                AttendanceApprovalFormUserInfoDO formUserInfo = formUserInfoList.get(0);
                overTimeApprovalFormVO.setStartDate(formUserInfo.getStartDate());
                overTimeApprovalFormVO.setEndDate(formUserInfo.getEndDate());
                List<AttendanceDept> userDeptList = deptIdMap.get(formUserInfo.getDeptId());
                if (CollectionUtils.isNotEmpty(userDeptList)) {
                    overTimeApprovalFormVO.setApplyDeptName(RequestInfoHolder.isChinese()
                            ? userDeptList.get(0).getDeptNameCn()
                            : userDeptList.get(0).getDeptNameEn());
                }

                List<AttendancePost> userPostList = postIdMap.get(formUserInfo.getPostId());
                if (CollectionUtils.isNotEmpty(userPostList)) {
                    overTimeApprovalFormVO.setApplyPostName(RequestInfoHolder.isChinese()
                            ? userPostList.get(0).getPostNameCn()
                            : userPostList.get(0).getPostNameEn());
                }
                overTimeApprovalFormVO.setEstimateDuration(Objects.isNull(formUserInfo.getEstimateDuration())
                        ? null : formUserInfo.getEstimateDuration().divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));

            }
            targetList.add(overTimeApprovalFormVO);
        }
        return targetList;
    }

}
