package com.imile.attendance.warehouse.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.HyperLink;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @project hrms
 * @description 日报展示
 * @date 2024/6/29 20:28:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DateReportVO extends ReportVO {

    /**
     * id
     */
    private Long id;

    /**
     * 进场日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date warehouseDate;

    /**
     * 算薪日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date salaryDate;

    /**
     * 入仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inTime;

    /**
     * 入仓对比照
     */
    private FaceRecognitionDetail inFaceRecognitionDetail;


    /**
     * 离仓时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date outTime;

    /**
     * 离仓对比照
     */
    private FaceRecognitionDetail outFaceRecognitionDetail;

    /**
     * 离仓间隔
     */
    private String interval;

    /**
     * 离仓比对结果
     */
    private String result;

    /**
     * 1正常 2异常 3无班次
     */
    @WithDict(typeCode = "WarehouseDateReportResult", ref = "resultCodeDesc")
    private Integer resultCode;

    /**
     * 当日结果描述
     */
    private String resultCodeDesc;

    /**
     * 班次类型
     */
    @WithDict(typeCode = "PunchClassType", ref = "classTypeDesc")
    private Integer classType;

    /**
     * 班次类型描述
     */
    private String classTypeDesc;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 应出勤时长
     */
    private BigDecimal requiredAttendanceTime;

    /**
     * 缺勤时长
     */
    private BigDecimal absenceTime;

    /**
     * 加班时长
     */
    private BigDecimal overtimeHours;

    /**
     * 实际出勤时长
     */
    private BigDecimal actualAttendanceTime;

    /**
     * 实际工作总时长
     */
    private BigDecimal actualWorkingHours;

    /**
     * 仓内实际出勤时长
     */
    private BigDecimal warehouseActualAttendanceTime;

    /**
     * 请假时长
     */
    private BigDecimal leaveHours;

    /**
     * 请假类型
     */
    private String leaveType;


    /**
     * 法定出勤时长时长 单位:小时
     */
    private BigDecimal legalWorkingHours;

    /**
     * 工作网点经度
     */
    private BigDecimal ocLongitude;

    /**
     * 工作网点纬度
     */
    private BigDecimal ocLatitude;

    /**
     * 考勤流水号
     */
    private String warehouseAttendanceCode;

    /**
     * 打卡状态 1正常 2异常
     */
    @WithDict(typeCode = "WarehousePunchStatus", ref = "punchStatusDesc")
    private Integer punchStatus;

    /**
     * 打卡状态描述
     */
    private String punchStatusDesc;

    /**
     * 供应商确认状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.VENDOR_CLASSES_CONFIRM_STATUS, ref = "confirmStatusDesc")
    private Integer confirmStatus;

    /**
     * 供应商确认状态
     */
    private String confirmStatusDesc;

    /**
     * 出入仓时间
     */
    private String entryAndExitTime;

    @Data
    @NoArgsConstructor
    public static class FaceRecognitionDetail {

        /**
         * 识别得分
         */
        private BigDecimal recognitionScore;

        /**
         * 录入照片
         */
        @HyperLink(ref = "facePhoto")
        private String facePhotoKey;

        /**
         * 识别照片
         */
        @HyperLink(ref = "recognitionPhoto")
        private String recognitionPhotoKey;

        /**
         * 录入照片
         */
        private String facePhoto;

        /**
         * 识别照片
         */
        private String recognitionPhoto;

        /**
         * 识别时间
         */
        private Date faceRecordTime;

        /**
         * 是否证件上传（0:人脸 1:证件）
         */
        private Integer isCertificatesUpload;


    }


}
