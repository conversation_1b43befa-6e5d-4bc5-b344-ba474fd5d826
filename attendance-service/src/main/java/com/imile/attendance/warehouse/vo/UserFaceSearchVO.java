package com.imile.attendance.warehouse.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @project hrms
 * @description 人脸搜索结果
 * @date 2024/6/29 20:02:33
 */
@Data
public class UserFaceSearchVO {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工Code
     */
    private String userCode;

    /**
     * 员工名称
     */
    private String userName;

    /**
     * 性别 1:男 2:女
     */
    private Integer sex;


    /**
     * 人脸图片url
     */
    private String faceUrl;

    /**
     * 网点id
     */
    private Long ocId;

    /**
     * 网点名称
     */
    private String ocName;

    /**
     * 供应商id
     */
    private Long vendorId;

    /**
     * 供应商编码
     */
    private String vendorCode;

    /**
     * 供应商名称
     */
    private String vendorName;


    /**
     * 相似度得分
     */
    private Float score;

    /**
     * 人脸比对通过标识
     */
    private Boolean pass;

    /**
     * 网点是否变更
     */
    private Boolean ocChange = Boolean.FALSE;

    /**
     * 供应商是否变更
     */
    private Boolean vendorChange = Boolean.FALSE;

    /**
     * 人脸重复
     */
    private Boolean faceRepeat = Boolean.FALSE;
}
