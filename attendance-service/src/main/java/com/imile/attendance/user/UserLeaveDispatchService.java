package com.imile.attendance.user;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.dao.DispatchUserRecordDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO;
import com.imile.attendance.infrastructure.repository.vacation.query.DispatchUserRecordQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.BigDecimalUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigIssueRuleService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.UserLeaveConfigHistoryService;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.attendance.vacation.dto.DispatchUserRecordDTO;
import com.imile.attendance.vacation.factory.UserLeaveRecordFactory;
import com.imile.attendance.vacation.factory.UserLeaveStageDetailFactory;
import com.imile.attendance.vacation.job.service.UserLeaveInspectionService;
import com.imile.common.enums.StatusEnum;
import com.imile.genesis.api.enums.WorkStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Year;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户派遣假期服务
 *
 * <AUTHOR>
 * @menu 假期
 * @date 2025/5/26
 */
@Service
@Slf4j
public class UserLeaveDispatchService {
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private UserLeaveRecordFactory userLeaveRecordManage;
    @Resource
    private DispatchUserRecordDao dispatchUserRecordDao;
    @Resource
    private UserLeaveConfigHistoryService userLeaveConfigHistoryService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigIssueRuleService companyLeaveConfigIssueRuleService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private UserLeaveStageDetailFactory userLeaveStageDetailFactory;
    @Resource
    private UserLeaveInspectionService userLeaveInspectionService;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    /**
     * 员工派遣折算假期逻辑
     *
     * @param param
     */
    public void conversationUserDispatchLeave(DispatchUserRecordDTO param) {
        log.info("conversationUserDispatchLeave param：{}", param);
        if (param == null || param.getUserCode() == null || param.getDispatchDate() == null) {
            log.info("conversationUserDispatchLeave param is null, userCode{} ", param.getUserCode());
            return;
        }
        //查询用户信息
        List<UserInfoDO> userInfoList = userInfoManage.listByUserCodes(Arrays.asList(param.getUserCode()));
        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("conversationUserDispatchLeave | userInfo is null, userCode{} ", param.getUserCode());
            return;
        }
        UserInfoDO userInfo = userInfoList.get(0);
        //已经离职的不处理
        if (WorkStatusEnum.DIMISSION.getCode().equals(userInfo.getWorkStatus())) {
            log.info("conversationUserDispatchLeave | OffBoarding, userCode{} ", param.getUserCode());
            return;
        }
        //查询用户入职时间
        List<AttendanceUserEntryRecord> userEntryRecordDOList = userEntryRecordService.selectUserEntryByUserIds(Arrays.asList(userInfo.getId()));
        if (CollectionUtils.isEmpty(userEntryRecordDOList)) {
            log.info("conversationUserDispatchLeave | userEntryRecordDOList is empty, userCode{} ", param.getUserCode());
            return;
        }
        // 判断派遣是否结束
        if (BusinessConstant.Y.equals(param.getEndFlag())) {
            // 查询是否已经有派遣结束记录，有了则不折算假期
            if (checkIsHaveRecord(BusinessConstant.Y, param.getUserCode())) {
                return;
            }
            // 派遣结束 折算处理派遣假期+补发假期
            converseDispatchEndLeaveConfig(userEntryRecordDOList, userInfo, param);
        } else {
            // 查询是否已经有派遣记录，有了则不折算假期
            if (checkIsHaveRecord(BusinessConstant.N, param.getUserCode())) {
                return;
            }
            // 查询人员历史假期范围
            List<CompanyLeaveConfigDO> historyLeaveConfigList = userLeaveConfigHistoryService.selectHistoryLeaveConfigByUser(userInfo.getId());
            // 派遣开始 折算处理原假期
            converseDispatchStartLeaveConfig(historyLeaveConfigList, userEntryRecordDOList, userInfo, param);
        }
    }

    /**
     * 派遣开始 折算处理原假期
     *
     * @param companyLeaveConfigList
     * @param userEntryRecordDOList
     * @param userInfo
     * @param param
     */
    private void converseDispatchStartLeaveConfig(List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                                  List<AttendanceUserEntryRecord> userEntryRecordDOList,
                                                  UserInfoDO userInfo,
                                                  DispatchUserRecordDTO param) {
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            log.info("converseDispatchStartLeaveConfig | companyLeaveConfigList is empty, userCode:{}", param.getUserCode());
            return;
        }
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            // 判断是否派遣假
            if (BusinessConstant.Y.equals(companyLeaveConfig.getIsDispatch())) {
                log.info("converseDispatchStartLeaveConfig | companyLeaveConfig is dispatch, userCode:{}, companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRule = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleListById(companyLeaveConfig.getId());
            if (CollectionUtils.isEmpty(leaveConfigIssueRule)) {
                log.info("converseDispatchStartLeaveConfig | leaveConfigIssueRule is not exist, userCode:{}, old_companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }
            // 判断是否需要折算
            Integer isConvertDispatch = leaveConfigIssueRule.get(0).getIsConvertDispatch();
            // 0和2 代表无需折算
            if (BusinessConstant.ZERO.equals(isConvertDispatch) || BusinessConstant.TWO.equals(isConvertDispatch)) {
                log.info("converseDispatchStartLeaveConfig is not need conversation, userCode:{}, old_companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }
            // 折算逻辑
            UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                    .userId(userInfo.getId())
                    .configId(companyLeaveConfig.getId())
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
            if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
                log.info("converseDispatchStartLeaveConfig | userLeaveDetailDOList is not have this leaveType, userCode:{}, old_companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            List<Long> userLeaveIdList = userLeaveDetailDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = userLeaveStageDetailService.selectByLeaveId(userLeaveIdList);
            if (CollectionUtils.isEmpty(userLeaveStageDetailDOList)) {
                log.info("converseDispatchStartLeaveConfig | userLeaveStageDetailDOList is not have this leaveType, userCode:{}, old_companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            // 计算折算时间
            // 入职时间
            Date confirmEntryDate = userEntryRecordDOList.get(0).getConfirmDate();
            // 派遣时间
            Date dispatchDate = param.getDispatchDate();
            if (Objects.isNull(dispatchDate)) {
                log.info("converseDispatchStartLeaveConfig | dispatchDate is null, userCode:{}, param:{} ", param.getUserCode(), param);
                continue;
            }
//            DateTime entryEndOfYear = DateUtil.endOfYear(confirmEntryDate);
            DateTime endOfYear = DateUtil.endOfYear(dispatchDate);
            // 判断是否同年
            BigDecimal conversationMinutes = BigDecimal.ZERO; // 折算后余额
            // 获取该年有多少天
            int year = dispatchDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
            int totalDays = Year.of(year).isLeap() ? 366 : 365;

            BigDecimal betweenDays = BigDecimal.valueOf(DateUtil.between(dispatchDate, endOfYear, DateUnit.DAY)).add(BigDecimal.ONE);
            BigDecimal percent = BigDecimalUtil.divide(betweenDays, BigDecimal.valueOf(totalDays), 4);
            if (percent.compareTo(BigDecimal.ZERO) == 0) {
                // 相差0天 不折算
                log.info("converseDispatchStartLeaveConfig | percent is zero, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            // 找出人员当年应该发放多少假期
            Map<Integer, BigDecimal> fixedLeaveQuota = userLeaveInspectionService.getFixedLeaveQuota(companyLeaveConfig.getId()
                    , confirmEntryDate, confirmEntryDate, dispatchDate, userInfo, false, false);
            if (Objects.isNull(fixedLeaveQuota)) {
                log.info("converseDispatchStartLeaveConfig | fixedLeaveQuota is null, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            log.info("converseDispatchStartLeaveConfig | fixedLeaveQuota is {}, userCode:{}, companyLeaveConfig:{}, confirmEntryDate:{}, dispatchDate:{}"
                    , JSON.toJSONString(fixedLeaveQuota), param.getUserCode(), companyLeaveConfig, confirmEntryDate, dispatchDate);
            Map<Integer, BigDecimal> conversationQuota = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : fixedLeaveQuota.entrySet()) {
                BigDecimal conversationQuotaMinutes = BigDecimalUtil.multiply(entry.getValue(), percent);
                // 记录每个阶段应扣除多少余额
                conversationQuota.put(entry.getKey(), conversationQuotaMinutes);
                // 计算折算扣除总额
                conversationMinutes = conversationMinutes.add(conversationQuotaMinutes);
            }
            // 折算后总额为0不修改
            if (BigDecimal.ZERO.equals(conversationMinutes)) {
                log.info("converseDispatchStartLeaveConfig | fixedLeaveQuota is zero, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            List<UserLeaveStageDetailDO> userUpdateLeaveStageDetailList = Lists.newArrayList();
            for (UserLeaveStageDetailDO leaveStageDetail : userLeaveStageDetailDOList) {
                if (BusinessConstant.Y.equals(leaveStageDetail.getLeaveMark())) {
                    log.info("converseDispatchStartLeaveConfig | leaveStageDetail leaveMark is Yes, userCode:{}, leaveStageDetail:{} "
                            , param.getUserCode(), leaveStageDetail);
                    continue;
                }
                // 判断余额是否为空
                BigDecimal leaveResidueMinutes = leaveStageDetail.getLeaveResidueMinutes();
                if (Objects.isNull(leaveResidueMinutes)) {
                    log.info("converseDispatchStartLeaveConfig | leaveResidueMinutes is null, userCode:{}, old_companyLeaveConfig:{} "
                            , param.getUserCode(), companyLeaveConfig);
                    continue;
                }
                log.info("converseDispatchStartLeaveConfig | leaveResidueMinutes is {}, userCode:{}, old_companyLeaveConfig:{} "
                        , leaveResidueMinutes, param.getUserCode(), companyLeaveConfig);
                BigDecimal stageQuota = conversationQuota.get(leaveStageDetail.getStage());
                if (Objects.isNull(stageQuota)) {
                    continue;
                }
                // 修改假期余额为当前余额 - 折算后余额
                log.info("converseDispatchStartLeaveConfig | stageQuota is {}, leaveResidueMinutes is {}, userCode:{}, old_companyLeaveConfig:{} "
                        , stageQuota, leaveResidueMinutes, param.getUserCode(), companyLeaveConfig);
                leaveStageDetail.setLeaveResidueMinutes(leaveResidueMinutes.subtract(stageQuota));
                BaseDOUtil.fillDOUpdate(leaveStageDetail);
                userUpdateLeaveStageDetailList.add(leaveStageDetail);
            }
            // 修改假期余额
            userLeaveStageDetailFactory.batchUpdateStageDetail(userUpdateLeaveStageDetailList);
            // 增加操作记录
            addDispatchLeaveRecord(userInfo, LeaveTypeEnum.DEDUCTION.getCode()
                    , RequestInfoHolder.isChinese() ? LeaveTypeEnum.DEDUCTION.getDesc() : LeaveTypeEnum.DEDUCTION.getDescEn()
                    , companyLeaveConfig
                    , conversationMinutes);
        }
    }

    /**
     * 派遣结束 折算处理派遣假期+补发假期
     *
     * @param userEntryRecordDOList
     * @param userInfo
     * @param param
     */
    private void converseDispatchEndLeaveConfig(List<AttendanceUserEntryRecord> userEntryRecordDOList,
                                                UserInfoDO userInfo,
                                                DispatchUserRecordDTO param) {
        // 折算处理原派遣假期
        List<CompanyLeaveConfigDO> historyLeaveConfigList = userLeaveConfigHistoryService.selectHistoryLeaveConfigByUser(userInfo.getId());
        convertLeaveBalance(historyLeaveConfigList, userEntryRecordDOList, userInfo, param);
        // 返还现有非派遣假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.selectConfigByUserCodeList(Arrays.asList(userInfo.getUserCode()));
        returnLeaveBalance(companyLeaveConfigList, userEntryRecordDOList, userInfo, param);
    }

    // 折算逻辑
    private void convertLeaveBalance(List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                     List<AttendanceUserEntryRecord> userEntryRecordDOList,
                                     UserInfoDO userInfo,
                                     DispatchUserRecordDTO param) {
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            // 判断是否派遣假
            if (!BusinessConstant.Y.equals(companyLeaveConfig.getIsDispatch())) {
                log.info("convertLeaveBalance | companyLeaveConfig is not dispatch, userCode:{}, companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRule = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleListById(companyLeaveConfig.getId());
            if (CollectionUtils.isEmpty(leaveConfigIssueRule)) {
                log.info("convertLeaveBalance | leaveConfigIssueRule is not exist, userCode:{}, dispatch_companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }
            // 判断是否需要折算
            Integer isConvertDispatch = leaveConfigIssueRule.get(0).getIsConvertDispatch();
            // 0和2 代表无需折算
            if (BusinessConstant.ZERO.equals(isConvertDispatch) || BusinessConstant.TWO.equals(isConvertDispatch)) {
                log.info("convertLeaveBalance is not need conversation, userCode:{}, dispatch_companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }
            // 折算逻辑
            UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                    .userId(userInfo.getId())
                    .configId(companyLeaveConfig.getId())
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
            if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
                log.info("convertLeaveBalance | userLeaveDetailDOList is not have this leaveType, userCode:{}, dispatch_companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            List<Long> userLeaveIdList = userLeaveDetailDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = userLeaveStageDetailService.selectByLeaveId(userLeaveIdList);
            if (CollectionUtils.isEmpty(userLeaveStageDetailDOList)) {
                log.info("convertLeaveBalance | userLeaveStageDetailDOList is not have this leaveType, userCode:{}, dispatch_companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            // 计算折算时间
            // 入职时间
            Date confirmEntryDate = userEntryRecordDOList.get(0).getConfirmDate();
            // 回国时间
            Date dispatchDate = param.getDispatchDate();
            if (Objects.isNull(dispatchDate)) {
                log.info("convertLeaveBalance | dispatchDate is null, userCode:{}, param:{} ", param.getUserCode(), param);
                continue;
            }
            // 上次派遣时间
            Date old_dispatchDate = getOldDispatchDate(param.getUserCode());
            if (Objects.isNull(old_dispatchDate)) {
                log.info("convertLeaveBalance | old_dispatchDate is null, userCode:{}, param:{} ", param.getUserCode(), param);
                continue;
            }
//            DateTime oldDispatchEndOfYear = DateUtil.endOfYear(old_dispatchDate);
            DateTime endOfYear = DateUtil.endOfYear(dispatchDate);
            // 判断是否同年
            BigDecimal conversationMinutes = BigDecimal.ZERO; // 折算后余额
            // 获取该年有多少天
            int year = dispatchDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
            int totalDays = Year.of(year).isLeap() ? 366 : 365;
            // 扣减比例
            BigDecimal betweenDays = BigDecimal.valueOf(DateUtil.between(dispatchDate, endOfYear, DateUnit.DAY)).add(BigDecimal.ONE);
            BigDecimal percent = BigDecimalUtil.divide(betweenDays, BigDecimal.valueOf(totalDays), 4);
            ;

            // 找出人员当年应该发放多少假期(回国但是为补扣派遣假 所以isBackAndReissue = false)
            Map<Integer, BigDecimal> fixedLeaveQuota = userLeaveInspectionService.getFixedLeaveQuota(companyLeaveConfig.getId()
                    , confirmEntryDate, old_dispatchDate, dispatchDate, userInfo, true, false);
            if (Objects.isNull(fixedLeaveQuota)) {
                log.info("convertLeaveBalance | fixedLeaveQuota is null, userCode:{}, dispatchDate:{}, old_dispatchDate:{}"
                        , param.getUserCode(), dispatchDate, old_dispatchDate);
                continue;
            }
            log.info("convertLeaveBalance | fixedLeaveQuota is {}, userCode:{}, companyLeaveConfig:{}, confirmEntryDate:{}, dispatchDate:{}, old_dispatchDate:{}"
                    , JSON.toJSONString(fixedLeaveQuota), param.getUserCode(), companyLeaveConfig, confirmEntryDate, dispatchDate, old_dispatchDate);
            Map<Integer, BigDecimal> conversationQuota = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : fixedLeaveQuota.entrySet()) {
                BigDecimal conversationQuotaMinutes = BigDecimalUtil.multiply(entry.getValue(), percent);
                conversationQuota.put(entry.getKey(), conversationQuotaMinutes);
                // 计算折算扣除总额
                conversationMinutes = conversationMinutes.add(conversationQuotaMinutes);
            }

            if (percent.compareTo(BigDecimal.ZERO) == 0) {
                // 相差0天 不折算
                log.info("convertLeaveBalance | percent is zero, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            List<UserLeaveStageDetailDO> userUpdateLeaveStageDetailList = Lists.newArrayList();
            for (UserLeaveStageDetailDO leaveStageDetail : userLeaveStageDetailDOList) {
                if (BusinessConstant.Y.equals(leaveStageDetail.getLeaveMark())) {
                    log.info("convertLeaveBalance | leaveStageDetail leaveMark is Yes, userCode:{}, leaveStageDetail:{} "
                            , param.getUserCode(), leaveStageDetail);
                    continue;
                }
                // 判断余额是否为空
                BigDecimal leaveResidueMinutes = leaveStageDetail.getLeaveResidueMinutes();
                if (Objects.isNull(leaveResidueMinutes)) {
                    log.info("convertLeaveBalance | leaveResidueMinutes is null, userCode:{}, dispatch_companyLeaveConfig:{} "
                            , param.getUserCode(), companyLeaveConfig);
                    continue;
                }
                BigDecimal stageQuota = conversationQuota.get(leaveStageDetail.getStage());
                if (Objects.isNull(stageQuota)) {
                    continue;
                }
                // 修改假期余额为当前余额 - 折算后余额
                log.info("convertLeaveBalance | stageQuota is {}, leaveResidueMinutes is {}, userCode:{}, dispatch_companyLeaveConfig:{} "
                        , stageQuota, leaveResidueMinutes, param.getUserCode(), companyLeaveConfig);
                leaveStageDetail.setLeaveResidueMinutes(leaveResidueMinutes.subtract(stageQuota));
                BaseDOUtil.fillDOUpdate(leaveStageDetail);
                userUpdateLeaveStageDetailList.add(leaveStageDetail);
            }
            // 修改假期余额
            userLeaveStageDetailFactory.batchUpdateStageDetail(userUpdateLeaveStageDetailList);
            // 增加操作记录
            addDispatchLeaveRecord(userInfo, LeaveTypeEnum.DEDUCTION.getCode()
                    , RequestInfoHolder.isChinese() ? LeaveTypeEnum.DEDUCTION.getDesc() : LeaveTypeEnum.DEDUCTION.getDescEn()
                    , companyLeaveConfig, conversationMinutes);
        }
    }

    // 返还逻辑
    private void returnLeaveBalance(List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                    List<AttendanceUserEntryRecord> userEntryRecordDOList,
                                    UserInfoDO userInfo,
                                    DispatchUserRecordDTO param) {
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            // 判断是否派遣假
            if (!BusinessConstant.N.equals(companyLeaveConfig.getIsDispatch())) {
                log.info("returnLeaveBalance | companyLeaveConfig is dispatch, userCode:{}, companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }

            // 判断是否需要返还
            List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRule = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleListById(companyLeaveConfig.getId());
            if (CollectionUtils.isEmpty(leaveConfigIssueRule)) {
                log.info("returnLeaveBalance | leaveConfigIssueRule is not exist, userCode:{}, companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }

            Integer isConvertDispatch = leaveConfigIssueRule.get(0).getIsConvertDispatch();
            // 0和2 代表无需折算, 之前没折算那就不用返还
            if (BusinessConstant.ZERO.equals(isConvertDispatch) || BusinessConstant.TWO.equals(isConvertDispatch)) {
                log.info("returnLeaveBalance is not need conversation, userCode:{}, companyLeaveConfig:{}, leaveConfigIssueRule:{} "
                        , param.getUserCode(), companyLeaveConfig, leaveConfigIssueRule);
                continue;
            }
            // 返还逻辑
            UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                    .userId(userInfo.getId())
                    .configId(companyLeaveConfig.getId())
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
            if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
                log.info("returnLeaveBalance | userLeaveDetailDOList is not have this leaveType, userCode:{}, companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            List<Long> userLeaveIdList = userLeaveDetailDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
            List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = userLeaveStageDetailService.selectByLeaveId(userLeaveIdList);
            if (CollectionUtils.isEmpty(userLeaveStageDetailDOList)) {
                log.info("returnLeaveBalance | userLeaveStageDetailDOList is not have this leaveType, userCode:{}, companyLeaveConfig:{} "
                        , param.getUserCode(), companyLeaveConfig);
                continue;
            }
            // 计算返还时间
            // 入职时间
            Date confirmEntryDate = userEntryRecordDOList.get(0).getConfirmDate();
            // 派遣结束时间
            Date dispatchDate = param.getDispatchDate();
            DateTime entryEndOfYear = DateUtil.endOfYear(confirmEntryDate);
            DateTime endOfYear = DateUtil.endOfYear(dispatchDate);
            // 上次派遣时间
            Date old_dispatchDate = getOldDispatchDate(param.getUserCode());
            if (Objects.isNull(old_dispatchDate)) {
                log.info("returnLeaveBalance | old_dispatchDate is null, userCode:{}, param:{} ", param.getUserCode(), param);
                continue;
            }
            DateTime oldDispatchEndOfYear = DateUtil.endOfYear(old_dispatchDate);
            // 判断是否同年
            BigDecimal conversationMinutes = BigDecimal.ZERO; // 回补后余额
            // 获取该年有多少天
            int year = dispatchDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
            int totalDays = Year.of(year).isLeap() ? 366 : 365;
            // 返还比例
            BigDecimal betweenDays = BigDecimal.valueOf(DateUtil.between(dispatchDate, endOfYear, DateUnit.DAY)).add(BigDecimal.ONE);
            BigDecimal percent = BigDecimalUtil.divide(betweenDays, BigDecimal.valueOf(totalDays), 4);
            if (entryEndOfYear.compareTo(endOfYear) != 0
                    && oldDispatchEndOfYear.compareTo(endOfYear) != 0) {
                // 入职日和回国日不同年，派遣日和回国日也不同年则 不回补假期
                log.info("returnLeaveBalance | oldDispatchDate is different of dispatchEndDate, userCode:{}, oldDispatchDate:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), old_dispatchDate, dispatchDate, confirmEntryDate);
                continue;
            }
            // 找出人员当年应该返还多少假期(回国且补发非派遣假 isBackAndReissue=true)
            Map<Integer, BigDecimal> fixedLeaveQuota = userLeaveInspectionService.getFixedLeaveQuota(companyLeaveConfig.getId()
                    , confirmEntryDate, old_dispatchDate, dispatchDate, userInfo, true, true);
            if (Objects.isNull(fixedLeaveQuota)) {
                log.info("returnLeaveBalance | fixedLeaveQuota is null, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            log.info("returnLeaveBalance | fixedLeaveQuota is {}, userCode:{}, companyLeaveConfig:{}, confirmEntryDate:{}, dispatchDate:{}"
                    , JSON.toJSONString(fixedLeaveQuota), param.getUserCode(), companyLeaveConfig, confirmEntryDate, dispatchDate);
            Map<Integer, BigDecimal> conversationQuota = new HashMap<>();
            for (Map.Entry<Integer, BigDecimal> entry : fixedLeaveQuota.entrySet()) {
                BigDecimal conversationQuotaMinutes = BigDecimalUtil.multiply(entry.getValue(), percent);
                conversationQuota.put(entry.getKey(), conversationQuotaMinutes);
                // 计算折算回补总额
                conversationMinutes = conversationMinutes.add(conversationQuotaMinutes);
            }

            if (percent.compareTo(BigDecimal.ZERO) == 0) {
                // 相差0天 不返还
                log.info("returnLeaveBalance | percent is zero, userCode:{}, dispatchDate:{}, confirmEntryDate:{}"
                        , param.getUserCode(), dispatchDate, confirmEntryDate);
                continue;
            }
            List<UserLeaveStageDetailDO> userUpdateLeaveStageDetailList = Lists.newArrayList();
            for (UserLeaveStageDetailDO leaveStageDetail : userLeaveStageDetailDOList) {
                if (BusinessConstant.Y.equals(leaveStageDetail.getLeaveMark())) {
                    log.info("returnLeaveBalance | leaveStageDetail leaveMark is Yes, userCode:{}, leaveStageDetail:{} "
                            , param.getUserCode(), leaveStageDetail);
                    continue;
                }
                // 判断余额是否为空
                BigDecimal leaveResidueMinutes = leaveStageDetail.getLeaveResidueMinutes();
                if (Objects.isNull(leaveResidueMinutes)) {
                    log.info("returnLeaveBalance | leaveResidueMinutes is null, userCode:{}, companyLeaveConfig:{} "
                            , param.getUserCode(), companyLeaveConfig);
                    continue;
                }
                BigDecimal stageQuota = conversationQuota.get(leaveStageDetail.getStage());
                if (Objects.isNull(stageQuota)) {
                    continue;
                }
                // 修改假期余额为当前余额 + 折算后余额
                log.info("returnLeaveBalance | stageQuota is {}, leaveResidueMinutes is {}, userCode:{}, companyLeaveConfig:{} "
                        , stageQuota, leaveResidueMinutes, param.getUserCode(), companyLeaveConfig);
                leaveStageDetail.setLeaveResidueMinutes(leaveResidueMinutes.add(stageQuota));
                BaseDOUtil.fillDOUpdate(leaveStageDetail);
                userUpdateLeaveStageDetailList.add(leaveStageDetail);
            }
            // 修改假期余额
            userLeaveStageDetailFactory.batchUpdateStageDetail(userUpdateLeaveStageDetailList);
            // 增加操作记录
            addDispatchLeaveRecord(userInfo, LeaveTypeEnum.REISSUE.getCode()
                    , RequestInfoHolder.isChinese() ? LeaveTypeEnum.REISSUE.getDesc() : LeaveTypeEnum.REISSUE.getDescEn()
                    , companyLeaveConfig, conversationMinutes);
        }
    }

    /**
     * 查询上次派遣日期
     *
     * @param userCode
     * @return
     */
    private Date getOldDispatchDate(String userCode) {
        // 上次派遣时间
        List<DispatchUserRecordDO> dispatchUserRecordList = dispatchUserRecordDao.selectDispatchInfoByUserCode(Arrays.asList(userCode));
        if (CollectionUtils.isEmpty(dispatchUserRecordList)) {
            return null;
        }
        List<DispatchUserRecordDO> record_new = dispatchUserRecordList.stream()
                .filter(item -> Objects.nonNull(item.getDispatchDate()) && BusinessConstant.N.equals(item.getEndFlag()))
                .sorted(Comparator.comparing(DispatchUserRecordDO::getDispatchDate))
                .collect(Collectors.toList());
        Date old_dispatchDate = record_new.get(0).getDispatchDate();
        return old_dispatchDate;
    }

    private Boolean checkIsHaveRecord(Integer endFlag, String userCode) {
        DispatchUserRecordQuery query = DispatchUserRecordQuery.builder()
                .userCode(userCode)
                .endFlag(endFlag)
                .build();
        List<DispatchUserRecordDO> dispatchUserRecordList = dispatchUserRecordDao.selectDispatchInfo(query);
        if (CollectionUtils.isNotEmpty(dispatchUserRecordList)) {
            log.info("conversationUserDispatchLeave | dispatchUserRecordList is exist, userCode{} ", userCode);
            return true;
        }
        return false;
    }


    /**
     * 新增派遣假期操作记录
     *
     * @param userInfo
     * @param recordType
     * @param desc
     * @param companyLeaveConfig
     * @param leaveMinutes
     */
    private void addDispatchLeaveRecord(UserInfoDO userInfo,
                                        String recordType, String desc,
                                        CompanyLeaveConfigDO companyLeaveConfig,
                                        BigDecimal leaveMinutes) {
        //需要添加假期折算记录
        UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
        recordDO.setId(defaultIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(new Date());
        recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
        recordDO.setConfigId(companyLeaveConfig.getId());
        recordDO.setLeaveName(companyLeaveConfig.getLeaveName());
        recordDO.setLeaveType(companyLeaveConfig.getLeaveType());
        recordDO.setType(recordType);
        recordDO.setLeaveMinutes(leaveMinutes);
        recordDO.setRemark(desc);
        BaseDOUtil.fillDOInsert(recordDO);
        recordDO.setOperationUserCode("admin");
        recordDO.setOperationUserName("admin");
        userLeaveRecordManage.save(recordDO);
    }

}
