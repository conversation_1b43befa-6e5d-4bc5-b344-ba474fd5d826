package com.imile.attendance.user;

import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.user.vo.UserInfoVO;
import com.imile.attendance.user.vo.UserOptionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
public interface UserService {

    List<UserOptionVO> getUserAssociateList(UserDaoQuery userDaoQuery);

    UserBaseInfoDTO getUserBaseInfo(String userCode);

    void setUserClassNature(String userCode, AttendanceUserEntryRecord attendanceUserEntryRecord);

    boolean checkValidUserAttendanceRange(Long userId);

    boolean checkValidDimissionUserAttendanceRange(Long userId);

    boolean isWarehouseSupportUser(UserInfoDO userInfoDO);

    /**
     * 检查系统灰度推广的用户范围
     *
     * @param userId 用户ID
     * @return boolean
     */
    boolean checkGrayscaleRange(Long userId);

    /**
     * 获取人员信息
     *
     * @param userCode 人员编码
     */
    UserInfoVO getUserInfo(String userCode);

    /**
     * 员工国家或部门调动班次性质处理
     * 若已存在班次性质跳过不处理
     */
    void userClassNatureHandler(String userCode);

}
