package com.imile.attendance.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2024/12/3
 * @Description jackson工具类
 */
@Slf4j
public class JsonUtils {

    // 定义两种日期格式
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = Jackson2ObjectMapperBuilder.json()
                .createXmlMapper(false)
                .dateFormat(new SimpleDateFormat(DateUtils.DATETIME_FORMAT))
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .featuresToDisable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
                .featuresToDisable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
//                .modules(new ParameterNamesModule(), new Jdk8Module(), getJavaTimeModule())
                .modules(
                        new ParameterNamesModule(),
                        new Jdk8Module(),
                        createJavaTimeModule(), // 自定义 JavaTimeModule
                        createMultiDateModule()  // 添加多日期支持模块
                )
                .build();
    }

    // 创建支持多种日期格式的模块
    private static SimpleModule createMultiDateModule() {
        SimpleModule module = new SimpleModule();

        // 1. 多格式日期反序列化器（用于 java.util.Date）
        module.addDeserializer(Date.class, new JsonDeserializer<Date>() {
            private final String[] formats = {DATE_TIME_FORMAT, DATE_FORMAT};

            @Override
            public Date deserialize(com.fasterxml.jackson.core.JsonParser p,
                                    DeserializationContext ctxt) throws IOException {
                String dateStr = p.getText();

                for (String format : formats) {
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat(format);
//                        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                        return sdf.parse(dateStr);
                    } catch (ParseException e) {
                        // 尝试下一种格式
                    }
                }
                throw new IllegalArgumentException("无法解析日期: " + dateStr);
            }
        });

        // 2. 多格式 LocalDateTime 反序列化器
        module.addDeserializer(LocalDateTime.class, new JsonDeserializer<LocalDateTime>() {
            private final DateTimeFormatter[] formatters = {
                    DateTimeFormatter.ofPattern(DATE_TIME_FORMAT),
                    DateTimeFormatter.ofPattern(DATE_FORMAT)
            };

            @Override
            public LocalDateTime deserialize(com.fasterxml.jackson.core.JsonParser p,
                                             DeserializationContext ctxt) throws IOException {
                String dateStr = p.getText();

                for (DateTimeFormatter formatter : formatters) {
                    try {
                        // 尝试解析为完整日期时间
                        return LocalDateTime.parse(dateStr, formatter);
                    } catch (DateTimeParseException e) {
                        // 尝试下一种格式
                    }
                }

                // 尝试解析为日期（自动补零时间）
                try {
                    LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern(DATE_FORMAT));
                    return date.atStartOfDay();
                } catch (DateTimeParseException e) {
                    throw new IllegalArgumentException("无法解析 LocalDateTime: " + dateStr);
                }
            }
        });

        // 3. 多格式 LocalDate 反序列化器
        module.addDeserializer(LocalDate.class, new JsonDeserializer<LocalDate>() {
            private final DateTimeFormatter[] formatters = {
                    DateTimeFormatter.ofPattern(DATE_FORMAT),
                    DateTimeFormatter.ofPattern(DATE_TIME_FORMAT) // 尝试从日期时间字符串中提取日期
            };

            @Override
            public LocalDate deserialize(com.fasterxml.jackson.core.JsonParser p,
                                         DeserializationContext ctxt) throws IOException {
                String dateStr = p.getText();

                for (DateTimeFormatter formatter : formatters) {
                    try {
                        // 尝试直接解析为日期
                        return LocalDate.parse(dateStr, formatter);
                    } catch (DateTimeParseException e) {
                        // 尝试下一种格式
                    }
                }

                // 尝试解析为日期时间然后提取日期部分
                try {
                    LocalDateTime dateTime = LocalDateTime.parse(dateStr,
                            DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));
                    return dateTime.toLocalDate();
                } catch (DateTimeParseException e) {
                    throw new IllegalArgumentException("无法解析 LocalDate: " + dateStr);
                }
            }
        });

        return module;
    }

    // 创建自定义的 JavaTimeModule
    private static JavaTimeModule createJavaTimeModule() {
        JavaTimeModule module = new JavaTimeModule();

        // 设置默认序列化格式
        module.addSerializer(LocalDate.class,
                new LocalDateSerializer(DateTimeFormatter.ofPattern(DATE_FORMAT)));

        module.addSerializer(LocalDateTime.class,
                new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)));

        return module;
    }


    @NotNull
    private static JavaTimeModule getJavaTimeModule() {
        DateTimeFormatter dtFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter desDtFormatter = new DateTimeFormatterBuilder()
                .appendPattern("yyyy-MM-dd[[[' ']['T']HH][:mm][:ss[.SSS]]")
                .parseDefaulting(ChronoField.HOUR_OF_DAY, 0)
                .parseDefaulting(ChronoField.MINUTE_OF_HOUR, 0)
                .parseDefaulting(ChronoField.SECOND_OF_MINUTE, 0)
                .parseDefaulting(ChronoField.NANO_OF_SECOND, 0)
                .toFormatter();
        DateTimeFormatter tFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dtFormatter));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(tFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(desDtFormatter));
        javaTimeModule.addDeserializer(LocalTime.class, new LocalTimeDeserializer(tFormatter));
        javaTimeModule.addSerializer(ZonedDateTime.class,
                new ZonedDateTimeSerializer(DateTimeFormatter.ISO_ZONED_DATE_TIME));
        return javaTimeModule;
    }


    @SneakyThrows
    public static <T> T read(String text, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return OBJECT_MAPPER.readValue(text, typeReference);

    }

    @SneakyThrows
    public static <T> T read(String text, Class<T> clazz) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        return OBJECT_MAPPER.readValue(text, clazz);
    }

    @SneakyThrows
    public static String write(Object obj) {
        if (obj == null) {
            return null;
        }
        return OBJECT_MAPPER.writeValueAsString(obj);

    }


    /**
     * Notice: invoke object.toString()
     */
    @SneakyThrows
    public static String writeToString(Object obj) {
        if (obj == null) {
            return "";
        }
        return obj.toString();

    }

    @SneakyThrows
    public static byte[] writeBytes(Object value) {
        if (value == null) {
            return new byte[0];
        }
        return OBJECT_MAPPER.writeValueAsBytes(value);
    }

    @SneakyThrows
    public static <T> T convert(Object obj, TypeReference<T> typeReference) {
        return OBJECT_MAPPER.convertValue(obj, typeReference);
    }

    @SneakyThrows
    public static <T> T convert(Object obj, Class<T> type) {
        return OBJECT_MAPPER.convertValue(obj, type);
    }


    public static class CustomModule extends SimpleModule {
        public CustomModule() {
            addDeserializer(List.class, new FlexibleListDeserializer());
        }
    }

    public static class FlexibleListDeserializer extends JsonDeserializer<List<?>> {
        @Override
        public List<?> deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String text = p.getText();

            // 尝试获取类型信息
            JavaType type = ctxt.getContextualType();

            // 如果无法获取类型信息，尝试推断类型
            if (type == null) {
                // 尝试解析为整数
                try {
                    return parseIntegerList(text);
                } catch (NumberFormatException e) {
                    // 如果解析整数失败，则作为字符串处理
                    return parseStringList(text);
                }
            }

            // 如果能获取到类型信息，按类型处理
            Class<?> elementType = type.getContentType().getRawClass();
            if (String.class.equals(elementType)) {
                return parseStringList(text);
            } else if (Integer.class.equals(elementType)) {
                return parseIntegerList(text);
            } else {
                // 如果类型不匹配，尝试智能推断
                try {
                    return parseIntegerList(text);
                } catch (NumberFormatException e) {
                    return parseStringList(text);
                }
            }
        }

        private List<String> parseStringList(String text) {
            return Arrays.stream(text.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        }

        private List<Integer> parseIntegerList(String text) {
            return Arrays.stream(text.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .map(Integer::parseInt)
                    .collect(Collectors.toList());
        }
    }
}
