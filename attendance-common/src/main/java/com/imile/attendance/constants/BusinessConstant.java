package com.imile.attendance.constants;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.common.enums.StatusEnum;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
public class BusinessConstant {

    public static final String SYSTEM_CODE = "Attendance";

    /**
     * 国家级别
     */
    public static final Integer COUNTRY_LEVEL = 1;

    /**
     * WPM
     */
    public static final String WPM = "WPM";

    /**
     * 供应商
     */
    public static final String SUPPLIER = "supplier";

    /**
     * 性别男
     */
    public static final String MALE = "male";

    /**
     * 性别女
     */
    public static final String FEMALE = "female";

    /**
     * 人脸识别通过得分
     */
    public static final BigDecimal FACE_PASS = new BigDecimal("0.85");

    /**
     * imile虚拟供应商ID
     */
    public static final Long IMILE_VIRTUAL_VENDOR_ID = 14L;

    public static final Integer Y = 1;
    public static final Integer N = 0;

    public static final Long LONG_ZERO = 0L;

    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;
    public static final Integer TWO = 2;
    public static final Integer THREE = 3;

    public static final int FIRST_ELEMENT_INDEX = 0;

    public static final Long DEFAULT_ORG_ID = 10L;

    public static final String LEFT_BRACKET = "(";

    public static final String RIGHT_BRACKET = ")";

    public static final String EMPTY_STR = "";

    public static final String DEFAULT_DELIMITER = ",";

    public static final String DEFAULT_HYPHEN = "-";

    public static final String WELL_NO = "#";

    public static final String UNDER_LINE = "_";

    public static final String CHINESE_PATTERN = "[\u4e00-\u9fa5]";

    public static final Integer MONTH_YEAR = 12;
    public static final Integer MONTH_DAY = 31;
    public static final Integer HOUR_DAY_LAST = 23;

    public static final List<String> ALL_STATUS_LIST = Lists.newArrayList(StatusEnum.ACTIVE.getCode(),StatusEnum.DISABLED.getCode());

    /**
     * 一百
     */
    public static final BigDecimal HUNDRED = new BigDecimal(100);
    public static final String ONE_HUNDRED = "100";
    public static final Integer ONE_HUNDRED_NUM = 100;
    public static final Integer TWO_HUNDRED_NUM = 200;
    public static final Integer FIVE_HUNDRED_NUM = 500;

    /**
     * 负一
     */
    public static final BigDecimal NEGATIVE_ONE = new BigDecimal(-1);

    /**
     * 一年
     */
    public static final BigDecimal ONE_YEAR = new BigDecimal(365);
    /**
     * 一月
     */
    public static final BigDecimal ONE_MONTH = new BigDecimal(30);
    /**
     * 一周
     */
    public static final BigDecimal ONE_WEEK = new BigDecimal(7);
    /**
     * 天偏移量
     */
    public static final Integer DEFAULT_OFFSET = -1;
    /**
     * 周期偏移量
     */
    public static final Integer DEFAULT_CYCLE_OFFSET = 0;

    /**
     * 默认时间
     * 1970-01-01
     */
    public static final String DEFAULT_TIME = "1970-01-01 ";

    public static final Integer DEFAULT_TIMEZONE = 8;

    /**
     * 一天的小时数
     */
    public static final BigDecimal HOURS = new BigDecimal("24");

    /**
     * 一小时的分钟数
     */
    public static final BigDecimal MINUTES = new BigDecimal("60");

    /**
     * 一天的分钟数
     */
    public static final BigDecimal DAYS_MINUTES = new BigDecimal("1440");

    /**
     * 默认的endTimeStamp 4102329599000L
     */
    public static final Long DEFAULT_END_TIMESTAMP = 4102329599000L;

    /**
     * 默认的endTime 2099-12-30 23:59:59
     */
    public static final Date DEFAULT_END_TIME = new Date(DEFAULT_END_TIMESTAMP);

    /**
     * 默认的endTimeStr 2099-12-30 23:59:59
     */
    public static final String DEFAULT_END_TIME_STRING = "2099-12-30 23:59:59";

    public static final Date DEFAULT_END_TIME_DATE = DateUtil.parse(DEFAULT_END_TIME_STRING, DateConvertUtils.FORMAT_DATE_TIME);

    /**
     * 默认每天的法定工作时长
     */
    public static final BigDecimal DEFAULT_LEGAL_WORKING_HOURS = new BigDecimal("8");
    public static final BigDecimal DEFAULT_LEGAL_WORKING_MINUTES = new BigDecimal("480");

    /**
     * 考勤计算相关常量
     */
    public static final int ATTENDANCE_CALCULATION_SCALE = 4; // 考勤计算精度（小数位数）
    public static final int ATTENDANCE_DISPLAY_SCALE = 2; // 考勤显示精度（小数位数）
    /**
     * 全球标识
     */
    public static final String GLOBAL_FLAG = "Global";

    /**
     * hrms pc端
     */
    public static final String HRMS_WEB = "hrms_web";

    public static final String PERCENT = "%";

    /**
     * 自动排班最大天数 180天
     */
    public final static Integer MAX_AUTO_SHIFT_DAYS = 180;

    /**
     * 批量处理的最大数量限制
     */
    public static final Integer MAX_BATCH_SIZE = 1000;

    //OSS私有桶类型
    public static final Integer OSS_PRIVATE_BUCKET_TYPE = 1;

    public static final String OCR_UPLOAD_FILE_PATH_PREFIX = "ocr/image/";
    public static final String FACE_UPLOAD_FILE_PATH_PREFIX = "face/image/";
    public static final String WAREHOUSE_UPLOAD_FILE_PATH_PREFIX = "warehouse/attendance/image/";

    /**
     * 仓内外包员工
     */
    public static final String HUB_SERVICE_SUPPLIER_TYPE = "HubService";

    /**
     * 劳务派遣
     */
    public static final String LABOR_DISPATCH = "laborDispatch";

    /**
     * 正式/挂靠
     */
    public static final String FORMAL = "formal";


    /**
     * 仓内考勤用工类型限制范围
     */
    public static final List<String> WAREHOUSE_EMPLOYEE_TYPE = Lists.newArrayList(LABOR_DISPATCH, FORMAL);

    /**
     * 网点员工管理用工类型限制范围
     */
    public static final List<String> OC_EMPLOYEE_TYPE = Lists.newArrayList(EmploymentTypeEnum.OS_FIXED_SALARY.getCode(),
            EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode());

    /**
     * 月报
     */
    public static final String MONTH = "MONTH";

    /**
     * 周报
     */
    public static final String WEEK = "WEEK";
    /**
     * 入仓类型
     */
    public static final String IN = "in";

    /**
     * 待出仓类型
     */
    public static final String WAIT_OUT = "waitOut";

    /**
     * 出仓类型
     */
    public static final String OUT = "out";

    public static final String VENDOR_ATTENDANCE_DETAIL_EXPORT_FILE_NAME = "imile%s";

    /**
     * 仓内班次人员出勤明细中文
     */
    public static final String WAREHOUSE_CLASS_ATTENDANCE_CHN_HEAD_SHEET_NAME = "人员出勤";

    /**
     * 仓内班次人员出勤详情中文导出模版头
     */
    public static final List<String> WAREHOUSE_CLASS_ATTENDANCE_CHN_HEAD_SHEET = Arrays.asList("作业日", "班次名称", "工作网点", "工作供应商", "人员姓名", "人员证件号", "入仓时间", "出仓时间", "应出勤时长", "实际出勤时长");

    /**
     * 仓内班次人员出勤明细英文
     */
    public static final String WAREHOUSE_CLASS_ATTENDANCE_EN_HEAD_SHEET_NAME = "Personnel attendance";

    /**
     * 仓内班次人员出勤详情英文导出模版头
     */
    public static final List<String> WAREHOUSE_CLASS_ATTENDANCE_EN_HEAD_SHEET = Arrays.asList("Homework Day", "Shift name", "Work network", "Work suppliers", "Personnel Name", "Personnel ID number", "Storage time", "Outbound time", "Attendance duration required", "Actual attendance duration");
    /**
     * 仓内班次人员出勤明细西语
     */
    public static final String WAREHOUSE_CLASS_ATTENDANCE_MX_HEAD_SHEET_NAME = "Asistencia del personal";

    /**
     * 仓内班次人员出勤详情西语导出模版头
     */
    public static final List<String> WAREHOUSE_CLASS_ATTENDANCE_MX_HEAD_SHEET = Arrays.asList("Día de la operación", "Nombre del turno", "Puntos de trabajo", "Proveedores de trabajo", "Nombre de la persona", "Número de identificación del personal", "Tiempo de almacenamiento", "Tiempo de salida", "Duración de la asistencia", "Duración real de la asistencia");

    /**
     * 仓内班次人员出勤明细葡萄牙语
     */
    public static final String WAREHOUSE_CLASS_ATTENDANCE_BR_HEAD_SHEET_NAME = "Participação do pessoal";
    /**
     * 仓内班次人员出勤详情葡萄牙语导出模版头
     */
    public static final List<String> WAREHOUSE_CLASS_ATTENDANCE_BR_HEAD_SHEET = Arrays.asList("Dia dos trabalhos de casa", "Nome de mudança", "Rede de trabalho", "Fornecedores de trabalho", "Nome do pessoal", "Número de identificação do pessoal", "Hora de armazenamento", "Tempo de saída", "Duração de assistência requerida", "Duração da assistência real");

    public static final Locale PT_BR = new Locale("pt", "BR");

    /**
     * 下班打卡考勤正常推送事件
     */
    public static final String OFF_WORK_PUNCH_IN = "OffWorkPunchIn";

    /**
     * 考勤异常处理后补推事件
     */
    public static final String OFF_WORK_PUNCH_REISSUE = "OffWorkPunchReissue";

    public static final String ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_CN = "实出勤时长从%sH，更新为%sH";

    public static final String ADD_DURATION_ACTUAL_ATTENDANCE_CONTENT_EN = "The actual attendance time has been updated from %sH to %sH";

    public static final String ADD_DURATION_ACTUAL_WORKING_CONTENT_CN = "实工作时长从%sH，更新为%sH";

    public static final String ADD_DURATION_ACTUAL_WORKING_CONTENT_EN = "The actual working hours have been updated from %sH to %sH";

    public static final String EMPLOYEE_DISABLED_REASON = "Staff has multiple accounts, and this account has been confirmed to be deactivated";

    public static final String FACE_PHOTO = "facePhoto";

    public static final String RECOGNITION_PHOTO = "recognitionPhoto";

    /**
     * 排班数据迁移相关常量
     */
    public static final class ShiftMigration {
        /**
         * 2025年1月1日的dayId，用作历史数据和当前数据的分界点
         */
        public static final Long SPLIT_DAY_ID = 20250101L;

        /**
         * 2024年12月31日的dayId，历史数据的结束日期
         */
        public static final Long HISTORY_END_DAY_ID = 20241231L;

        /**
         * 迁移分页大小
         */
        public static final Integer MIGRATION_PAGE_SIZE = 5000;

        /**
         * day_punch_type转换映射
         */
        public static final String DAY_PUNCH_TYPE_PH = "PH";
        public static final String DAY_PUNCH_TYPE_OFF = "OFF";
        public static final String DAY_SHIFT_RULE_H = "H";

        /**
         * 默认排班类型
         */
        public static final String DEFAULT_SHIFT_TYPE = "CUSTOM_SHIFT";

        /**
         * 从HRMS迁移过来的排班数据标识
         */
        public static final String MIGRATE_FROM_HRMS = "MIGRATE_FROM_HRMS";

        /**
         * 班次未映射到新考勤的标识
         */
        public static final String PUNCH_CLASS_NOT_IN_NEW_ATTENDANCE_FLAG = "punch class not in new attendance";
    }


    /**
     * 移动打卡请求有效时间,5分钟
     */
    public static final long EXPIRE_TIME_INTERNAL_MILLISECONDS = 5 * 60 * 1000L;

    public static final String IMILE_CLOCK_APP_ID = "1000146";

    public static final String IMILE_CLOCK_AGENT_ID = "1000156";

    public static final String IMILE_CLOCK_SECRET = "9AC8FD3AACDC3FE919FD60133C6FA802494F06185FFBA5D789E5F63DA5B3104D";

    public  static  final String CONFIRM_P_OPERATION_CONTENT_CN = "工作时长%s小时；实际工作时长%s小时";

    public  static  final String CONFIRM_P_OPERATION_CONTENT_EN = "working hours %s hours ; actual working hours %s hours";

    public static class SysDictDataTypeConstant {
        private SysDictDataTypeConstant() {
        }

        public static final String SYS_BOOLEAN = "SysBoolean";
        public static final String SEX = "SEX";
        public static final String MARITAL_STATUS = "maritalStatus";
        public static final String EDUCATION = "education";
        public static final String EMPLOYEE_TYPE = "employeeType";
        public static final String EMPLOYMENT_TYPE = "EmploymentType";
        public static final String CLASS_NATURE = "ClassNature";
        public static final String SALARY_SETTLEMENT_FORM_STATUS_ENUM = "SalarySettlementFormStatusEnum";
        public static final String SALARY_SETTLEMENT_FORM_SUBMIT_TYPE_ENUM = "SalarySettlementFormSubmitTypeEnum";
        public static final String SALARY_SUBMIT_TEMPLATE_COLLECTION_TYPE_ENUM = "SalarySubmitTemplateCollectionTypeEnum";
        public static final String SALARY_SUBMIT_TEMPLATE_APPROVAL_METHOD_ENUM = "SalarySubmitTemplateApprovalMethodEnum";
        public static final String SALARY_SETTLEMENT_USER_STATUS_ENUM = "SalarySettlementUserStatusEnum";
        public static final String SALARY_SETTLEMENT_USER_ITEM_STATUS_ENUM = "SalarySettlementUserItemStatusEnum";
        public static final String ENTRY_STATUS = "entryStatus";
        public static final String VISA_TYPE_OLD = "visaType";
        public static final String WORK_STATUS = "workStatus";
        public static final String TRANSFER_STATUS = "transferStatus";
        public static final String CERTIFICATE_TYPE_CODE = "certificateTypeCode";

        public static final String ACCOUNT_STATUS = "accountStatus";

        public static final String FMS_ACCOUNT_STATUS = "fmsAccountStatus";

        public static final String EMERGENCY_RELATION = "EmergencyRelation";

        public static final String DRIVER_TYPE = "driverType";

        public static final String HRMS_ABNORMAL_TYPE = "HrmsAbnormalType";

        public static final String WAREHOUSE_ABNORMAL_TYPE = "WarehouseAbnormalType";

        public static final String HRMS_ATTENDANCE_DAY_TYPE = "HrmsAttendanceDayType";

        public static final String ATTENDANCE_LEAVE_TYPE = "LeaveTypeConfig";

        public static final String ATTENDANCE_ABNORMAL_OPERATION_TYPE = "AttendanceAbnormalOperationTypeEnum";
        public static final String HR_ATTENDANCE_APPLICATION_FORM_STATUS = "HrAttendanceApplicationFormStatusEnum";


        /**
         * 离职状态
         */
        public static final String DIMISSION_STATUS = "dimissionStatus";
        public static final String ADMINISTRATOR_ATTRIBUTES = "administratorAttribute";

        /**
         * Hrms账号状态
         */
        public static final String HRMS_ACCOUNT_STATUS = "HrmsAccountStatus";

        /**
         * 入职渠道
         */
        public static final String HRMS_ONBOARDING_CHANNEL = "onboardingChannel";

        /**
         * 奖惩类型
         */
        public static final String HRMS_OWN_INCENTIVE_TYPE = "ownIncentiveType";

        /**
         * 计薪方式
         */
        public static final String HRMS_SALARY_METHOD = "HrmsSalaryMethod";
        /**
         * 计薪周期类型
         */
        public static final String HRMS_CYCLE_TYPE = "HrmsCycleType";
        /**
         * 缴纳基本类型
         */
        public static final String BASE_PAYMENT_TYPE = "basePaymentType";
        /**
         * 输入类型
         */
        public static final String INPUT_TYPE = "InputType";
        /**
         * 上班规则配置
         */
        public static final String PUNCH_TYPE = "HrmsPunchConfigType";
        /**
         * 加班补贴方式
         */
        public static final String SUBSIDY_TYPE = "HrmsAllowanceType";
        /**
         * 日期类型
         */
        public static final String DATE_TYPE = "hrmsDateType";

        /**
         * 打卡方式
         */
        public static final String PUNCH_CARD_TYPE = "PunchCardTypeSearch";
        /**
         * 打卡数据来源
         */
        public static final String PUNCH_SOURCE_TYPE = "HrmsPunchCardType";

        /**
         * 培训类型
         */
        public static final String TRAINING_TYPE = "TrainingType";

        /**
         * 培训状态
         */
        public static final String TRAINING_STATUS = "TrainingStatusEnum";

        /**
         * 培训状态
         */
        public static final String TRAINING_USER_STATUS = "TrainingUserStatusEnum";

        /**
         * 众包司机审核类型
         */
        public static final String REVIEW_TYPE = "reviewType";
        public static final String FREELANCER_DRIVER_AUDIT_TYPE = "FreelancerDriverAuditType";
        /**
         * 黑名单封禁状态
         */
        public static final String BAN_STATUS = "banStatus";

        /**
         * 调动类型
         */
        public static final String TRANSFER_TYPE = "hrmsTransferType";

        /**
         * 业务领域
         */
        public static final String BIZ_AREA = "bizArea";

        /**
         * 组织类型
         */
        public static final String DEPT_TYPE = "deptType";

        /**
         * 网点类型
         */
        public static final String OC_TYPE = "OcType";

        /**
         * 业务负责人属性
         */
        public static final String LEADER_PROPERTY = "leaderProperty";

        /**
         * 支付方式
         */
        public static final String ERS_PAYMENT_METHOD = "ErsPaymentMethod";

        /**
         * 是否
         */
        public static final String WHETHER = "YesOrNo";

        /**
         * 驾驶证等级
         */
        public static final String DRIVER_LICENSE_LEVEL = "DriverLicenseLevel";

        /**
         * 开户银行
         */
        public static final String ACCOUNT_BANK_NAME = "AccountBankName";

        /**
         * 工作经验
         */
        public static final String WORK_EXPERIENCE = "WorkExperience";

        public static final String OFFER_STATUS_ENUM = "OfferStatusEnum";

        public static final String HC_STATUS_ENUM = "HcStatusEnum";

        public static final String HC_REQUEST_TYPE_ENUM = "HcRequestTypeEnum";

        public static final String SALARY_BUDGET_TYPE_ENUM = "SalaryBudgetTypeEnum";

        public static final String BUDGET_ALLOCATION_TYPE_ENUM = "BudgetAllocationTypeEnum";

        public static final String IT_ASSETS_REQUIRED_TYPE_ENUM = "ITAssetsRequiredTypeEnum";

        public static final String HC_APPROVAL_INFO_STATUS_ENUM = "HcApprovalInfoStatusEnum";

        public static final String RECRUITMENT_TYPE_ENUM = "RecruitmentTypeEnum";

        public static final String RECRUITMENT_EMAIL_SEND_STATUS_ENUM = "RecruitmentEmailSendStatusEnum";
        public static final String FUNCTION_TYPE_ENUM = "FunctionTypeEnum";

        /**
         * 薪资项属性
         */
        public static final String SALARY_ITEM_ATTRIBUTE = "SalaryItemAttributeEnum";

        /**
         * OA费用项
         */
        public static final String OA_COST_TYPE = "OACostType";

        /**
         * 薪资项格式
         */
        public static final String SALARY_ITEM_FORMAT = "SalaryItemFormatEnum";

        /**
         * 薪资项取值方式
         */
        public static final String SALARY_ITEM_VALUE_TYPE = "SalaryItemValueTypeEnum";

        /**
         * 薪资项取值-报表展示
         */
        public static final String SALARY_ITEM_REPORT_VALUE = "SalaryItemReportValueEnum";

        /**
         * 薪资项类型
         */
        public static final String SALARY_ITEM_TYPE = "SalaryItemTypeEnum";

        /**
         * 计薪方案周期类型
         */
        public static final String SALARY_SCHEME_CYCLE_TYPE = "SalarySchemeCycleTypeEnum";


        /**
         * 发薪日期(周)
         */
        public static final String SALARY_PAY_DATA_WEEK = "SalaryPayDataWeekEnum";


        /**
         * 发薪日期(双周)
         */
        public static final String SALARY_PAY_DATA_DOUBLE_WEEK = "SalaryPayDataDoubleWeekEnum";

        /**
         * 薪资操作类型
         */
        public static final String SALARY_OPERATION_TYPE = "SalaryOperationTypeEnum";

        /**
         * 考勤报表配置类型
         */
        public static final String SALARY_REPORT_TYPE = "SalaryReportTypeEnum";

        /**
         * 薪资计算任务状态
         */
        public static final String SALARY_CALCULATE_TASK_STATUS = "SalaryCalculateTaskStatus";

        /**
         * 招聘操作日志行为类型
         */
        public static final String RECRUITMENT_OPERATION_ACTION_TYPE = "RecruitmentOperationActionType";

        public static final String RECRUITMENT_OPERATION_OFFER_ACTION_TYPE = "RecruitmentOperationOfferActionType";

        /**
         * 数据审批方式（提报后需审批MUST_APPROVAL / 提报后无需审批NONE_APPROVAL）
         */
        public static final String SALARY_APPROVAL_METHOD = "SalaryApprovalMethod";

        /**
         * 薪资计算任务计算人员类型（入职、离职、本月新增）
         */
        public static final String SALARY_CALCULATE_USER_STATUS_TYPE = "SalaryCalculateUserStatusType";

        /**
         * 薪资提报记录数据状态
         */
        public static final String SALARY_SUBMIT_RECORD_DATA_STATUS = "SalarySubmitRecordDataStatus";

        /**
         * 薪资提报记录操作类型
         */
        public static final String SALARY_SUBMIT_RECORD_OPERATE_TYPE = "SalarySubmitRecordOperateType";

        /**
         * 薪资提报记录数据更新原因
         */
        public static final String SALARY_SUBMIT_RECORD_STATUS_UPDATE_REASON = "SalarySubmitRecordStatusUpdateReason";

        /**
         * 薪资报表配置类型
         */
        public static final String SALARY_REPORT_CONFIG_TYPE = "SalaryReportConfigType";

        /**
         * 签证类型
         */
        public static final String VISA_TYPE = "GlobalVisaType";

        /**
         * 依赖人类型
         */
        public static final String DEPENDENT_TYPE = "DependentType";

        /**
         * 语言类型
         */
        public static final String LANGUAGE_TYPE = "GlobalLanguageType";

        /**
         * 证件字段
         */
        public static final String CERTIFICATE_FIELD = "CertificateField";

        /**
         * 民族
         */
        public static final String NATION = "Nation";

        /**
         * 户口类型
         */
        public static final String RESIDENCE_TYPE = "RegisteredResidenceType";

        /**
         * 政治面貌
         */
        public static final String POLITICAL_AFFILIATION = "PoliticalAffiliation";

        /**
         * 心理测试结果
         */
        public static final String MENTAL_ASSESSMENT_RESULT = "MentalAssessmentResult";

        /**
         * 税收类型
         */
        public static final String TAX_TYPE = "FreelancerDriverTaxType";

        /**
         * 计税方式
         */
        public static final String SALARY_TAXATION_METHOD = "SalaryTaxationMethod";

        /**
         * 生效类型
         */
        public static final String ACTIVE_TYPE = "activeType";

        /**
         * 生效状态
         */
        public static final String ACTIVE_STATUS = "activeTaskStatus";

        /**
         * 试用期转正结果
         */
        public static final String PROBATION_CONFIRMATION_RESULT = "ProbationConfirmationResult";

        /**
         * 试用期操作记录编码
         */
        public static final String PROBATION_LOG_OPERATION_TYPE = "ProbationOperationCode";

        /**
         * 调动状态(new)
         */
        public static final String USER_TRANSFORM_STATUS = "userTransformStatus";

        /**
         * 调动类型(new)
         */
        public static final String USER_TRANSFORM_TYPE = "userTransformType";

        /**
         * 人员合同类型
         */
        public static final String USER_CONTRACT_TYPE = "UserContractType";

        /**
         * 操作场景
         */
        public static final String OPERATION_SCENE = "HrmsOperationScene";
        /**
         * 班次结果确认状态
         */
        public static final String VENDOR_CLASSES_CONFIRM_STATUS = "VendorClassesConfirmStatus";

        /**
         * 仓内黑名单类型
         */
        public static final String WAREHOUSE_BLACK_TYPE = "WarehouseBlackTypeEnum";
        /**
         * 晋升类型
         */
        public static final String PROMOTION_TYPE = "PromotionType";
        /**
         * 晋升状态
         */
        public static final String PROMOTION_STATUS = "PromotionStatus";
    }

    public static class JobHandler {
        private JobHandler() {
        }

        /**
         * 日期相关数据初始化
         */
        public static final String DATE_INIT_HANDLER = "dateInitHandler";

        /**
         * 缺勤数据同步
         */
        public static final String ABSENT_DATA_SYNC_HANDLER = "absentDataSyncHandler";

        /**
         * 钉钉考勤数据同步
         */
        public static final String DING_TALK_PUNCH_DATA_SYNC_HANDLER = "dingTalkPunchDataSyncHandler";

        /**
         * 发送邮件给没有导入员工考勤的负责人/HR
         */
        public static final String ATTENDANCE_EMAIL_HANDLER = "attendanceEmailHandler";

        /**
         * 司机出勤，定时任务
         */
        public static final String DRIVER_ATTENDANCE_HANDLER = "driverAttendanceHandler";

        /**
         * 仓内员工出勤，定时任务
         */
        public static final String WARE_HOUSE_ATTENDANCE_HANDLER = "wareHouseAttendanceHandler";

        /**
         * 同步考勤机关联关系到HR
         */
        public static final String ZKTECO_AREA_RELATION_HANDLER = "zktecoAreaRelationHandler";

        /**
         * 员工薪资计算
         */
        public static final String USER_DAILY_SALARY_DETAIL_HANDLER = "userDailySalaryDetailHandler";

        /**
         * 员工假期信息初始化
         */
        public static final String USER_LEAVE_DETAIL_INIT_HANDLER = "userLeaveDetailInitHandler";

        /**
         * 员工假期巡检（如果员工不存在某一种假期，可以使用这个定时任务新增，保证可以获得所属国家）
         */
        public static final String USER_LEAVE_INSPECTION_HANDLER = "userLeaveInspectionHandler";

        /**
         * 员工假期巡检-new（如果员工不存在某一种假期，可以使用这个定时任务新增，保证可以获得所属国家）
         */
        public static final String USER_LEAVE_INSPECTION_NEW_HANDLER = "userLeaveInspectionNewHandler";

        /**
         * 用户假期结转处理
         */
        public static final String USER_LEAVE_CARRY_OVER_HANDLER = "userLeaveCarryOverHandler";

        /**
         * 用户假期失效处理
         */
        public static final String USER_LEAVE_INVALID_HANDLER = "userLeaveInvalidHandler";

        /**
         * 用户假期配置按年龄/工龄发放并符合跨层级新规则时重新计算发放假期
         */
        public static final String USER_LEAVE_REISSUE_HANDLER = "userLeaveReissueHandler";

        /**
         * 员工打卡记录巡检处理
         */
        public static final String EMPLOYEE_PUNCH_RECORD_INSPECTION_HANDLER = "employeePunchRecordInspectionHandler";

        /**
         * 用户假期失效处理
         */
        public static final String USER_LEAVE_DETAIL_CLEAR_HANDLER = "userLeaveDetailClearHandler";
        /**
         * 给用户绑定假期范围
         */
        public static final String USER_LEAVE_BIND_HANDLER = "userLeaveBindHandler";
        /**
         * 员工年假处理类
         */
        public static final String USER_ANNUAL_LEAVE_HANDLER = "userAnnualLeaveHandler";
        /**
         * 每日生成前一日的司机考勤信息
         */
        public static final String DRIVER_ATTENDANCE_DETAIL_HANDLER = "driverAttendanceDetailHandler";

        /**
         * 非司机外所有员工历史数据审核记录初始化
         */
        public static final String HISTORY_WAREHOUSE_AUDIT_RECORD_INIT_HANDLER = "historyWarehouseAuditRecordInitHandler";

        /**
         * 异常考勤定时任务
         */
        public static final String ABNORMAL_ATTENDANCE_HANDLER = "abnormalAttendanceHandler";

        /**
         * 刷新用户假期历史数据
         */
        public static final String EMPLOYEE_USER_LEAVE_HISTORY_HANDLER = "employeeUserLeaveHistoryHandler";

        /**
         * 刷新请假历史单据数据
         */
        public static final String EMPLOYEE_USER_LEAVE_FORM_HISTORY_HANDLER = "employeeUserLeaveFormHistoryHandler";

        /**
         * 考勤每日结算
         */
        public static final String ATTENDANCE_GENERATE_HANDLER = "attendanceGenerateHandler";

        /**
         * 考勤每日结算(暂定未打卡人员)
         */
        public static final String ATTENDANCE_DAY_GENERATE_HANDLER = "attendanceDayGenerateHandler";

        /**
         * 考勤每日生成考勤快照(所有人员)
         */
        public static final String ATTENDANCE_DAY_GENERATE_SNAPSHOT_HANDLER = "attendanceDayGenerateSnapshotHandler";

        /**
         * 仓内考勤未出仓打卡结算
         */
        public static final String ATTENDANCE_OUT_WAREHOUSE_MISS_PUNCH_HANDLER = "attendanceOutWarehouseMissPunchHandler";

        /**
         * 排班非活跃数据清理
         */
        public static final String SCHEDULING_INACTIVE_DATA_CLEAN_HANDLER = "schedulingInactiveDataCleanHandler";

        /**
         * 离职员工人脸特征向量清理
         */
        public static final String FACE_FEATURE_CLEAN_HANDLER = "faceFeatureCleanHandler";

        /**
         * 考勤每日结算(测试)
         */
        public static final String ATTENDANCE_GENERATE_TEST_HANDLER = "attendanceGenerateTestHandler";

        /**
         * 异常考勤-企业微信提醒
         */
        public static final String ABNORMAL_ATTENDANCE_REMINDER_HANDLER = "abnormalAttendanceReminderHandler";

        /**
         * 异常考勤-员工每日提醒
         */
        public static final String ABNORMAL_ATTENDANCE_DAY_REMIND_HANDLER = "abnormalAttendanceDayRemindHandler";

        /**
         * 加班单调休假发放
         */
        public static final String ATTENDANCE_OT_LEAVE_PAYMENT_HANDLER = "attendanceOTLeavePaymentHandler";

        /**
         * 同步员工信息到中控考勤机
         */
        public static final String SYNC_EMPLOYEE_ZKTECO_HANDLER = "syncEmployeeHandler";
        /**
         * 同步区域和部门信息到中控考勤机
         */
        public static final String SYNC_AREA_DEPT_ZKTECO_HANDLER = "syncAreaAndDeptHandler";
        /**
         * 同步中控考勤机打卡信息
         */
        public static final String SYNC_EMPLOYEE_ATTENDANCE_ZKTECO_HANDLER = "syncEmployeeAttendanceHandler";

        /**
         * 员工考勤日历配置数据转换
         */
        public static final String EMPLOYEE_ATTENDANCE_CONFIG_CONVERT_HANDLER = "employeeAttendanceConfigConvertHandler";

        /**
         * 考勤日历配置数据转换
         */
        public static final String ATTENDANCE_CONFIG_CONVERT_HANDLER = "attendanceConfigConvertHandler";

        /**
         * 同步tms司机排班数据和司机考核信息数据至司机绩效考核信息表
         */
        public static final String DRIVER_PERFORMANCE_INFO_SYNCHRONOUS_HANDLER = "driverPerformanceInfoSynchronousHandler";

        /**
         * 非司机员工每日打卡统计数据
         */
        public static final String SYNC_EMPLOYEE_ATTENDANCE_STATISTICAL_HANDLER = "syncEmployeeAttendanceStatisticalHandler";

        /**
         * 员工排班初始化
         */
        public static final String SYNC_EMPLOYEE_SCHEDULING_HANDLER = "employeeSchedulingHandler";

        /**
         * 定时任务针对OFF天数进行报警
         */
        public static final String PUNCH_OFF_HANDLER = "punchOffHandler";

        /**
         * 排班记录表弹性时间刷新
         */
        public static final String PUNCH_RECORD_ELASTIC_HANDLER = "punchRecordElasticHandler";

        /**
         * 员工打卡规则初始化
         */
        public static final String INIT_USER_PUNCH_CONFIG_HANDLER = "initUserPunchConfigHandler";

        /**
         * 异常考勤出勤时长数据刷新
         */
        public static final String ABNORMAL_PUNCH_ATTENDANCE_HOURS_REFRESH = "abnormalPunchAttendanceHoursRefresh";

        /**
         * 考勤周期日历数据自动续期
         */
        public static final String ATTENDANCE_CYCLE_CALENDAR_RENEWAL_HANDLER = "attendanceCycleCalendarRenewalHandler";

        /**
         * 初始化员工种类
         */
        public static final String INIT_USER_STAFF_HSNDLER = "initUserStaffTypeHandler";

        /**
         * 最早打卡时间刷新
         */
        public static final String EARLIEST_PUNCH_IN_TIME_HANDLER = "earliestPunchInTimeHandler";

        /**
         * 下班时间刷新
         */
        public static final String PUNCH_OUT_TIME_HANDLER = "punchOutTimeHandler";

        /**
         * 每天在职员工非司机排班打卡信息入库
         */
        public static final String EMPLOYEE_PUNCH_INFO_HANDLER = "employeePunchInfoHandler";

        /**
         * 每日更新前日考勤看板异常员工数
         */
        public static final String EMPLOYEE_ABNORMAL_DASHBOARD_HANDLER = "employeeAbnormalDashboardHandler";

        /**
         * 固定班次自动排班续期任务
         */
        public static final String FIXED_CLASS_AUTO_SCHEDULING_HANDLER = "fixedClassAutoSchedulingHandler";

        /**
         * HR考勤组到新考勤规则迁移任务
         */
        public static final String ATTENDANCE_RULE_MIGRATION_HANDLER = "attendanceRuleMigrationHandler";

        /**
         * 考勤规则回滚任务
         */
        public static final String ATTENDANCE_RULE_ROLLBACK_HANDLER = "attendanceRuleRollbackHandler";

        /**
         * 排班数据迁移任务
         */
        public static final String USER_SHIFT_CONFIG_MIGRATION_HANDLER = "userShiftConfigMigrationHandler";

        /**
         * 排班数据迁移回滚任务
         */
        public static final String USER_SHIFT_CONFIG_ROLLBACK_HANDLER = "userShiftConfigRollbackHandler";

        /**
         * 非灰度用户排班数据迁移任务
         */
        public static final String USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER = "userShiftConfigNoGrayscaleMigrationHandler";

        /**
         * 周期排班自动续上周期
         */
        public static final String CYCLE_SHIFT_HANDLER = "cycleShiftHandler";


        /**
         * 用户考勤周期补卡次数配置
         */
        public static final String USER_REISSUE_CARD_CONFIG_HANDLER = "userReissueCardConfigHandler";

        /**
         * 用户正常考勤表小时初始化为分钟
         */
        public static final String ATTENDANCE_DETAIL_MINUTE_INIT_HANDLER = "attendanceDetailMinuteInitHandler";

        /**
         * 用户假期余额转换到分钟初始化
         */
        public static final String USER_LEAVE_MINUTES_INIT_HANDLER = "userLeaveMinutesInitHandler";

        /**
         * 用户考勤单据确认周期监控
         */
        public static final String APPROVAL_FORM_CONFIRM_CYCLE_HANDLER = "approvalFormConfirmCycleHandler";

        /**
         * 异常考勤确认周期监控
         */
        public static final String ABNORMAL_CONFIRM_CYCLE_HANDLER = "abnormalConfirmCycleHandler";

        /**
         * 用户考勤日报生成
         */
        public static final String ATTENDANCE_DAY_REPORT_HANDLER = "attendanceDayReportHandler";

        /**
         * 新系统异常相关表同步老系统
         */
        public static final String ABNORMAL_TABLE_SYNC_HR_HANDLER = "abnormalTableSyncHrHandler";
        /**
         * 老系统异常相关表同步新系统
         */
        public static final String ABNORMAL_TABLE_SYNC_ATTENDANCE_HANDLER = "abnormalTableSyncAttendanceHandler";


        /**
         * 用户考勤日报生成
         */
        public static final String ATTENDANCE_DAY_REPORT_HISTORY_HANDLER = "attendanceDayReportHistoryHandler";

        /**
         * WPM自定义员工人脸特征向量清理
         */
        public static final String WPM_FACE_FEATURE_CLEAN_HANDLER = "wpmFaceFeatureCleanHandler";

        /**
         * 多班次跨夜打卡人员异常计算(兜底适配打卡但未计算异常场景)
         */
        public static final String ATTENDANCE_ACROSS_NIGHT_ABNORMAL_HANDLER = "attendanceAcrossNightAttendanceHandler";

    }


    public static class HermesDict {
        public static final String PUNCH_RULE_TYPE = "punchRuleType";
    }
}
