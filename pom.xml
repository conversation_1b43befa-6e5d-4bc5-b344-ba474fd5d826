<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.imile</groupId>
        <artifactId>com.imile.framework.jar</artifactId>
        <version>1.0.36</version>
    </parent>


    <artifactId>attendance</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>attendance</name>


    <modules>
        <module>attendance-api</module>
        <module>attendance-common</module>
        <module>attendance-integration</module>
        <module>attendance-infrastructure</module>
        <module>attendance-service</module>
        <module>attendance-manager</module>
        <module>attendance-web</module>
    </modules>


    <properties>
        <attendance-api.version>1.0.0-TEST-SNAPSHOT</attendance-api.version>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <spring-boot.version>2.3.5.RELEASE</spring-boot.version>
        <apollo.client.version>2.1.0</apollo.client.version>
        <hutool-all.version>5.8.21</hutool-all.version>
        <rocketmq.client.version>4.8.0</rocketmq.client.version>
        <xxl.job.version>2.1.2</xxl.job.version>
        <mybatis-plus-boot-starter.version>3.4.0</mybatis-plus-boot-starter.version>
        <dubbo.starter.version>2.7.4.1</dubbo.starter.version>
        <jmolecules-ddd.version>1.4.0</jmolecules-ddd.version>
        <jetcache.version>2.7.8</jetcache.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <imie.framework.version>1.0.48</imie.framework.version>
        <com.imile.framework.redis.version>1.0.12</com.imile.framework.redis.version>
        <translate-center-api.version>1.0.2</translate-center-api.version>
        <framework.aliyun.version>1.0.11-SNAPSHOT</framework.aliyun.version>
        <swagger.version>3.0.0</swagger.version>
        <skywalking.version>8.0.1</skywalking.version>
        <dynamic.datasource.version>3.5.0</dynamic.datasource.version>
        <druid.version>1.1.21</druid.version>
        <page.helper.version>5.1.9</page.helper.version>
        <imile.rocketmq.version>1.0.5</imile.rocketmq.version>
        <easy-log.version>1.0.0</easy-log.version>
        <mockito.version>3.4.0</mockito.version>
        <recognition.version>1.0.6</recognition.version>
        <arcsoft.version>3.1.0.0</arcsoft.version>
        <zxing.version>3.4.1</zxing.version>
        <!-- 该版本对应springboot 2.3.2.RELEASE -->
        <spring.cloud.context.version>2.2.9.RELEASE</spring.cloud.context.version>


        <!-- api version-->
        <ucenter.version>1.0.34-SNAPSHOT</ucenter.version>
        <lm-express-base.version>1.2.5</lm-express-base.version>
        <hrms.version>1.0.0-dev-SNAPSHOT</hrms.version>
        <genesis.version>1.1.2</genesis.version>
        <purchase.version>1.2.2</purchase.version>
        <fms.version>1.0.16</fms.version>
        <hermes.version>2.11.45</hermes.version>
        <ipep.version>1.0.9</ipep.version>
        <bpm.version>1.0.37-SNAPSHOT</bpm.version>
        <pcs.version>1.0.1</pcs.version>
<!--        <permission.version>1.0.21</permission.version>-->
        <permission.version>1.0.29</permission.version>
        <dtrack.version>1.0.1</dtrack.version>
        <saas-tms.version>2.6.27</saas-tms.version>
        <poi.version>4.0.0</poi.version>
        <poi.ooxml.version>4.0.0</poi.ooxml.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <!--全局排除spring-boot-starter-logging内的所有依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.imile</groupId>
            <artifactId>com.imile.framework.common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool-all.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.common</artifactId>
                <version>${imie.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.rpc</artifactId>
                <version>${imie.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.redis</artifactId>
                <version>${imie.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>translate-center-api</artifactId>
                <version>${translate-center-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.client.version}</version>
            </dependency>
            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl.job.version}</version>
            </dependency>
            <!-- mybatis-plus相关-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus-boot-starter.version}</version>
            </dependency>
            <!--多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic.datasource.version}</version>
            </dependency>
            <!-- alibaba 数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>tools</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.sun</groupId>
                        <artifactId>jconsole</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.jmolecules</groupId>
                <artifactId>jmolecules-ddd</artifactId>
                <version>${jmolecules-ddd.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-anno</artifactId>
                <version>${jetcache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-redisson</artifactId>
                <version>${jetcache.version}</version>
            </dependency>

            <!--mapstruct-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-log4j-2.x</artifactId>
                <version>${skywalking.version}</version>
            </dependency>

            <!-- 支持apollo动态刷新，需要引入该包 https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-context -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>${spring.cloud.context.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.izachwei</groupId>
                <artifactId>apollo-config-spring-boot-starter</artifactId>
                <version>1.0.4-RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>recognition-api</artifactId>
                <version>${recognition.version}</version>
            </dependency>

            <dependency>
                <groupId>com.arcsoft.face</groupId>
                <artifactId>arcsoft-sdk-face</artifactId>
                <version>${arcsoft.version}</version>
            </dependency>

            <!--二维码-->
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>${zxing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>com.imile.framework.aliyun</artifactId>
                <version>${framework.aliyun.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.rpc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- dubbo -->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-client</artifactId>
                <version>${rocketmq.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>imile-rocketmq-starter</artifactId>
                <version>${imile.rocketmq.version}</version>
            </dependency>
            <!--引入swagger的依赖-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!-- page helper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>${page.helper.version}</version>
            </dependency>

            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>ucenter-api</artifactId>
                <version>${ucenter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>purchase-api</artifactId>
                <version>${purchase.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>hrms-api</artifactId>
                <version>${hrms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>genesis-api</artifactId>
                <version>${genesis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>permission-api</artifactId>
                <version>${permission.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>hermes-api</artifactId>
                <version>${hermes.version}</version>
                <exclusions>
                    <!-- 这里将以下依赖全部排除掉,在项目基本不要使用里面的拦截器 -->
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.facade</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.imile</groupId>
                        <artifactId>com.imile.framework.rpc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>javax.servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>jakarta.validation</groupId>
                        <artifactId>jakarta.validation-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>ipep-api</artifactId>
                <version>${ipep.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>bpm-api</artifactId>
                <version>${bpm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>pcs-api</artifactId>
                <version>${pcs.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>dtrack-api</artifactId>
                <version>${dtrack.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>saas-tms-api</artifactId>
                <version>${saas-tms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github</groupId>
                <artifactId>easy-log</artifactId>
                <version>${easy-log.version}</version>
            </dependency>
            <dependency>
                <groupId>com.imile</groupId>
                <artifactId>fms-api</artifactId>
                <version>${fms.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.ooxml.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <repositories>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.imile-inc.com/nexus/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>snapshots</id>
            <name>Nexus Snapshots Repositories</name>
            <url>https://nexus.imile.com/nexus/content/repositories/snapshots</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>https://nexus.imile-inc.com/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>https://nexus.imile.com/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>utf-8</encoding>
                    <useDefaultDelimiters>true</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.8.RELEASE</version>
                <configuration>
                    <mainClass>com.imile.attendance.AttendanceApplication</mainClass>
                    <layout>ZIP</layout>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
            </plugin>
        </plugins>
    </build>



</project>
