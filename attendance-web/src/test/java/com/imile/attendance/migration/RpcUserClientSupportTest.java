package com.imile.attendance.migration;

import com.imile.attendance.base.BaseTest;
import com.imile.attendance.hrms.dto.UserDynamicInfoRpcDTO;
import com.imile.attendance.hrms.support.RpcUserClientSupport;
import org.junit.Test;
import org.springframework.test.annotation.Repeat;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/08/06
 * @Time 11:28
 * @Description
 */
public class RpcUserClientSupportTest extends BaseTest {

    @Resource
    private RpcUserClientSupport rpcUserClientSupport;

    @Test
    public void testListUserDynamicInfo() {
        String userCode = "2103429201";
        List<String> userCodes = Stream.iterate(0, i -> i + 1)
                .map(k -> k + "").limit(1000).collect(Collectors.toList());
        userCodes.add(userCode);
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = rpcUserClientSupport.listUserDynamicInfo(userCodes);
        System.out.println(userDynamicInfoRpcDTOList);
    }

    @Test
    public void testlistUserDynamicInfoById(){
        Long userId = 1103011271906697217L;
        List<Long> userIds = Stream.iterate(0, i -> i + 1)
                .map(Long::valueOf).limit(1000)
                .collect(Collectors.toList());
        userIds.add(userId);
        List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = rpcUserClientSupport.listUserDynamicInfoById(userIds);
        System.out.println(userDynamicInfoRpcDTOList);
    }

    @Test
    @Repeat(10)
    public void testGetUserDynamicInfoById() {
        // 使用一个有效的用户ID进行测试
        Long testUserId = 1103011271906697217L;
        UserDynamicInfoRpcDTO result = rpcUserClientSupport.getUserDynamicInfoById(testUserId);

        assertNotNull(result, "根据用户ID获取用户信息不应为null");
        assertEquals(testUserId, result.getId(), "返回的用户ID应与查询的ID一致");
    }

    @Test
    @Repeat(10)
    public void testGetUserDynamicInfoByCode() {
        // 使用一个有效的用户编码进行测试
        String testUserCode = "21032382";
        UserDynamicInfoRpcDTO result = rpcUserClientSupport.getUserDynamicInfoByCode(testUserCode);

        assertNotNull(result, "根据用户编码获取用户信息不应为null");
        assertEquals(testUserCode, result.getUserCode(), "返回的用户编码应与查询的编码一致");
        System.out.println(result);
    }

    @Test
    public void testGetUserWecomIdMap(){
        String userWecomId = rpcUserClientSupport.getUserWecomId("21032588");
        System.out.println(userWecomId);

        String userWecomId2 = rpcUserClientSupport.getUserWecomId("");
        System.out.println(userWecomId2);

        String userWecomId3 = rpcUserClientSupport.getUserWecomId("1031370");
        System.out.println(userWecomId3);

        String userWecomId4 = rpcUserClientSupport.getUserWecomId("2103610101");
        System.out.println(userWecomId4);
    }
}
