package com.imile.attendance.migration;


import com.imile.attendance.base.BaseTest;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.migration.impl.MigrationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * MigrationService 性能测试
 * 
 * <AUTHOR> chen
 * @date 2025/5/27
 */
@Slf4j
class MigrationServicePerformanceTest extends BaseTest {

    @Mock
    private AttendanceUserService userService;

    @InjectMocks
    private MigrationServiceImpl migrationService;

    private AttendanceUser testUser;
    private UserContext testUserContext;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(migrationService, "enableNewAttendanceCountries", "CHN,USA,GBR");
        
        // 创建测试用户
        testUser = new AttendanceUser();
        testUser.setUserCode("test_user");
        testUser.setLocationCountry("CHN");
        testUser.setUserName("测试用户");
        
        // 创建测试用户上下文
        testUserContext = new UserContext();
        testUserContext.setUserCode("test_user");
        
        // 设置用户上下文
        RequestInfoHolder.setLoginInfo(testUserContext);
    }

    @Test
    void testGetEnableNewAttendanceCountryPerformance() {
        log.info("=== 测试配置解析性能 ===");
        
        // 预热
        for (int i = 0; i < 10; i++) {
            migrationService.getEnableNewAttendanceCountry();
        }
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        int iterations = 1000;
        
        for (int i = 0; i < iterations; i++) {
            List<String> countries = migrationService.getEnableNewAttendanceCountry();
            assertEquals(3, countries.size());
            assertTrue(countries.contains("CHN"));
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        log.info("配置解析性能测试完成:");
        log.info("- 总执行次数: {}", iterations);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- QPS: {:.2f}", 1000.0 / avgTime);
        
        // 断言性能要求：平均响应时间应小于1ms（由于缓存）
        assertTrue(avgTime < 1.0, "配置解析平均耗时应小于1ms，实际: " + avgTime + "ms");
    }

    @Test
    void testGetCurrentUserCodePerformance() {
        log.info("=== 测试获取用户编码性能 ===");
        
        // 预热
        for (int i = 0; i < 10; i++) {
            migrationService.getCurrentUserCode();
        }
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        int iterations = 1000;
        
        for (int i = 0; i < iterations; i++) {
            String userCode = migrationService.getCurrentUserCode();
            assertEquals("test_user", userCode);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        log.info("获取用户编码性能测试完成:");
        log.info("- 总执行次数: {}", iterations);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- QPS: {:.2f}", 1000.0 / avgTime);
        
        // 断言性能要求：平均响应时间应小于0.5ms
        assertTrue(avgTime < 0.5, "获取用户编码平均耗时应小于0.5ms，实际: " + avgTime + "ms");
    }

    @Test
    void testVerifyUserIsEnableNewAttendancePerformance() {
        log.info("=== 测试用户验证性能 ===");
        
        // Mock 用户服务
        when(userService.getByUserCodeCache(anyString())).thenReturn(testUser);
        
        // 预热
        for (int i = 0; i < 10; i++) {
            migrationService.verifyUserIsEnableNewAttendance();
        }
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        int iterations = 1000;
        
        for (int i = 0; i < iterations; i++) {
            Boolean result = migrationService.verifyUserIsEnableNewAttendance();
            assertTrue(result);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        log.info("用户验证性能测试完成:");
        log.info("- 总执行次数: {}", iterations);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- QPS: {:.2f}", 1000.0 / avgTime);
        
        // 验证缓存调用次数（应该只调用一次，后续从缓存获取）
        verify(userService, atMost(iterations)).getByUserCodeCache("test_user");
        
        // 断言性能要求：平均响应时间应小于2ms
        assertTrue(avgTime < 2.0, "用户验证平均耗时应小于2ms，实际: " + avgTime + "ms");
    }

    @Test
    void testConcurrentPerformance() {
        log.info("=== 测试并发性能 ===");
        
        // Mock 用户服务
        when(userService.getByUserCodeCache(anyString())).thenReturn(testUser);
        
        int threadCount = 10;
        int iterationsPerThread = 100;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        long startTime = System.currentTimeMillis();
        
        // 创建并发任务
        CompletableFuture<Void>[] futures = IntStream.range(0, threadCount)
                .mapToObj(i -> CompletableFuture.runAsync(() -> {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        Boolean result = migrationService.verifyUserIsEnableNewAttendance();
                        assertTrue(result);
                    }
                }, executor))
                .toArray(CompletableFuture[]::new);
        
        // 等待所有任务完成
        CompletableFuture.allOf(futures).join();
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        int totalIterations = threadCount * iterationsPerThread;
        double avgTime = (double) totalTime / totalIterations;
        double qps = 1000.0 * totalIterations / totalTime;
        
        log.info("并发性能测试完成:");
        log.info("- 并发线程数: {}", threadCount);
        log.info("- 每线程执行次数: {}", iterationsPerThread);
        log.info("- 总执行次数: {}", totalIterations);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        log.info("- QPS: {:.2f}", qps);
        
        executor.shutdown();
        
        // 断言并发性能要求：QPS应大于500
        assertTrue(qps > 500, "并发QPS应大于500，实际: " + qps);
    }

    @Test
    void testCacheEffectiveness() {
        log.info("=== 测试缓存有效性 ===");
        
        // Mock 用户服务，模拟数据库查询延迟
        when(userService.getByUserCodeCache(anyString())).thenAnswer(invocation -> {
            // 模拟数据库查询延迟
            Thread.sleep(10);
            return testUser;
        });
        
        // 第一次调用（缓存未命中）
        long startTime1 = System.currentTimeMillis();
        List<String> countries1 = migrationService.getEnableNewAttendanceCountry();
        long time1 = System.currentTimeMillis() - startTime1;
        
        // 第二次调用（缓存命中）
        long startTime2 = System.currentTimeMillis();
        List<String> countries2 = migrationService.getEnableNewAttendanceCountry();
        long time2 = System.currentTimeMillis() - startTime2;
        
        log.info("缓存有效性测试:");
        log.info("- 第一次调用耗时: {}ms", time1);
        log.info("- 第二次调用耗时: {}ms", time2);
        log.info("- 性能提升: {:.2f}倍", (double) time1 / time2);
        
        assertEquals(countries1, countries2);
        // 缓存命中应该显著提升性能
        assertTrue(time2 < time1 / 2, "缓存应该显著提升性能");
    }

    @Test
    void testErrorHandlingPerformance() {
        log.info("=== 测试异常处理性能 ===");
        
        // Mock 用户服务抛出异常
        when(userService.getByUserCodeCache(anyString())).thenThrow(new RuntimeException("模拟数据库异常"));
        
        long startTime = System.currentTimeMillis();
        int iterations = 100;
        
        for (int i = 0; i < iterations; i++) {
            Boolean result = migrationService.verifyUserIsEnableNewAttendance();
            assertFalse(result); // 异常情况应返回false
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / iterations;
        
        log.info("异常处理性能测试完成:");
        log.info("- 总执行次数: {}", iterations);
        log.info("- 总耗时: {}ms", totalTime);
        log.info("- 平均耗时: {:.2f}ms", avgTime);
        
        // 异常处理不应显著影响性能
        assertTrue(avgTime < 5.0, "异常处理平均耗时应小于5ms，实际: " + avgTime + "ms");
    }
}
