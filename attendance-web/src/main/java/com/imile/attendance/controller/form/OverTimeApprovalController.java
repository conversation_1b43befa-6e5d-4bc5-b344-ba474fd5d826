package com.imile.attendance.controller.form;

import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.overtime.OvertimeApprovalService;
import com.imile.attendance.form.biz.overtime.OvertimeConfigAndCalculationService;
import com.imile.attendance.form.biz.overtime.param.OverTimeAddParam;
import com.imile.attendance.form.biz.overtime.param.OverTimeCalcParam;
import com.imile.attendance.form.biz.overtime.param.OverTimeDetailParam;
import com.imile.attendance.form.biz.overtime.param.OverTimeListParam;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalCloverListVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormExportVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormListVO;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFromVO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.param.ApplicationFormCancelParam;
import com.imile.attendance.form.param.ApplicationFormDeleteParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: HR考勤请假审批入口
 * @author: han.wang
 * @createDate: 2025-5-19
 */

@Slf4j
@RestController
@RequestMapping("/attendance/approval/overtime")
public class OverTimeApprovalController extends BaseController {
    @Resource
    private OvertimeApprovalService overtimeApprovalService;
    @Resource
    private OvertimeConfigAndCalculationService overtimeConfigAndCalculationService;
    @Resource
    private ConverterService converterService;
    @Resource
    private ImileRedisClient redissonClient;

    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_OVERTIME_SYNC:";

    /**
     * 加班申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> overTimeAdd(@RequestBody @Validated OverTimeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = overtimeApprovalService.overTimeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 加班申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/update")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> overTimeUpdate(@RequestBody @Validated OverTimeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = overtimeApprovalService.overTimeUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 详情
     */
    @PostMapping("/detail")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<OverTimeApprovalFromVO> detail(@RequestBody OverTimeDetailParam param) {
        OverTimeApprovalFromVO detail = overtimeApprovalService.getApprovalFromDetail(param.getApprovalFormId());
        return Result.ok(detail);
    }

    /**
     * 加班列表
     */
    @PostMapping("/list")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<PaginationResult<OverTimeApprovalFormListVO>> list(@RequestBody OverTimeListParam param) {
        PaginationResult<OverTimeApprovalFormListVO> detail = overtimeApprovalService.list(param);
        // 处理注解
        converterService.withAnnotation(detail.getResults());
        return Result.ok(detail);
    }

    /**
     * 加班列表
     */

    @PostMapping("/clover/list")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<PaginationResult<OverTimeApprovalCloverListVO>> cloverList(@RequestBody OverTimeListParam param) {
        PaginationResult<OverTimeApprovalListDTO> listDTO = overtimeApprovalService.cloverList(param);
        PaginationResult<OverTimeApprovalCloverListVO> listVO = this.convertPage(listDTO, OverTimeApprovalCloverListVO.class);

        return Result.ok(listVO);
    }

    /**
     * 取消
     */

    @PostMapping("/cancel")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<Boolean> cancel(@RequestBody @Validated ApplicationFormCancelParam param) {
        overtimeApprovalService.cancel(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 删除
     */

    @PostMapping("/delete")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<Boolean> delete(@RequestBody @Validated ApplicationFormDeleteParam param) {
        overtimeApprovalService.delete(param.getFormId());
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 加班申请预览
     */

    @PostMapping("/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> preview(@RequestBody @Validated OverTimeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = overtimeApprovalService.overtimePreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 加班申请预计加班时长计算
     */

    @PostMapping("/calc")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<BigDecimal> calc(@RequestBody @Validated OverTimeCalcParam param) {
        BigDecimal time = overtimeConfigAndCalculationService.overtimeCalculate(param);
        return Result.ok(time);
    }

    /**
     * 校验登录用户常驻国是否包含加班权限
     */

    @GetMapping("/checkLoginUserAuth")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<Boolean> checkLoginUserAuth() {
        Boolean isHaveOverTimeAuth = overtimeConfigAndCalculationService.checkUserOverTimeAuth();
        return Result.ok(isHaveOverTimeAuth);
    }

    /**
     * 加班单据信息导出
     */
    @PostMapping("/export")
    @ExportParamFill
    public Result<PaginationResult<OverTimeApprovalFormExportVO>> overTimeExport(HttpServletRequest request,
                                                                             OverTimeListParam param) {
        setExcelCallBackParam(request, param);
        PaginationResult<OverTimeApprovalFormExportVO> res = overtimeApprovalService.listExport(param);
        return Result.ok(res);
    }
}
