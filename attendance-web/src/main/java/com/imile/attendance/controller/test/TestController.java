package com.imile.attendance.controller.test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.abnormal.job.AttendanceDayGenerateSnapshotHandler;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.clock.job.AttendanceDayGenerateHandler;
import com.imile.attendance.cycleConfig.job.AttendanceCycleCalendarRenewalHandler;
import com.imile.attendance.deviceConfig.AttendanceGpsConfigService;
import com.imile.attendance.deviceConfig.AttendanceWifiConfigService;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigImportDTO;
import com.imile.attendance.driver.service.DriverDataManagementService;
import com.imile.attendance.enums.ZktVersionEnum;
import com.imile.attendance.form.CommonFormOperationService;
import com.imile.attendance.form.biz.overtime.OvertimeApprovalService;
import com.imile.attendance.form.biz.overtime.param.OverTimeListParam;
import com.imile.attendance.form.biz.overtime.vo.OverTimeApprovalFormExportVO;
import com.imile.attendance.form.biz.reissueCard.UserReissueCardCountService;
import com.imile.attendance.form.biz.reissueCard.dto.UserCycleReissueCardCountDeleteResult;
import com.imile.attendance.form.biz.reissueCard.param.UserCycleReissueCardCountDeleteParam;
import com.imile.attendance.form.param.AttendanceApprovalInfoParam;
import com.imile.attendance.form.vo.FormInfoExportVO;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.mq.MqFailRecordService;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.migration.AttendanceMigrationApi;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.migration.UserClassNatureService;
import com.imile.attendance.migration.UserShiftConfigMigrationService;
import com.imile.attendance.migration.dto.BatchUpdateShiftTypeResult;
import com.imile.attendance.migration.dto.BatchUserMigrationVerifyResult;
import com.imile.attendance.migration.dto.UserMigrationVerifyResult;
import com.imile.attendance.migration.param.BatchUserMigrationVerifyParam;
import com.imile.attendance.migration.param.RefreshUserClassNatureByCountryRequest;
import com.imile.attendance.migration.param.UserMigrationVerifyParam;
import com.imile.attendance.report.AttendanceReportApi;
import com.imile.attendance.report.day.AttendanceDayReportService;
import com.imile.attendance.report.day.job.AttendanceDayReportHandler;
import com.imile.attendance.report.day.job.AttendanceDayReportHistoryHandler;
import com.imile.attendance.report.day.job.param.DayReportJobHistoryParam;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.day.query.DayReportListQuery;
import com.imile.attendance.report.day.vo.UserDayReportExportVO;
import com.imile.attendance.report.param.AttendanceMonthReportParam;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.PunchConfigService;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.RuleConfigQueryService;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.rule.command.PunchConfigAddCommand;
import com.imile.attendance.rule.command.PunchConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.PunchConfigUpdateCommand;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.PunchConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.query.PunchConfigUserQuery;
import com.imile.attendance.rule.query.RuleConfigSelectQuery;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.factory.CycleShiftConfigFactory;
import com.imile.attendance.shift.param.FixedClassAutoRenewalParam;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.sync.hrms.HrUserInfoTableSyncHandler;
import com.imile.attendance.third.ThirdZktecoService;
import com.imile.attendance.third.job.SyncEmployeeAttendanceHandler;
import com.imile.attendance.user.UserLeaveIpepService;
import com.imile.attendance.user.dto.UserLeaveBalanceImportDTO;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.vacation.job.UserLeaveCarryOverHandler;
import com.imile.attendance.vacation.job.UserLeaveInvalidHandler;
import com.imile.attendance.vacation.param.UserLeaveCarryOverParam;
import com.imile.attendance.vacation.param.UserLeaveInvalidParam;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.rpc.common.RpcResult;
import com.imile.ucenter.api.authenticate.NoLoginRequired;
import com.xxl.job.core.biz.model.ReturnT;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Resource
    private HrUserInfoTableSyncHandler hrUserInfoTableSyncHandler;
    @Resource
    private PunchConfigService punchConfigService;
    @Resource
    private MqFailRecordService mqFailRecordService;
    @Resource
    private UserShiftService userShiftService;
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    private CycleShiftConfigFactory cycleShiftConfigFactory;
    @Resource
    private RuleConfigQueryService ruleConfigQueryService;
    @Resource
    private AttendanceGpsConfigService attendanceGpsConfigService;
    @Resource
    private AttendanceWifiConfigService attendanceWifiConfigService;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private AttendanceDayGenerateHandler attendanceDayGenerateHandler;
    @Resource
    private UserLeaveCarryOverHandler userLeaveCarryOverHandler;
    @Resource
    private UserLeaveInvalidHandler userLeaveInvalidHandler;
    @Resource
    private UserLeaveIpepService userLeaveIpepService;
    @Resource
    private CommonFormOperationService commonFormOperationService;
    @Resource
    private OvertimeApprovalService overtimeApprovalService;
    @Resource
    private ThirdZktecoService thirdZktecoService;
    @Resource
    private PunchClassConfigApplicationService punchClassConfigApplicationService;
    @Resource
    private UserClassNatureService userClassNatureService;
    @Resource
    private AttendanceDayReportHandler attendanceDayReportHandler;
    @Resource
    private AttendanceDayReportHistoryHandler attendanceDayReportHistoryHandler;
    @Resource
    private AttendanceDayReportService attendanceDayReportService;
    @Resource
    private DriverDataManagementService driverDataManagementService;
    @Resource
    private AttendanceDayGenerateSnapshotHandler attendanceDayGenerateSnapshotHandler;
    @Resource
    private MigrationService migrationService;
    @Resource
    private AttendanceMigrationApi attendanceMigrationApi;
    @Resource
    private UserReissueCardCountService userReissueCardCountService;
    @Resource
    private AttendanceReportApi attendanceReportApi;
    @Resource
    private UserShiftConfigMigrationService userShiftConfigMigrationService;
    @Resource
    private SyncEmployeeAttendanceHandler syncEmployeeAttendanceHandler;
    @Resource
    private AttendanceCycleCalendarRenewalHandler attendanceCycleCalendarRenewalHandler;

    @GetMapping("/getJVMDefaultZoneId")
    @NoLoginRequired
    public Result<String> getJVMDefaultZoneId() {
        return Result.ok(DateHelper.getJVMDefaultZoneId());
    }

    /**
     * @param param
     */
    @GetMapping("/syncHrUserInfo")
    @NoLoginRequired
    public void syncHrUserInfo(String param) {
        hrUserInfoTableSyncHandler.syncHrUserInfo(param);
    }

    @PostMapping("/addPunchConfig")
    @NoLoginRequired
    public Result<Void> addPunchConfig(@RequestBody PunchConfigAddCommand addCommand) {
        punchConfigService.add(addCommand);
        return Result.ok();
    }


    @PostMapping("/checkUpdateRule")
    @NoLoginRequired
    public Result<UpdateRuleReflectResult> checkUpdateRule(@RequestBody PunchConfigUpdateCommand updateCommand) {
        return Result.ok(punchConfigService.checkUpdateRule(updateCommand));
    }

    /**
     * 更新考勤配置
     *
     * @param updateCommand 包含更新信息的命令对象
     * @return 操作结果
     */
    @PostMapping("/updatePunchConfig")
    @NoLoginRequired
    public Result<Void> updatePunchConfig(@RequestBody PunchConfigUpdateCommand updateCommand) {
        punchConfigService.update(updateCommand);
        return Result.ok();
    }


    @PostMapping("/checkStatusSwitch")
    @NoLoginRequired
    public Result<UpdateRuleReflectResult> checkStatusSwitch(@RequestBody PunchConfigStatusSwitchCommand statusSwitchCommand) {
        return Result.ok(punchConfigService.checkStatusSwitch(statusSwitchCommand));
    }

    @PostMapping("/statusSwitch")
    @NoLoginRequired
    public Result<Void> statusSwitch(@RequestBody PunchConfigStatusSwitchCommand statusSwitchCommand) {
        punchConfigService.statusSwitch(statusSwitchCommand);
        return Result.ok();
    }

    @PostMapping("/listPunchList")
    @NoLoginRequired
    public Result<PaginationResult<PunchConfigPageDTO>> listPunchList(@RequestBody PunchConfigPageQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigList(query));
    }

    @PostMapping("/pagePunchConfigUserList")
    @NoLoginRequired
    public Result<PaginationResult<RuleConfigUserInfoDTO>> pagePunchConfigUserList(@RequestBody PunchConfigUserQuery query) {
        return Result.ok(punchConfigService.pagePunchConfigUserList(query));
    }

    /**
     * 查询打卡配置详情
     *
     * @param configNo 配置编号
     * @return Result<PunchConfigDetailDTO>
     */
    @GetMapping("/queryPunchConfigDetail")
    @NoLoginRequired
    public Result<PunchConfigDetailDTO> queryPunchConfigDetail(String configNo) {
        return Result.ok(punchConfigService.queryPunchConfigDetail(configNo));
    }

    @PostMapping("/shift/page")
    @NoLoginRequired
    public Result<PaginationResult<UserShiftConfigDTO>> page(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(userShiftService.page(query));
    }

    /**
     * 自动排班
     *
     * @param userAutoShiftParam 自动排班参数
     * @return 操作结果
     */
    @PostMapping("/shift/autoShift")
    @NoLoginRequired
    public Result<Void> autoShift(@RequestBody UserAutoShiftParam userAutoShiftParam) {
        autoShiftConfigFactory.autoShift(userAutoShiftParam);
        return Result.ok();
    }

    @PostMapping("/gps/export")
    @NoLoginRequired
    public Result<PaginationResult<AttendanceGpsConfigExportDTO>> gpsExport(@RequestBody AttendanceGpsConfigQuery query) {
        PaginationResult<AttendanceGpsConfigExportDTO> export = attendanceGpsConfigService.export(query);
        return Result.ok(export);
    }

    @PostMapping("/wifi/export")
    @NoLoginRequired
    public Result<PaginationResult<AttendanceWifiConfigExportDTO>> wifiExport(@RequestBody AttendanceWifiConfigQuery query) {
        PaginationResult<AttendanceWifiConfigExportDTO> export = attendanceWifiConfigService.export(query);
        return Result.ok(export);
    }

    @GetMapping("/mqRetryByMsgId")
    @NoLoginRequired
    public void mqRetryByMsgId(String msgId) {
        mqFailRecordService.mqRetryByMsgId(msgId);
    }

    @PostMapping("/wifi/import")
    @NoLoginRequired
    public Result<List<AttendanceWifiConfigImportDTO>> wifiImport(@RequestBody AttendanceWifiConfigImportDTO param) {
        List<AttendanceWifiConfigImportDTO> resultList = attendanceWifiConfigService.importWifiConfig(Arrays.asList(param));
        return Result.ok(resultList);
    }

    /**
     * 导出用户班次配置标题
     *
     * @param query 用户班次配置查询条件
     * @return 导出结果
     */

    @PostMapping("/shift/titleExport")
    @NoLoginRequired
    public Result<List<ExcelTitleExportDTO>> titleExport(@RequestBody UserShiftConfigQuery query) {
        return Result.ok(userShiftService.titleExport(query));
    }

    /**
     * 查询规则配置下拉选择
     *
     * @param query 规则配置下拉选择查询条件
     * @return 规则配置下拉选择结果
     */
    @PostMapping("/queryRuleConfigSelect")
    @NoLoginRequired
    public Result<List<RuleConfigSelectDTO>> queryRuleConfigSelect(@RequestBody RuleConfigSelectQuery query) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigSelect(query, true, false));
    }

    /**
     * 查询规则配置用户ID列表
     *
     * @param ruleConfigSelectDTO 规则配置下拉选择DTO
     * @return 规则配置用户ID列表
     */
    @PostMapping("/queryRuleConfigUsers")
    @NoLoginRequired
    public Result<List<Long>> queryRuleConfigUsers(@RequestBody RuleConfigSelectDTO ruleConfigSelectDTO) {
        return Result.ok(ruleConfigQueryService.queryRuleConfigUsers(ruleConfigSelectDTO));
    }

    @GetMapping("/queryUserPunchConfig")
    @NoLoginRequired
    public Result<Map<Long, PunchConfigDO>> queryUserPunchConfig(@RequestParam List<Long> userIds) {
        return Result.ok(punchConfigManage.getConfigMapByUserIdList(userIds));
    }

    @GetMapping("/queryUserReissueCardConfig")
    @NoLoginRequired
    public Result<Map<Long, ReissueCardConfigDO>> queryUserReissueCardConfig(@RequestParam List<Long> userIds) {
        return Result.ok(reissueCardConfigManage.getConfigMapByUserIdList(userIds));
    }

    /**
     * 测试循环排班自动续期
     * <p>
     * 该方法用于测试循环排班自动续期功能。如果不提供日期参数，将使用当前日期。
     * 方法会计算当前日期的dayId，并调用循环排班自动续期方法。
     * </p>
     *
     * @param dateStr 可选的日期参数，格式为yyyy-MM-dd
     * @return 操作结果
     */
    @GetMapping("/cycleShiftAutoRenewal")
    @NoLoginRequired
    public Result<Void> cycleShiftAutoRenewal(@RequestParam(required = false) String dateStr) {
        Date date;
        if (StringUtils.isNotBlank(dateStr)) {
            try {
                date = DateHelper.parseYYYYMMDD(dateStr);
            } catch (Exception e) {
                date = new Date();
            }
        } else {
            date = new Date();
        }
        Long dayId = DateHelper.getDayId(date);
        cycleShiftConfigFactory.cycleShiftAutoRenewal(date, dayId);
        return Result.ok();
    }

    /**
     * 测试固定班次自动排班续期
     * <p>
     * 该方法用于测试固定班次自动排班续期功能。可以指定国家列表、用户编码、开始时间和结束时间等参数。
     * 如果不提供时间参数，将使用当前日期作为开始时间，并自动计算结束时间。
     * </p>
     *
     * @param countryList  国家列表，逗号分隔（可选）
     * @param userCodes    用户编码列表，逗号分隔（可选）
     * @param startTimeStr 开始时间，格式为yyyy-MM-dd（可选）
     * @param endTimeStr   结束时间，格式为yyyy-MM-dd（可选）
     * @param pageSize     分页大小（可选，默认为500）
     * @return 操作结果
     */
    @GetMapping("/fixedClassAutoRenewal")
    @NoLoginRequired
    public Result<Void> fixedClassAutoRenewal(
            @RequestParam(required = false) String countryList,
            @RequestParam(required = false) String userCodes,
            @RequestParam(required = false) String startTimeStr,
            @RequestParam(required = false) String endTimeStr,
            @RequestParam(required = false) Integer pageSize) {

        // 创建参数对象
        FixedClassAutoRenewalParam param = new FixedClassAutoRenewalParam();
        param.setCountryList(countryList);
        param.setUserCodes(userCodes);

        // 处理开始时间
        if (StringUtils.isNotBlank(startTimeStr)) {
            try {
                Date startTime = DateHelper.parseYYYYMMDD(startTimeStr);
                param.setStartTime(startTime);
            } catch (Exception e) {
                log.info("解析开始时间失败: {}", startTimeStr, e);
            }
        }

        // 处理结束时间
        if (StringUtils.isNotBlank(endTimeStr)) {
            try {
                Date endTime = DateHelper.parseYYYYMMDD(endTimeStr);
                param.setEndTime(endTime);
            } catch (Exception e) {
                log.info("解析结束时间失败: {}", endTimeStr, e);
            }
        }

        // 设置分页大小
        if (pageSize != null && pageSize > 0) {
            param.setPageSize(pageSize);
        }

        // 调用固定班次自动排班续期方法
        autoShiftConfigFactory.fixedClassAutoRenewal(param);

        return Result.ok();
    }


    @PostMapping("/shift/import")
    @NoLoginRequired
    public Result<List<UserShiftImportDTO>> shiftImport(@RequestBody List<UserShiftImportDTO> importList) {
//        List<UserShiftImportDTO> importList = JSON.parseArray(jsonStr, UserShiftImportDTO.class);
        if (CollectionUtils.isEmpty(importList)) {
            return Result.ok(Collections.emptyList());
        }
        List<UserShiftImportDTO> failImportList = userShiftService.shiftImport(importList);
        return Result.ok(failImportList);
    }

    @PostMapping("/userLeaveCarryOverHandler")
    @NoLoginRequired
    public Result<ReturnT<String>> userLeaveCarryOverHandler(@RequestBody UserLeaveCarryOverParam content) {
        return Result.ok(userLeaveCarryOverHandler.userLeaveCarryOverHandler(JSON.toJSONString(content)));
    }

    @PostMapping("/userLeaveInvalidHandler")
    @NoLoginRequired
    public Result<ReturnT<String>> userLeaveInvalidHandler(@RequestBody UserLeaveInvalidParam content) {
        return Result.ok(userLeaveInvalidHandler.userLeaveInvalidHandler(JSON.toJSONString(content)));
    }

    @PostMapping("/user/leave/import")
    @NoLoginRequired
    public Result<List<UserLeaveBalanceImportDTO>> userLeaveInvalidHandler(@RequestBody UserLeaveBalanceImportDTO importDTO) {
        return Result.ok(userLeaveIpepService.leaveBalanceImport(Arrays.asList(importDTO)));
    }

    @GetMapping("/attendanceDayGenerateHandler")
    @NoLoginRequired
    public Result<Void> attendanceDayGenerateHandler(String param) {
        attendanceDayGenerateHandler.attendanceDayGenerateHandler(param);
        return Result.ok();
    }

    @PostMapping("/leave/export")
    @NoLoginRequired
    public Result<PaginationResult<FormInfoExportVO>> leaveExport(@RequestBody AttendanceApprovalInfoParam query) {
        PaginationResult<FormInfoExportVO> export = commonFormOperationService.listExport(query);
        return Result.ok(export);
    }

    @PostMapping("/overTime/export")
    @NoLoginRequired
    public Result<PaginationResult<OverTimeApprovalFormExportVO>> leaveExport(@RequestBody OverTimeListParam param) {
        PaginationResult<OverTimeApprovalFormExportVO> export = overtimeApprovalService.listExport(param);
        return Result.ok(export);
    }

    /**
     * 初始化国家下所有用户的班次性质
     */
    @GetMapping("/initUserClassNatureByCountry")
    @NoLoginRequired
    public Result<Void> initUserClassNatureByCountry(String country) {
        userClassNatureService.initUserClassNatureByCountry(country);
        return Result.ok();
    }


    /**
     * 清空所有非灰度国家用户的班次性质
     */
    @GetMapping("/clearUserClassNatureForAllNoGrayscaleCountry")
    @NoLoginRequired
    public Result<Void> clearUserClassNatureForAllNoGrayscaleCountry() {
        userClassNatureService.clearUserClassNatureForAllNoGrayscaleCountry();
        return Result.ok();
    }

    /**
     * 获取中控考勤机访问token
     */
    @GetMapping("/getZktecoToken")
    public Result<String> getZktecoToken(ZktVersionEnum zktVersionEnum) {
        return Result.ok(thirdZktecoService.getZktecoToken(zktVersionEnum));
    }

    /**
     * 刷新班次适用范围时间戳
     */
    @GetMapping("/refreshPunchClassRangeTimestamp")
    public Result<Void> refreshPunchClassRangeTimestamp() {
        punchClassConfigApplicationService.refreshPunchClassRangeTimestamp();
        return Result.ok();
    }

    /**
     * 清空司机打卡记录表所有数据
     *
     * @return 操作结果
     */
    @GetMapping("/driver/clearAllPunchRecords")
    @NoLoginRequired
    public Result<Void> clearDriverPunchRecords() {
        try {
            driverDataManagementService.clearAllDriverPunchRecords();
            return Result.ok();
        } catch (Exception e) {
            log.error("清空司机打卡记录失败", e);
            return Result.getFailResult("CLEAR_DRIVER_PUNCH_RECORDS_FAILED", "清空司机打卡记录失败：" + e.getMessage());
        }
    }

    @PostMapping("/attendanceDayReportHandler")
    @NoLoginRequired
    public Result<ReturnT<String>> attendanceDayReportHandler(@RequestBody DayReportJobParam content) {
        return Result.ok(attendanceDayReportHandler.attendanceDayReportHandler(JSON.toJSONString(content)));
    }

    @PostMapping("/attendanceDayReportHistoryHandler")
    @NoLoginRequired
    public Result<ReturnT<String>> attendanceDayReportHistoryHandler(@RequestBody DayReportJobHistoryParam content) {
        return Result.ok(attendanceDayReportHistoryHandler.attendanceDayReportHistoryHandler(JSON.toJSONString(content)));
    }

    @PostMapping("/attendanceDayReportExport")
    @NoLoginRequired
    public Result<PaginationResult<UserDayReportExportVO>> attendanceDayReportExport(@RequestBody DayReportListQuery query) {
        return Result.ok(attendanceDayReportService.export(query));
    }

    @PostMapping("/attendanceDayGenerateSnapshotHandler")
    @NoLoginRequired
    public Result<ReturnT<String>> attendanceDayGenerateSnapshotHandler(@RequestBody AttendanceDayGenerateSnapshotHandler.AttendanceDayGenerateSnapshotHandlerParam content) {
        return Result.ok(attendanceDayGenerateSnapshotHandler.attendanceDayGenerateSnapshotHandler(JSON.toJSONString(content)));
    }

    /**
     * 验证指定用户是否启用新考勤系统
     *
     * @param userId 用户ID
     * @return 是否启用新考勤系统
     */
    @GetMapping("/verifyUserIsEnableNewAttendance")
    @NoLoginRequired
    public Result<Boolean> verifyUserIsEnableNewAttendance(Long userId) {
        return Result.ok(migrationService.verifyUserIsEnableNewAttendance(userId));
    }

    /**
     * 测试单个用户迁移验证API接口
     * 通过AttendanceMigrationApi验证单个用户是否启用新考勤系统
     * 用于测试RPC接口的完整调用链路和返回结果
     *
     * @param userId 用户ID
     * @return RPC调用结果，包含用户验证信息
     */
    @GetMapping("/testVerifyUserIsEnableNewAttendance")
    @NoLoginRequired
    public Result<RpcResult<UserMigrationVerifyResult>> testVerifyUserIsEnableNewAttendance(@RequestParam Long userId) {
        UserMigrationVerifyParam param = new UserMigrationVerifyParam();
        param.setUserId(userId);
        RpcResult<UserMigrationVerifyResult> result = attendanceMigrationApi.verifyUserIsEnableNewAttendance(param);
        return Result.ok(result);
    }

    /**
     * 测试批量用户迁移验证API接口
     * 通过AttendanceMigrationApi批量验证用户是否启用新考勤系统
     * 用于测试RPC接口的批量处理能力和统计结果
     *
     * @param userIds 用户ID列表
     * @return RPC调用结果，包含批量验证结果和统计信息
     */
    @PostMapping("/testVerifyUsersIsEnableNewAttendance")
    @NoLoginRequired
    public Result<RpcResult<BatchUserMigrationVerifyResult>> testVerifyUsersIsEnableNewAttendance(@RequestBody List<Long> userIds) {
        BatchUserMigrationVerifyParam param = new BatchUserMigrationVerifyParam();
        param.setUserIds(userIds);
        RpcResult<BatchUserMigrationVerifyResult> result = attendanceMigrationApi.verifyUsersIsEnableNewAttendance(param);
        return Result.ok(result);
    }

    /**
     * 测试批量更新HRMS迁移排班数据的排班类型
     *
     * @return 批量更新结果，包含详细的处理统计信息
     */
    @PostMapping("/testBatchUpdateShiftTypeForHrmsMigration")
    @NoLoginRequired
    public Result<BatchUpdateShiftTypeResult> testBatchUpdateShiftTypeForHrmsMigration() {
        log.info("开始测试批量更新HRMS迁移排班数据的排班类型");
        BatchUpdateShiftTypeResult result = userShiftConfigMigrationService.batchUpdateShiftTypeForHrmsMigration();
        log.info("测试批量更新HRMS迁移排班数据完成，结果: {}", result);
        return Result.ok(result);
    }

    /**
     * 测试根据用户ID列表迁移数据到新表
     *
     * @param userIds    用户ID列表
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果，true表示成功，false表示失败
     */
    @PostMapping("/testMigrateByUserIdsAndDateRange")
    @NoLoginRequired
    public Result<Boolean> testMigrateByUserIdsAndDateRange(@RequestBody List<Long> userIds, @RequestParam Long startDayId, @RequestParam Long endDayId) {
        CompletableFuture.runAsync(
                RunnableWrapper.of(
                        () -> {
                            log.info("开始测试根据用户ID列表迁移数据到新表, 用户数量: {}, dayId范围: {} - {}",
                                    userIds.size(), startDayId, endDayId);
                            Boolean result = userShiftConfigMigrationService.migrateByUserIdsAndDateRange(userIds, startDayId, endDayId);
                            log.info("测试根据用户ID列表迁移数据到新表完成, 结果: {}", result);
                        }
                )
        );
        return Result.ok();
    }

    /**
     * 测试根据国家和入职确认时间迁移数据到新表
     *
     * @param locationCountry 国家代码
     * @param confirmDayId    入职确认时间
     * @param shiftEndDayId   结束日期
     * @return 迁移结果，true表示成功，false表示失败
     */
    @PostMapping("/testMigrateByCountryAndConfirmDate")
    @NoLoginRequired
    public Result<Boolean> testMigrateByCountryAndConfirmDate(@RequestParam String locationCountry, @RequestParam Long confirmDayId, @RequestParam Long shiftEndDayId) {
        CompletableFuture.runAsync(
                RunnableWrapper.of(
                        () -> {
                            log.info("开始测试根据国家和入职确认时间迁移数据到新表, 国家: {}, 入职确认时间: {}, 结束日期: {}",
                                    locationCountry, confirmDayId, shiftEndDayId);
                            Boolean result = userShiftConfigMigrationService.migrateByCountryAndConfirmDate(locationCountry, confirmDayId, shiftEndDayId);
                            log.info("测试根据国家和入职确认时间迁移数据到新表完成, 结果: {}", result);
                        }
                )
        );
        return Result.ok();
    }

    /**
     * 删除用户周期补卡次数记录
     * 根据指定条件删除用户周期补卡次数数据
     * 删除条件：cycle_start_date >= 指定时间 AND cycle_end_date >= 指定时间 AND create_date = 指定时间
     *
     * @param deleteParam 删除参数，包含删除条件和确认标识
     * @return 删除操作结果，包含删除的记录数量和操作详情
     */
    @PostMapping("/deleteUserCycleReissueCardCount")
    @NoLoginRequired
    public Result<UserCycleReissueCardCountDeleteResult> deleteUserCycleReissueCardCount(
            @RequestBody @Validated UserCycleReissueCardCountDeleteParam deleteParam) {
        log.info("接收到删除用户周期补卡次数记录请求，参数：{}", deleteParam);

        try {
            UserCycleReissueCardCountDeleteResult result = userReissueCardCountService.deleteByCondition(deleteParam);

            if (result.getSuccess()) {
                log.info("删除操作成功完成，删除记录数量：{}", result.getDeletedCount());
            } else {
                log.warn("删除操作失败：{}", result.getMessage());
            }

            return Result.ok(result);
        } catch (Exception e) {
            log.error("删除用户周期补卡次数记录接口异常", e);
            UserCycleReissueCardCountDeleteResult errorResult = UserCycleReissueCardCountDeleteResult.failure("系统异常：" + e.getMessage());
            return Result.ok(errorResult);
        }
    }

    /**
     * 测试考勤月报API接口
     *
     * @param startTimeStr 开始时间，格式：yyyy-MM-dd
     * @param endTimeStr   结束时间，格式：yyyy-MM-dd
     * @param userCodes    用户编码列表
     * @return RPC调用结果，包含月报数据
     */
    @GetMapping("/testAttendanceMonthReportApi")
    @NoLoginRequired
    public Result<RpcResult<List<Map<String, String>>>> testAttendanceMonthReportApi(
            @RequestParam(required = true) String startTimeStr,
            @RequestParam(required = true) String endTimeStr,
            @RequestParam(required = true) String userCodes) {
        try {
            Date startTime = DateHelper.parseYYYYMMDD(startTimeStr);
            Date endTime = DateHelper.parseYYYYMMDD(endTimeStr);
            if (startTime.after(endTime)) {
                log.warn("开始时间不能大于结束时间: startTime={}, endTime={}",
                        DateHelper.formatYYYYMMDD(startTime), DateHelper.formatYYYYMMDD(endTime));
                return Result.fail("开始时间不能大于结束时间");
            }
            List<String> userCodeList = Arrays.asList(userCodes.split(","));

            AttendanceMonthReportParam param = new AttendanceMonthReportParam();
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setUserCodeList(userCodeList);

            log.info("调用考勤月报API，参数：startTime={}, endTime={}, userCodeList={}",
                    DateHelper.formatYYYYMMDD(startTime),
                    DateHelper.formatYYYYMMDD(endTime),
                    userCodeList);
            RpcResult<List<Map<String, String>>> rpcResult = attendanceReportApi.selectAttendanceMonthReport(param);

            // 记录结果日志
            if (rpcResult.isSuccess()) {
                List<Map<String, String>> resultData = rpcResult.getResult();
                log.info("考勤月报API调用成功，返回数据条数：{}",
                        resultData != null ? resultData.size() : 0);
                if (resultData != null && !resultData.isEmpty()) {
                    log.info("第一条数据示例：{}", resultData.get(0));
                }
            } else {
                log.error("考勤月报API调用失败:{}", rpcResult.getMessage());
            }
            return Result.ok(rpcResult);
        } catch (Exception e) {
            log.error("测试考勤月报API接口异常", e);
            return Result.fail("系统异常：" + e.getMessage());
        }
    }


    @GetMapping("/userDimissionCleanShift")
    @NoLoginRequired
    public Result<Void> userDimissionCleanShift(Long userId, Long dayId) {
        if (Objects.isNull(userId) || Objects.isNull(dayId)) {
            return Result.ok();
        }
        userShiftService.userDimissionCleanShift(userId, DateHelper.transferDayIdToDate(dayId));
        return Result.ok();
    }

    @PostMapping("/syncEmployeeAttendanceHandler")
    @NoLoginRequired
    @NoLoginAuthRequired
    public Result<Boolean> syncEmployeeAttendanceHandler(@RequestBody JSONObject param) {
        String content = ObjectUtil.isNotNull(param) ? JSON.toJSONString(param) : "";
        ReturnT<String> xxlJobReturn = syncEmployeeAttendanceHandler.syncEmployeeAttendanceHandler(content);
        return ObjectUtil.isNotNull(xxlJobReturn) && ObjectUtil.equal(xxlJobReturn.getCode(), ReturnT.SUCCESS_CODE) ? Result.ok(true) : Result.ok(false);
    }

    /**
     * 刷新指定国家用户的班次性质
     *
     * @param request 请求参数，包含国家代码、目标班次性质和配置用户编码列表
     * @return 操作结果
     */
    @PostMapping("/refreshUserClassNatureByCountry")
    @NoLoginRequired
    public Result<Boolean> refreshUserClassNatureByCountry(@RequestBody @Valid RefreshUserClassNatureByCountryRequest request) {

        log.info("刷新用户班次性质 | 接收到请求 request:{}", request);

        try {
            Boolean result = userClassNatureService.refreshUserClassNatureByCountry(
                    request.getCountryCode(),
                    request.getTargetClassNature(),
                    request.getConfigUserCodes());

            if (Boolean.TRUE.equals(result)) {
                log.info("刷新用户班次性质 | 处理成功 request:{}", request);
                return Result.ok(true);
            } else {
                log.warn("刷新用户班次性质 | 处理失败 request:{}", request);
                return Result.ok(false);
            }

        } catch (Exception e) {
            log.error("刷新用户班次性质异常 request:{}", request, e);
            return Result.fail("刷新用户班次性质异常: " + e.getMessage());
        }
    }

//    @PostMapping("/abnormalAttendanceDayRemindHandler")
//    @NoLoginRequired
//    @NoLoginAuthRequired
//    public void abnormalAttendanceDayRemindHandler(@RequestBody JSONObject param) {
//        String content = ObjectUtil.isNotNull(param) ? JSON.toJSONString(param) : "";
//        abnormalAttendanceDayRemindHandler.abnormalAttendanceDayReminderHandler(content);
//    }

    /**
     * 测试考勤周期日历数据自动续期功能
     * <p>
     * 该方法用于在开发和测试阶段手动触发考勤周期日历数据自动续期任务，验证功能是否正常工作。
     * 支持通过HTTP请求传递JSON格式的任务参数，包括续期阈值、数据窗口年数、国家列表、试运行模式等。
     * </p>
     *
     * @param param 任务参数，包含以下字段：
     *              - renewalThresholdMonths: 续期阈值（月），当剩余有效期少于此值时触发续期，默认6个月
     *              - dataWindowYears: 数据窗口年数，保持多少年的数据，默认3年
     *              - countryList: 指定处理的国家列表，为空则处理所有启用的国家
     *              - dryRun: 是否为试运行模式，试运行模式只检查不执行实际续期操作，默认false
     *              - batchSize: 批处理大小，默认使用系统配置的批处理大小
     * @return 执行结果，包含统计信息和执行状态
     */
    @PostMapping("/attendanceCycleCalendarRenewal")
    @NoLoginRequired
    public Result<ReturnT<String>> attendanceCycleCalendarRenewal(@RequestBody(required = false) JSONObject param) {
        try {
            log.info("开始执行考勤周期日历数据自动续期测试接口，参数：{}", param);

            // 将参数转换为JSON字符串
            String content = ObjectUtil.isNotNull(param) ? JSON.toJSONString(param) : "";

            // 调用实际的续期处理方法
            ReturnT<String> result = attendanceCycleCalendarRenewalHandler.attendanceCycleCalendarRenewalHandler(content);

            log.info("考勤周期日历数据自动续期测试接口执行完成，结果：{}", result);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("执行考勤周期日历数据自动续期测试接口时发生异常", e);
            return Result.fail("执行考勤周期日历数据自动续期测试接口时发生异常: " + e.getMessage());
        }
    }
}
