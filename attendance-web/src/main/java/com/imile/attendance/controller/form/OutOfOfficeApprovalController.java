package com.imile.attendance.controller.form;

import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.outOffice.OutOfOfficeApprovalService;
import com.imile.attendance.form.biz.outOffice.param.OutOfOfficeAddParam;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: HR考勤审批所有入口
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/attendance/approval")
public class OutOfOfficeApprovalController extends BaseController {

    @Resource
    private OutOfOfficeApprovalService outOfOfficeApprovalService;
    @Resource
    private ImileRedisClient redissonClient;

    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_SYNC:";

    /**
     * 外勤申请(点击新增按钮调用 新增/暂存)
     */
    @PostMapping("/out/of/office/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> outOfOfficeAdd(@RequestBody @Validated OutOfOfficeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = outOfOfficeApprovalService.outOfOfficeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 外勤申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/out/of/office/update")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> outOfOfficeUpdate(@RequestBody @Validated OutOfOfficeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = outOfOfficeApprovalService.outOfOfficeUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 外勤-撤销申请
     */
    @PostMapping("/out/of/office/revoke/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> outOfOfficeRevokeAdd(@RequestBody @Validated RevokeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = outOfOfficeApprovalService.outOfOfficeRevokeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 外勤申请预览
     */
    @PostMapping("/out/of/office/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> outOfOfficePreview(@RequestBody @Validated OutOfOfficeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = outOfOfficeApprovalService.outOfOfficePreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 外勤-撤销申请预览
     */
    @PostMapping("/out/of/office/revoke/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> outOfOfficeRevokePreview(@RequestBody @Validated RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = outOfOfficeApprovalService.outOfOfficeRevokePreview(param);
        return Result.ok(stepRecordDTOS);
    }

//    /**
//     * 外勤单据信息导出
//     */
//    @PostMapping("/export")
//    public Result<PaginationResult<LeaveFormInfoExportVO>> listExport(HttpServletRequest request, AttendanceApprovalInfoParam param) {
//        setExcelCallBackParam(request, param);
//        PaginationResult<LeaveFormInfoExportVO> res = outOfOfficeApprovalService.listExport(param);
//        return Result.ok(res);
//    }
}
