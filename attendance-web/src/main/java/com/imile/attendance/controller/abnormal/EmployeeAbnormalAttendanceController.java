package com.imile.attendance.controller.abnormal;

import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.dto.AbnormalAttendanceCountDTO;
import com.imile.attendance.abnormal.dto.EmployeeAbnormalAttendanceQueryDTO;
import com.imile.attendance.abnormal.param.AbnormalAttendanceBatchUpdateParam;
import com.imile.attendance.abnormal.param.AbnormalAttendanceSingleUpdateParam;
import com.imile.attendance.abnormal.param.AttendanceAbnormalDetailParam;
import com.imile.attendance.abnormal.param.DayAttendanceTimeParam;
import com.imile.attendance.abnormal.param.EmployeeAbnormalAttendanceParam;
import com.imile.attendance.abnormal.vo.AbnormalAttendanceCountVO;
import com.imile.attendance.abnormal.vo.AbnormalDetailVO;
import com.imile.attendance.abnormal.vo.DayAttendanceTimeVO;
import com.imile.attendance.abnormal.vo.EmployeeAbnormalAttendanceVO;
import com.imile.attendance.annon.ExportParamFill;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.warehouse.vo.WarehouseAbnormalDetailVO;
import com.imile.common.page.PaginationResult;
import com.imile.common.result.Result;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;


/**
 * 员工异常考勤服务
 *
 * <AUTHOR>
 * @since 2025/6/3
 */
@Slf4j
@RequestMapping("/abnormal/attendance")
@RestController
public class EmployeeAbnormalAttendanceController extends BaseController {

    @Resource
    private EmployeeAbnormalAttendanceService abnormalAttendanceService;
    @Resource
    private ConverterService converterService;


    /**
     * 异常列表
     */
    @PostMapping("/list")
    public Result<PaginationResult<EmployeeAbnormalAttendanceVO>> page(@RequestBody @Validated EmployeeAbnormalAttendanceParam param) {
        EmployeeAbnormalAttendanceQueryDTO queryDTO = BeanUtils.convert(param, EmployeeAbnormalAttendanceQueryDTO.class);
        PaginationResult<EmployeeAbnormalAttendanceDTO> result = abnormalAttendanceService.page(queryDTO);
        PaginationResult<EmployeeAbnormalAttendanceVO> resultVO = this.convertPage(result, EmployeeAbnormalAttendanceVO.class);
        // 返回时区处理
        converterService.withAnnotation(resultVO.getResults());
        return Result.ok(resultVO);
    }

    /**
     * 异常考勤导出功能
     *
     * @param param 查询参数
     */
    @ExportParamFill
    @PostMapping("/list/export")
    public Result<PaginationResult<EmployeeAbnormalAttendanceVO>> export(EmployeeAbnormalAttendanceParam param) {
        EmployeeAbnormalAttendanceQueryDTO queryDTO = BeanUtils.convert(param, EmployeeAbnormalAttendanceQueryDTO.class);
        PaginationResult<EmployeeAbnormalAttendanceDTO> result = abnormalAttendanceService.page(queryDTO);
        PaginationResult<EmployeeAbnormalAttendanceVO> resultVO = this.convertPage(result, EmployeeAbnormalAttendanceVO.class);
        // 返回时区处理
        converterService.withAnnotation(resultVO.getResults());
        return Result.ok(resultVO);
    }

    /**
     * 异常明细
     */
    @PostMapping("/detail")
    public Result<AbnormalDetailVO> abnormalDetail(@RequestBody @Validated AttendanceAbnormalDetailParam param) {
        AbnormalDetailVO abnormalDetailVO = abnormalAttendanceService.getAbnormalDetail(param.getAbnormalId());
        converterService.withAnnotationForSingle(abnormalDetailVO.getEmployeeBaseInfo());
        converterService.withAnnotationForSingle(abnormalDetailVO.getAttendanceAbnormalInfo());
        converterService.withAnnotationForSingle(abnormalDetailVO.getAttendanceRuleConfigInfo());
        return Result.ok(abnormalDetailVO);
    }

    /**
     * 仓内异常明细
     */
    @GetMapping("/warehouse/abnormal/detail")
    public Result<WarehouseAbnormalDetailVO> warehouseAbnormalDetail(@NotBlank @RequestParam("abnormalId") Long abnormalId) {
        WarehouseAbnormalDetailVO abnormalDetailVO = abnormalAttendanceService.getWarehouseAbnormalDetail(abnormalId);
        converterService.withAnnotationForSingle(abnormalDetailVO);
        if (CollectionUtils.isNotEmpty(abnormalDetailVO.getWarehouseRecordList())){
            converterService.withAnnotation(abnormalDetailVO.getWarehouseRecordList());
        }
        return Result.ok(abnormalDetailVO);
    }

    /**
     * 异常数量统计
     */
    @PostMapping("/selectCount")
    public Result<AbnormalAttendanceCountVO> selectCount(@RequestBody EmployeeAbnormalAttendanceParam param) {
        EmployeeAbnormalAttendanceQueryDTO queryDTO = BeanUtils.convert(param, EmployeeAbnormalAttendanceQueryDTO.class);
        AbnormalAttendanceCountDTO countDTO = abnormalAttendanceService.selectCount(queryDTO);
        return Result.ok(BeanUtils.convert(countDTO, AbnormalAttendanceCountVO.class));
    }

    /**
     * 异常考勤处理-单个
     */
    @PostMapping("/single/updateAbnormalAttendance")
    public Result<Boolean> singleUpdateAbnormalAttendance(@RequestBody @Validated AbnormalAttendanceSingleUpdateParam param) {
        abnormalAttendanceService.singleUpdateAbnormalAttendance(param);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 异常考勤处理-批量
     */
    @PostMapping("/batch/updateAbnormalAttendance")
    public Result<Boolean> batchUpdateAbnormalAttendance(@RequestBody @Valid AbnormalAttendanceBatchUpdateParam param) {
        abnormalAttendanceService.batchUpdateAbnormalAttendance(param);
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 未排班异常-获取用户当天可输入的应出勤时间最大值
     */
    @PostMapping("/day/attendanceHours")
    public Result<DayAttendanceTimeVO> getDayAttendanceHours(@RequestBody @Validated DayAttendanceTimeParam param) {
        DayAttendanceTimeVO dayAttendanceTimeVO = abnormalAttendanceService.getDayAttendanceHours(param);
        return Result.ok(dayAttendanceTimeVO);
    }

    /**
     * 仓内异常分页
     */
    @PostMapping("/warehouse/list")
    public Result<PaginationResult<EmployeeAbnormalAttendanceVO>> warehouseList(@RequestBody EmployeeAbnormalAttendanceParam param) {
        EmployeeAbnormalAttendanceQueryDTO queryDTO = BeanUtils.convert(param, EmployeeAbnormalAttendanceQueryDTO.class);
        PaginationResult<EmployeeAbnormalAttendanceDTO> result = abnormalAttendanceService.page(queryDTO);
        PaginationResult<EmployeeAbnormalAttendanceVO> resultVO = this.convertPage(result, EmployeeAbnormalAttendanceVO.class);
        converterService.withAnnotation(resultVO.getResults());
        return Result.ok(resultVO);
    }
}
