package com.imile.attendance.controller.form;

import com.google.common.base.Throwables;
import com.imile.attendance.annon.NoAttendanceLoginAuthRequired;
import com.imile.attendance.annon.NoLoginAuthRequired;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.controller.BaseController;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.biz.reissueCard.ReissueCardApprovalService;
import com.imile.attendance.form.biz.reissueCard.param.AddDurationParam;
import com.imile.attendance.form.biz.reissueCard.param.ReissueCardAddParam;
import com.imile.attendance.form.param.RevokeAddParam;
import com.imile.attendance.form.biz.reissueCard.param.UserDayReissueInfoParam;
import com.imile.attendance.form.biz.reissueCard.vo.UserDayReissueInfoVO;
import com.imile.attendance.form.dto.ApprovalDetailStepRecordDTO;
import com.imile.attendance.form.vo.ApprovalResultVO;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.exception.BusinessException;
import com.imile.common.result.Result;
import com.imile.framework.redis.client.ImileRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: HR考勤审批所有入口
 * @author: taokang
 * @createDate: 2023-6-21
 * @version: 1.0
 */
@Slf4j
@RestController
@RequestMapping("/attendance/approval")
public class ReissueApprovalController extends BaseController {
    @Resource
    private ReissueCardApprovalService reissueCardApprovalService;
    @Resource
    private ImileRedisClient redissonClient;

    private static final String SYNC_LOCK = "HRMS:LOCK:ATTENDANCE_SYNC:";

    /**
     * 获取用户指定日期的可补卡异常及当天排班信息
     */
    @PostMapping("/user/day/reissue/info")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<UserDayReissueInfoVO> getUserDayReissueInfo(@RequestBody @Validated UserDayReissueInfoParam param) {
        UserDayReissueInfoVO resultVO = reissueCardApprovalService.getUserDayReissueInfo(param);
        return Result.ok(resultVO);
    }

    /**
     * 补卡申请(点击新增按钮调用 新增/暂存)
     * //todo 补卡申请，还缺一个用户指定日期的补卡异常    每次提交的一刻，就需要减少一次补卡次数，审批通过不用减，取消，驳回需要加回去
     */
    @PostMapping("/reissue/card/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> reissueCardAdd(@RequestBody @Validated ReissueCardAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = reissueCardApprovalService.reissueCardAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 补时长申请
     */
    @PostMapping("/add/duration")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> addDuration(@RequestBody @Validated AddDurationParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + ":addDuration:" + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = reissueCardApprovalService.addDuration(param);
        } catch (BusinessException e) {
            log.error("fail:{}", Throwables.getStackTraceAsString(e));
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", Throwables.getStackTraceAsString(e));
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 补卡申请(暂存后更新/驳回后更新)
     */
    @PostMapping("/reissue/card/update")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> reissueCardUpdate(@RequestBody @Validated ReissueCardAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = reissueCardApprovalService.reissueCardUpdate(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 补卡-撤销申请
     */
    @PostMapping("/reissue/card/revoke/add")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<ApprovalResultVO> reissueCardRevokeAdd(@RequestBody @Validated RevokeAddParam param) {
        //分布式锁
        String lockString = SYNC_LOCK + RequestInfoHolder.getUserCode();
        ApprovalResultVO resultVO;
        try {
            //上锁，时间为5秒
            boolean tryLock = redissonClient.tryLock(lockString, 5);
            BusinessLogicException.checkTrue(!tryLock, ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getCode(), ErrorCodeEnum.ACCOUNT_REPEAT_HANDLER_ERROR.getDesc());
            resultVO = reissueCardApprovalService.reissueCardRevokeAdd(param);
        } catch (BusinessException e) {
            log.error("fail:{}", e);
            throw e;
        } catch (Exception e) {
            log.error("fail:{}", e);
            throw BusinessException.get(MsgCodeConstant.SYSTEM_ERROR);
        } finally {
            redissonClient.unlock(lockString);
        }
        return Result.ok(resultVO);
    }

    /**
     * 补卡申请预览
     */
    @PostMapping("/reissue/card/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> reissueCardPreview(@RequestBody @Validated ReissueCardAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = reissueCardApprovalService.reissueCardPreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 补时长申请预览
     */
    @PostMapping("/add/duration/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> addDurationPreview(@RequestBody @Validated AddDurationParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = reissueCardApprovalService.addDurationPreview(param);
        return Result.ok(stepRecordDTOS);
    }

    /**
     * 补卡-撤销申请预览
     */
    @PostMapping("/reissue/card/revoke/preview")
    @NoLoginAuthRequired
    @NoAttendanceLoginAuthRequired
    public Result<List<ApprovalDetailStepRecordDTO>> reissueCardRevokePreview(@RequestBody @Validated RevokeAddParam param) {
        List<ApprovalDetailStepRecordDTO> stepRecordDTOS = reissueCardApprovalService.reissueCardRevokePreview(param);
        return Result.ok(stepRecordDTOS);
    }
}
