package com.imile.attendance.shift;

import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description 员工排班
 */
public interface UserShiftConfigManage {

    /**
     * 批量获取用户的排班记录
     */
    List<UserShiftConfigDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList);

    /**
     * 获取用户规定时间段内的排班规则
     */
    List<UserShiftConfigDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId);

    /**
     * 获取用户指定班次规定时间段内的排班规则
     */
    List<UserShiftConfigDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId, Long startDayId,
                                                               Long endDayId);

    /**
     * 批量获取对应班次和考勤日的排班记录
     */
    List<UserShiftConfigDO> selectRecordByClassIdAndDayId(List<Long> classIdList, Long dayId);

    /**
     * 批量排班保存或更新
     */
    void batchShiftUpdateOrAdd(List<UserShiftConfigDO> oldUserShiftConfigDOList,
                               List<UserShiftConfigDO> newUserShiftConfigDOList);

    /**
     * 逻辑删除用户指定时间后的所有排班记录
     */
    void updateToOld(Long userId, Date date);

    /**
     * 循环排班保存
     */
    void cycleShiftSave(List<UserShiftConfigDO> oldUserShiftConfigDOList,
                        List<UserShiftConfigDO> newUserShiftConfigDOList,
                        List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList,
                        List<UserCycleShiftConfigDO> addCycleShiftConfigDOList);

    /**
     * 取消循环排班
     */
    void cancelCycleShift(List<UserCycleShiftConfigDO> updateCycleShiftConfigDOList,
                          List<UserShiftConfigDO> oldUserClassConfigDOList);

    /**
     * 获取指定班次和日期的用户ID列表
     *
     * @param punchClassId 班次ID
     * @param dayId        日期ID (格式：yyyyMMdd)
     * @return 用户ID列表
     */
    List<Long> getUserIdsByPunchClassIdAndDayId(Long punchClassId, Long dayId);

    /**
     * 获取指定班次在某日期之后的所有用户ID列表
     *
     * @param punchClassId 班次ID
     * @param dayId        日期ID (格式：yyyyMMdd)，获取该日期之后的记录
     * @return 用户ID列表
     */
    List<Long> getUserIdsByPunchClassIdAndAfterDayId(Long punchClassId, Long dayId);

    /**
     * 获取指定用户在某日期的所有排班配置
     *
     * @param userIds 用户Ids
     * @param dayId   日期ID (格式：yyyyMMdd)，获取该日期的记录
     * @return 用户排班配置列表
     */
    Map<Long, List<UserShiftConfigDO>> getConfigByUserIdsAndDayId(List<Long> userIds, Long dayId);

    /**
     * 获取指定用户在某日期（含）之后的所有排班配置
     *
     * @param userIds 用户Ids
     * @param dayId   日期ID (格式：yyyyMMdd)，获取该日期（含）之后的记录
     * @return 用户排班配置列表
     */
    Map<Long, List<UserShiftConfigDO>> getShiftConfigByUserIdsAndDayIdIncludeAfter(List<Long> userIds, Long dayId);

    /**
     * 获取指定用户,指定班次在某日期（含）之后的所有排班配置
     *
     * @param userIds 用户Ids
     * @param classIds 班次Ids
     * @param dayId   日期ID (格式：yyyyMMdd)，获取该日期（含）之后的记录
     * @return 用户排班配置列表
     */
    Map<Long, List<UserShiftConfigDO>> getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(List<Long> userIds, List<Long> classIds, Long dayId);

    /**
     * 获取指定用户在某日期（含）之后的所有排班配置
     *
     * @param userIds 用户Ids
     * @param dayId   日期ID (格式：yyyyMMdd)，获取该日期（含）之后的记录
     * @return 用户排班配置列表
     */
    Map<Long, List<UserShiftConfigDO>> getConfigByUserIdsAndDayIdIncludeAfter(List<Long> userIds, Long dayId);

    /**
     * 获取指定用户,指定班次在某日期（含）之后的所有排班配置
     *
     * @param userIds 用户Ids
     * @param dayId   日期ID (格式：yyyyMMdd)，获取该日期（含）之后的记录
     * @return 用户排班配置列表
     */
    Map<Long, List<UserShiftConfigDO>> getAutoShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(List<Long> userIds, List<Long> classIds, Long dayId);

    /**
     * 获取用户排班分页信息（用于排班分页）
     *
     * @param startDayId  开始日期ID
     * @param endDayId    结束日期ID
     * @param shiftStatus 排班状态
     * @return 用户排班分页信息
     */
    Map<Long, List<UserShiftConfigDO>> queryForUserShiftPage(Long startDayId, Long endDayId, String shiftStatus);

    List<Long> getAllHasShiftedUsersInDayRange(Long startDayId, Long endDayId);


    /**
     * 查找用户在时间范围内的排班信息
     */
    List<UserShiftConfigDO> selectUserShift(Long userId, Long startDayId, Long endDayId);

    /**
     * 获取用户在指定天数的排班数据
     */
    List<UserShiftConfigDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds);
}
