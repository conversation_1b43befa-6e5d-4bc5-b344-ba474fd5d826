package com.imile.attendance.form;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailAbnormalDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseRecordDO;

import java.util.List;

public interface AttendanceApprovalManage {

    /**
     * 考勤单据新增（聚合）
     * @param formDO
     * @param relationDOS
     * @param formAttrArrayList
     * @param employeeAbnormalOperationRecord
     * @param abnormalAttendance
     * @param userLeaveStageDetailList
     * @param userLeaveRecord
     */
    void formAdd(AttendanceFormDO formDO,
                 List<AttendanceFormRelationDO> relationDOS,
                 List<AttendanceFormAttrDO> formAttrArrayList,
                 EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecord,
                 EmployeeAbnormalAttendanceDO abnormalAttendance,
                 List<UserLeaveStageDetailDO> userLeaveStageDetailList,
                 UserLeaveRecordDO userLeaveRecord);

    /**
     * 考勤单据修改（聚合）
     * @param formDO
     * @param relationDOS
     * @param formAttrArrayList
     */
    void formUpdate(AttendanceFormDO formDO,
                    List<AttendanceFormRelationDO> relationDOS,
                    List<AttendanceFormAttrDO> formAttrArrayList);

    /**
     * 考勤单据取消（聚合）
     * @param formDO
     * @param abnormalAttendanceDO
     * @param userCardConfigDO
     * @param userLeaveStageDetailInfoList
     * @param userLeaveRecord
     */
    void cancel(AttendanceFormDO formDO,
                EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                UserCycleReissueCardCountDO userCardConfigDO,
                List<UserLeaveStageDetailDO> userLeaveStageDetailInfoList,
                UserLeaveRecordDO userLeaveRecord);

    /**
     * 考勤单据删除（聚合）
     * @param formDO
     * @param relationDOS
     * @param attrDOS
     */
    void delete(AttendanceFormDO formDO,
                List<AttendanceFormRelationDO> relationDOS,
                List<AttendanceFormAttrDO> attrDOS);

    /**
     * 考勤补卡单据新增（聚合）
     * @param formDO
     * @param relationDOS
     * @param applicationFormAttrArrayList
     * @param employeeAbnormalOperationRecordDO
     * @param userCardConfigDO
     * @param abnormalAttendanceDO
     */
    void reissueFormAdd(AttendanceFormDO formDO,
                        List<AttendanceFormRelationDO> relationDOS,
                        List<AttendanceFormAttrDO> applicationFormAttrArrayList,
                        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                        UserCycleReissueCardCountDO userCardConfigDO,
                        EmployeeAbnormalAttendanceDO abnormalAttendanceDO);

    /**
     * 考勤补时长单据新增（聚合）
     * @param formDO
     * @param relationList
     * @param applicationFormAttrArrayList
     * @param employeeAbnormalOperationRecordDO
     * @param abnormalAttendanceDO
     */
    void addDurationAdd(AttendanceFormDO formDO,
                        List<AttendanceFormRelationDO> relationList,
                        List<AttendanceFormAttrDO> applicationFormAttrArrayList,
                        EmployeeAbnormalOperationRecordDO employeeAbnormalOperationRecordDO,
                        EmployeeAbnormalAttendanceDO abnormalAttendanceDO);

    void addDurationUpdate(AttendanceFormDO formDO,
                           AttendanceEmployeeDetailDO attendanceEmployeeDetailDO,
                           EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                           WarehouseDetailDO warehouseDetailDO,
                           WarehouseDetailAbnormalDO warehouseDetailAbnormalDO);


    /**
     * 考勤补卡审批通过（聚合）
     * @param formDO
     * @param employeePunchRecordDO
     * @param abnormalAttendanceDO
     * @param warehouseRecordDO
     */
    void reissueCardMqPassUpdate(AttendanceFormDO formDO,
                                 EmployeePunchRecordDO employeePunchRecordDO,
                                 EmployeeAbnormalAttendanceDO abnormalAttendanceDO,
                                 WarehouseRecordDO warehouseRecordDO);

    /**
     * 考勤请假审批通过（聚合）
     * @param formDO
     * @param abnormalAttendanceDO
     */
    void leaveMqPassUpdate(AttendanceFormDO formDO,
                           EmployeeAbnormalAttendanceDO abnormalAttendanceDO);

    /**
     * 考勤补卡撤销审批通过（聚合）
     * @param formDOList
     * @param attrDO
     * @param employeePunchRecordDOS
     * @param cardConfigDO
     */
    void revokeReissueCardMqPassUpdate(List<AttendanceFormDO> formDOList,
                                       AttendanceFormAttrDO attrDO,
                                       List<EmployeePunchRecordDO> employeePunchRecordDOS,
                                       UserCycleReissueCardCountDO cardConfigDO);

    /**
     * 考勤请假撤销审批通过（聚合）
     * @param formDOList
     * @param attrDO
     * @param updateStageList
     * @param addLeaveRecordList
     * @param employeeDetailDOS
     */
    void revokeLeaveMqPassUpdate(List<AttendanceFormDO> formDOList,
                                 AttendanceFormAttrDO attrDO,
                                 List<UserLeaveStageDetailDO> updateStageList,
                                 List<UserLeaveRecordDO> addLeaveRecordList,
                                 List<AttendanceEmployeeDetailDO> employeeDetailDOS);

    void approvalFormAdd(AttendanceApprovalFormDO approvalForm,
                         List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList);

    void approvalFormUpdate(AttendanceApprovalFormDO approvalForm,
                            List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList);

    void approvalFormAddAndUpdate(AttendanceApprovalFormDO approvalForm,
                                  List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList);

    void approvalFormOrLeaveUpdate(AttendanceApprovalFormDO approvalForm,
                                   List<AttendanceApprovalFormUserInfoDO> approvalFormUserInfoList,
                                   List<UserLeaveStageDetailDO> updateUserLeaveStageDetail,
                                   List<UserLeaveRecordDO> addUserLeaveRecord);
}
