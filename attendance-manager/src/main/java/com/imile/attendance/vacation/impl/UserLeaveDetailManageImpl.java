package com.imile.attendance.vacation.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveBalanceDTO;
import com.imile.attendance.infrastructure.repository.employee.mapper.UserLeaveDetailMapper;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022-05-23
 */
@Service
@Slf4j
public class UserLeaveDetailManageImpl implements UserLeaveDetailManage {

    @Resource
    private UserLeaveDetailDao userLeaveDetailDao;
    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;
    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;
    @Resource
    private UserLeaveDetailMapper userLeaveDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userLeaveMinutesInitUpdate(List<UserLeaveStageDetailDO> updateDetailList,
                                           List<UserLeaveRecordDO> updateRecordList) {
        if (CollectionUtils.isNotEmpty(updateDetailList)) {
            userLeaveStageDetailDao.updateBatchById(updateDetailList);
        }
        if (CollectionUtils.isNotEmpty(updateRecordList)) {
            userLeaveRecordDao.updateBatchById(updateRecordList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userLeaveBalanceDaysUpdate(List<UserLeaveDetailDO> addUserLeaveDetailList,
                                           List<UserLeaveStageDetailDO> addUserLeaveStageDetailList,
                                           List<UserLeaveRecordDO> addUserLeaveRecordList,
                                           List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList,
                                           List<UserLeaveDetailDO> updateUserLeaveDetailDOList) {
        if (CollectionUtils.isNotEmpty(addUserLeaveDetailList)) {
            userLeaveDetailDao.saveBatch(addUserLeaveDetailList);
        }
        if (CollectionUtils.isNotEmpty(addUserLeaveStageDetailList)) {
            userLeaveStageDetailDao.saveBatch(addUserLeaveStageDetailList);
        }
        if (CollectionUtils.isNotEmpty(addUserLeaveRecordList)) {
            userLeaveRecordDao.saveBatch(addUserLeaveRecordList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveStageDetailList)) {
            userLeaveStageDetailDao.updateBatchById(updateUserLeaveStageDetailList);
        }
        if (CollectionUtils.isNotEmpty(updateUserLeaveDetailDOList)) {
            userLeaveDetailDao.updateBatchById(updateUserLeaveDetailDOList);
        }
    }

    @Override
    public List<UserLeaveDetailDO> listByUserId(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)){
            return Collections.emptyList();
        }
        UserLeaveDetailQuery userLeaveDetailQuery = new UserLeaveDetailQuery();
        userLeaveDetailQuery.setUserIds(userIdList);
        userLeaveDetailQuery.setStatus(StatusEnum.ACTIVE.getCode());
        return userLeaveDetailDao.selectUserLeaveDetail(userLeaveDetailQuery);
    }

    @Override
    public List<UserLeaveBalanceDTO> selectBatchUserResidual(List<Long> userIds) {
        List<UserLeaveBalanceDTO> userLeaveResidualDTOS = userLeaveDetailMapper.selectBatchUserResidual(userIds);
        if (CollectionUtils.isEmpty(userLeaveResidualDTOS)) {
            return Lists.newArrayList();
        }
        return userLeaveResidualDTOS;
    }
}
