package com.imile.attendance.rule.impl;

import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dto.RuleConfigModifyDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.bo.OverTimeConfigBO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
@Component
public class OverTimeConfigManageImpl implements OverTimeConfigManage {

    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private AttendanceUserService userService;

    @Override
    public void configRangeUpdateOrAdd(List<OverTimeConfigRangeDO> updateList, List<OverTimeConfigRangeDO> addList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            overTimeConfigRangeDao.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            overTimeConfigRangeDao.saveBatch(addList);
        }
    }

    @Override
    public void configUpdateAndAdd(OverTimeConfigDO updateConfig, OverTimeConfigDO addConfig) {
        if (null != updateConfig) {
            overTimeConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            overTimeConfigDao.save(addConfig);
        }
    }

    @Override
    public void configUpdateAndAdd(OverTimeConfigDO updateConfig, OverTimeConfigDO addConfig,
                                   List<OverTimeConfigRangeDO> updatedConfigRanges, List<OverTimeConfigRangeDO> addConfigRanges) {
        if (null != updateConfig) {
            overTimeConfigDao.updateById(updateConfig);
        }
        if (null != addConfig) {
            overTimeConfigDao.save(addConfig);
        }
        if (CollectionUtils.isNotEmpty(updatedConfigRanges)) {
            overTimeConfigRangeDao.updateBatchById(updatedConfigRanges);
        }
        if (CollectionUtils.isNotEmpty(addConfigRanges)) {
            overTimeConfigRangeDao.saveBatch(addConfigRanges);
        }
    }

    @Override
    public OverTimeConfigDO getOverTimeConfigById(Long overTimeConfigId) {
        return overTimeConfigDao.getById(overTimeConfigId);
    }

    @Override
    public List<OverTimeConfigDO> getOverTimeConfigByIds(List<Long> overTimeConfigIds) {
        return overTimeConfigDao.listByConfigIds(overTimeConfigIds);
    }

    @Override
    public OverTimeConfigBO getConfigBO(String configNo) {
        if (StringUtils.isEmpty(configNo)) {
            return null;
        }
        OverTimeConfigDO overTimeConfigDO = overTimeConfigDao.getLatestByConfigNo(configNo);
        if (null == overTimeConfigDO) {
            return null;
        }
        return OverTimeConfigBO.of(overTimeConfigDO,
                overTimeConfigRangeDao.listByConfigId(overTimeConfigDO.getId()));
    }

    @Override
    public CountryOverTimeConfig getCountryConfig(String country) {
        if (StringUtils.isEmpty(country)) {
            return CountryOverTimeConfig.empty();
        }
        List<OverTimeConfigDO> countryConfigs = overTimeConfigDao.getByCountry(country);
        return CountryOverTimeConfig.of(country, countryConfigs);
    }

    @Override
    public List<CountryOverTimeConfig> getCountryListConfig(List<String> countries) {
        List<CountryOverTimeConfig> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(countries)) {
            return list;
        }
        List<OverTimeConfigDO> countryConfigs = overTimeConfigDao.listByCountries(countries);
        if (CollectionUtils.isEmpty(countryConfigs)) {
            return list;
        }
        Map<String, List<OverTimeConfigDO>> countryConfigMap = countryConfigs.stream()
                .collect(Collectors.groupingBy(OverTimeConfigDO::getCountry));
        countryConfigMap.forEach((country, configs) -> {
            list.add(CountryOverTimeConfig.of(country, configs));
        });
        return list;
    }


    @Override
    public Map<Long, OverTimeConfigDO> getConfigMapByUserIdList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        // 查询用户
        List<AttendanceUser> users = userService.listUsersByIds(userIds);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }
        Map<Long, OverTimeConfigDO> userConfigMap = new HashMap<>();
        // 查询用户是否在范围表里
        List<OverTimeConfigRangeDO> rangeList = overTimeConfigRangeDao.listConfigRanges(userIds);
        if (CollectionUtils.isEmpty(rangeList)) {
            return userConfigMap;
        }

        // 查询<userId, OverTimeConfigRangeDO>
        Map<Long, OverTimeConfigRangeDO> rangeMap = rangeList.stream()
                .collect(Collectors.toMap(OverTimeConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));

        // 查询<ruleConfigId, OverTimeConfigDO>
        Map<Long, OverTimeConfigDO> configMap = overTimeConfigDao.listLatestByConfigIds(
                rangeList.stream()
                        .map(OverTimeConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(OverTimeConfigDO::getId, Function.identity(), (a, b) -> a));

        // 查询在配置范围内的用户
        List<Long> inRangeUserIdList = new ArrayList<>(rangeMap.keySet());
        inRangeUserIdList.forEach(inRangeUserId -> {
            OverTimeConfigRangeDO overTimeConfigRangeDO = rangeMap.get(inRangeUserId);
            if (overTimeConfigRangeDO != null) {
                Long ruleConfigId = overTimeConfigRangeDO.getRuleConfigId();
                OverTimeConfigDO overTimeConfigDO = configMap.get(ruleConfigId);
                if (overTimeConfigDO != null) {
                    userConfigMap.put(inRangeUserId, overTimeConfigDO);
                }
            }
        });
        return userConfigMap;
    }

    @Override
    public Map<Long, OverTimeConfigDO> mapByUserIds(List<Long> userIds, Date endDate) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyMap();
        }
        long endDateTimeStamp = endDate.getTime();
        List<OverTimeConfigRangeDO> configRangeDOList = overTimeConfigRangeDao.listAllRangeByUserIds(userIds)
                .stream()
                .filter(item -> item.getEffectTimestamp().compareTo(endDateTimeStamp) < 1 &&
                        item.getExpireTimestamp().compareTo(endDateTimeStamp) > -1)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(configRangeDOList)) {
            return Collections.emptyMap();
        }

        Map<Long, OverTimeConfigRangeDO> rangeMap = configRangeDOList.stream()
                .collect(Collectors.toMap(OverTimeConfigRangeDO::getBizId, Function.identity(), (a, b) -> a));


        Map<Long, OverTimeConfigDO> configMap = overTimeConfigDao.listByConfigIds(configRangeDOList.stream()
                        .map(OverTimeConfigRangeDO::getRuleConfigId)
                        .distinct()
                        .collect(Collectors.toList()))
                .stream()
                .collect(Collectors.toMap(OverTimeConfigDO::getId, Function.identity(), (a, b) -> a));

        Map<Long, OverTimeConfigDO> userConfigMap = new HashMap<>();
        for (Long userId : rangeMap.keySet()) {
            OverTimeConfigRangeDO configRangeDO = rangeMap.get(userId);
            if (Objects.isNull(configMap.get(configRangeDO.getRuleConfigId()))) {
                continue;
            }
            userConfigMap.put(userId, configMap.get(configRangeDO.getRuleConfigId()));
        }
        return userConfigMap;
    }

    @Override
    public OverTimeConfigDO selectByUserIdAndDate(Long userId, Date endDate) {
        Map<Long, OverTimeConfigDO> overTimeConfigMap = mapByUserIds(Collections.singletonList(userId), endDate);
        if (MapUtils.isEmpty(overTimeConfigMap)){
            return null;
        }
        return overTimeConfigMap.get(userId);
    }

    @Override
    public OverTimeConfigBO getConfigBOByUserId(Long userId) {
        if (null == userId) {
            return null;
        }
        // 查询用户
        AttendanceUser user = userService.getByUserId(userId);
        if (null == user) {
            return null;
        }
        // 查询用户是否在范围表里
        List<OverTimeConfigRangeDO> rangeList = overTimeConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        // 如果范围表里没有用户，则查询国家级别的规则
        if (CollectionUtils.isEmpty(rangeList)) {
            List<OverTimeConfigDO> countryLevelConfigs = overTimeConfigDao.listCountryLevelConfigsByCountries(
                    Collections.singletonList(user.getLocationCountry()));
            if (CollectionUtils.isEmpty(countryLevelConfigs)) {
                return null;
            }
            return OverTimeConfigBO.buildCountryLevelConfigBO(countryLevelConfigs.get(0));
        }
        OverTimeConfigRangeDO overTimeConfigRangeDO = rangeList.get(0);
        // 根据规则编码查询规则业务
        return getConfigBO(overTimeConfigRangeDO.getRuleConfigNo());
    }

    @Override
    public List<RuleConfigModifyDTO> selectAllByBizId(Long bizId) {
        List<OverTimeConfigRangeDO> overTimeConfigRangeDOList = overTimeConfigRangeDao.listAllConfigRanges(bizId);
        if (CollectionUtils.isEmpty(overTimeConfigRangeDOList)) {
            return Collections.emptyList();
        }
        List<Long> ruleConfigIdList = overTimeConfigRangeDOList.stream().map(OverTimeConfigRangeDO::getRuleConfigId).distinct().collect(Collectors.toList());
        Map<Long, OverTimeConfigDO> overTimeConfigMap = overTimeConfigDao.listByConfigIds(ruleConfigIdList)
                .stream().collect(Collectors.toMap(OverTimeConfigDO::getId, Function.identity()));
        return overTimeConfigRangeDOList.stream().map(range -> {
            RuleConfigModifyDTO modifyDTO = new RuleConfigModifyDTO();
            OverTimeConfigDO overTimeConfigDO = overTimeConfigMap.getOrDefault(range.getRuleConfigId(),new OverTimeConfigDO());
            modifyDTO.setCountry(overTimeConfigDO.getCountry());
            modifyDTO.setRuleName(overTimeConfigDO.getConfigName());
            modifyDTO.setStartDate(range.getEffectTime());
            modifyDTO.setEndDate(range.getExpireTime());
            modifyDTO.setCreateUserName(range.getCreateUserName());
            return modifyDTO;
        }).collect(Collectors.toList());
    }
}
