package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.imile.attendance.hrms.RpcHrAbnormalClient;
import com.imile.attendance.infrastructure.adapter.AbstractPairAdapter;
import com.imile.attendance.infrastructure.adapter.DaoAdapter;
import com.imile.attendance.infrastructure.adapter.DataConverter;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import com.imile.hrms.api.attendance.dto.HrmsAttendanceEmployeeDetailDTO;
import com.imile.util.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component
public class AttendanceEmployeeDetailAdapter extends AbstractPairAdapter<AttendanceEmployeeDetailDO, HrmsAttendanceEmployeeDetailDO> implements DaoAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private RpcHrAbnormalClient rpcHrAbnormalClient;

    public AttendanceEmployeeDetailAdapter(List<DataConverter<AttendanceEmployeeDetailDO, HrmsAttendanceEmployeeDetailDO>> dataConverters) {
        super(dataConverters);
    }

    @Override
    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled();
    }

    //=====================dao层适配===============================


    public void save(AttendanceEmployeeDetailDO attendanceEmployeeDetailDO) {
       /* saveOrUpdateOneNewWrapper(
                attendanceEmployeeDetailDO,
                newData -> attendanceEmployeeDetailDao.save(newData),
                oldData -> hrmsAttendanceEmployeeDetailDao.save(oldData)
        );*/

        attendanceEmployeeDetailDao.save(attendanceEmployeeDetailDO);
        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                HrmsAttendanceEmployeeDetailDTO attendanceEmployeeDetailDTO = BeanUtils.convert(attendanceEmployeeDetailDO, HrmsAttendanceEmployeeDetailDTO.class);
                rpcHrAbnormalClient.attendanceEmployeeDetailBatchSave(Collections.singletonList(attendanceEmployeeDetailDTO));
            });
        }
    }

    public void updateBatchById(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList) {
//        saveOrUpdateBatchNewWrapper(
//                attendanceEmployeeDetailDOList,
//                newRecordList -> attendanceEmployeeDetailDao.updateBatchById(newRecordList),
//                oldRecordList -> hrmsAttendanceEmployeeDetailDao.updateBatchById(oldRecordList)
//        );

        attendanceEmployeeDetailDao.updateBatchById(attendanceEmployeeDetailDOList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsAttendanceEmployeeDetailDTO> attendanceEmployeeDetailDTOList = BeanUtils.convert(HrmsAttendanceEmployeeDetailDTO.class, attendanceEmployeeDetailDOList);
                rpcHrAbnormalClient.attendanceEmployeeDetaillBatchUpdate(attendanceEmployeeDetailDTOList);
            });
        }
    }

    public void saveBatch(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList) {
    /*    saveOrUpdateBatchNewWrapper(
                attendanceEmployeeDetailDOList,
                newRecordList -> attendanceEmployeeDetailDao.saveBatch(newRecordList),
                oldRecordList -> hrmsAttendanceEmployeeDetailDao.saveBatch(oldRecordList)
        );*/

        attendanceEmployeeDetailDao.saveBatch(attendanceEmployeeDetailDOList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsAttendanceEmployeeDetailDTO> attendanceEmployeeDetailDTOList = BeanUtils.convert(HrmsAttendanceEmployeeDetailDTO.class, attendanceEmployeeDetailDOList);
                rpcHrAbnormalClient.attendanceEmployeeDetailBatchSave(attendanceEmployeeDetailDTOList);
            });
        }
    }
}