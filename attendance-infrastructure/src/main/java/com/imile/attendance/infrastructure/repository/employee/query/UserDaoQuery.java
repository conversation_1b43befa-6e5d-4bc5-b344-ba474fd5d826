package com.imile.attendance.infrastructure.repository.employee.query;

import com.imile.common.enums.IsDeleteEnum;
import lombok.AllArgsConstructor;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/21
 * @Description
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class UserDaoQuery {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户id
     */
    private List<Long> userIds;

    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户名英文（公司花名）
     */
    private String userNameEn;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 是否删除数据
     */
    private Integer isDelete;
    /**
     * 员工工作状态 在职/离职
     */
    private String workStatus;
    /**
     * 员工工作状态 在职/离职
     */
    private List<String> workStatusList;
    /**
     * 账号启用状态
     */
    private String status;
    /**
     * 是否需要做子公司隔离
     */
    private Boolean isNeedSeparate;
    /**
     * 用户编码
     */
    private List<String> userCodes;
    /**
     * 部门id
     */
    private Collection<Long> deptIds;
    /**
     * 网点id
     */
    private Collection<Long> ocIds;
    /**
     * 供应商id
     */
    private Collection<Long> vendorIds;
    /**
     * 邮箱列表
     */
    private Collection<String> emails;
    /**
     * 岗位id
     */
    private Collection<Long> postIds;

    /**
     * 是否司机 0：否 1：是
     */
    private Integer isDriver;
    /**
     * 是否仓内 0：否 1：是
     */
    private Integer isWarehouseStaff;

    /**
     * 国家,注意：这个参数在userInfoDao.userList里没有处理
     */
    private String country;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 账号或姓名
     */
    private String codeOrName;

    /**
     * 账号或姓名(模糊搜索)
     */
    private String codeOrNameLike;

    /**
     * 职级id
     */
    private Long gradeId;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 性别
     */
    private Integer sex;

    /**
     * 省份
     */
    private List<String> localProviceList;

    /**
     * 国籍
     */
    private List<String> countryCodeList;

    /**
     * 是否全球派遣
     */
    private Integer isGlobalRelocation;

    private String locationCountry;
    private List<String> locationCountryList;
    private List<String> locationCityList;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 关键字
     */
    private String keyword;

    /**
     * 是否需要查询特殊的国家，影响用工类型
     */
    private Boolean isNeedQuerySpecialCountry = false;

    private List<String> specialCountryList;

    private List<Long> specialDeptList;

    private List<String> normalCountryList;

    private List<Long> normalDeptList;

    private List<String> specialEmployeeTypeList;

    private List<String> normalEmployeeTypeList;

    public Integer getIsDelete() {
        return isDelete == null ? IsDeleteEnum.NO.getCode() : isDelete;
    }
}
