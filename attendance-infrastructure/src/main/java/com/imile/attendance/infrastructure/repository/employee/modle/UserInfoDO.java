package com.imile.attendance.infrastructure.repository.employee.modle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imile.attendance.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <p>
 * 用户表 系统-员工
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-11
 */
@ApiModel(description = "用户信息表")
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("user_info")
public class UserInfoDO extends BaseDO {

    @ApiModelProperty(value = "员工id")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "人员编码")
    private String userCode;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "英文名")
    private String userNameEn;

    @ApiModelProperty(value = "性别(1:男,2:女)")
    private Integer sex;

    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "员工头像地址")
    private String profilePhotoUrl;

    @ApiModelProperty(value = "所属国家编码")
    private String countryCode;

    /**
     * 所属国
     */
    @ApiModelProperty(value = "所属国")
    private String originCountry;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "常驻地国家")
    private String locationCountry;

    @ApiModelProperty(value = "常驻地省份")
    private String locationProvince;

    @ApiModelProperty(value = "常驻地城市")
    private String locationCity;

    @ApiModelProperty(value = "是否全球派遣（0:否 1:是）")
    private Integer isGlobalRelocation;

    @ApiModelProperty(value = "用工类型（详见数据字典EmploymentType)")
    private String employeeType;

    @ApiModelProperty(value = "是否司机")
    private Integer isDriver;

    @ApiModelProperty(value = "是否司机leader")
    private Integer isDtl;

    @ApiModelProperty(value = "是否仓内员工")
    private Integer isWarehouseStaff;

    @ApiModelProperty(value = "岗位ID")
    private Long postId;

    @ApiModelProperty(value = "所属部门ID：hrms_ent_dept.id")
    private Long deptId;

    @ApiModelProperty(value = "网点ID")
    private Long ocId;

    @ApiModelProperty(value = "网点编码")
    private String ocCode;

    @ApiModelProperty(value = "供应商ID")
    private Long vendorId;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "结算主体编码")
    private String settlementCenterCode;

    @ApiModelProperty(value = "公司邮箱")
    private String email;

    @ApiModelProperty(value = "状态(ACTIVE 生效,DISABLED)")
    private String status;

    @ApiModelProperty(value = "工作状态 在职、离职等")
    private String workStatus;

    @ApiModelProperty(value = "班次性质（FIXED_CLASS,MULTIPLE_CLASS）")
    private String classNature;

    @ApiModelProperty(value = "账号冻结时间")
    private Date disabledDate;

    @ApiModelProperty(value = "用工形式")
    private String employeeForm;
}


