package com.imile.attendance.infrastructure.repository.rule.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigPageQuery;
import com.imile.attendance.infrastructure.repository.rule.query.PunchConfigQuery;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/7
 * @Description
 */
public interface PunchConfigDao extends IService<PunchConfigDO> {

    /**
     * 根据名称获取打卡配置
     * 
     * @param name 名称
     * @return 打卡配置
     */
    PunchConfigDO getByName(String name);

    /**
     * 根据国家获取打卡配置
     * 
     * @param country 国家代码
     * @return 打卡配置列表
     */
    List<PunchConfigDO> getByCountry(String country);


    /**
     * 根据国家列表获取打卡配置
     * 
     * @param countryList 国家列表
     * @return 打卡配置列表
     */
    List<PunchConfigDO> getByCountries(List<String> countryList);

    /**
     * 查询配置（非已删除的，不区分是否启用）
     * 
     * @param configIdList 配置ID列表
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listByConfigIds(List<Long> configIdList);

    /**
     * 查询配置（最新且启用的）
     * 
     * @param configIdList 配置ID列表
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listLatestByConfigIds(List<Long> configIdList);

    /**
     * 根据configNo获取最新打卡规则
     * 
     * @param configNo 配置编码
     * @return 打卡配置
     */
    PunchConfigDO getLatestByConfigNo(String configNo);

    /**
     * 根据configNos获取最新打卡规则
     * 
     * @param configNos 配置编码列表
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listLatestByConfigNos(List<String> configNos);

    /**
     * 根据configNo获取最新的规则，不区分是否启用
     * 
     * @param configNo 配置编码
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listByConfigNo(String configNo);

    /**
     * 打卡规则列表查询
     * 
     * @param query 查询条件
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listByQuery(PunchConfigQuery query);

    /**
     * 根据国家列表查询国家级规则
     * 
     * @param countryList 国家列表
     * @return 打卡配置列表
     */
    List<PunchConfigDO> listCountryLevelConfigsByCountries(List<String> countryList);

    /**
     * 分页查询打卡规则
     *
     * @param query 查询条件
     * @return 打卡配置列表
     */
    List<PunchConfigDO> pageQuery(PunchConfigPageQuery query);

    /**
     * 根据国家+部门+打卡类型查询最新得记录
     */
    List<PunchConfigDO> selectLatestAndActiveByCondition(String country,
                                                         Long deptId, String punchConfigType);

}
