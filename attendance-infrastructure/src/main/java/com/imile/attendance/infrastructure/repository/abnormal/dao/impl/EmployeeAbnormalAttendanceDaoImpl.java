package com.imile.attendance.infrastructure.repository.abnormal.dao.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dto.EmployeeAbnormalAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.mapper.EmployeeAbnormalAttendanceMapper;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalAttendanceQuery;
import com.imile.attendance.infrastructure.repository.abnormal.query.EmployeeAbnormalAttendancePageQuery;
import com.imile.common.enums.IsDeleteEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 员工异常考勤数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-22
 */
@Service
public class EmployeeAbnormalAttendanceDaoImpl extends ServiceImpl<EmployeeAbnormalAttendanceMapper, EmployeeAbnormalAttendanceDO> implements EmployeeAbnormalAttendanceDao {

    @Resource
    private EmployeeAbnormalAttendanceMapper employeeAbnormalAttendanceMapper;

    @Override
    public EmployeeAbnormalAttendanceDO selectById(Long abnormalId) {
        if (Objects.isNull(abnormalId)) {
            return null;
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(EmployeeAbnormalAttendanceDO::getId, abnormalId);
        wrapper.eq(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.getOne(wrapper);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> listAbnormal(AbnormalAttendanceQuery query) {
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(query.getDayId())) {
            queryWrapper.ge(EmployeeAbnormalAttendanceDO::getDayId, Integer.valueOf(query.getDayId()));
        }
        if (StringUtils.isNotBlank(query.getStatus())) {
            queryWrapper.eq(EmployeeAbnormalAttendanceDO::getStatus, query.getStatus());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIds())) {
            queryWrapper.in(EmployeeAbnormalAttendanceDO::getUserId, query.getUserIds());
        }
        if (CollectionUtils.isNotEmpty(query.getDayIds())) {
            queryWrapper.in(EmployeeAbnormalAttendanceDO::getDayId, query.getDayIds());
        }
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            queryWrapper.in(EmployeeAbnormalAttendanceDO::getUserId, query.getUserIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getDeptIdList())) {
            queryWrapper.in(EmployeeAbnormalAttendanceDO::getDeptId, query.getDeptIdList());
        }
        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
            queryWrapper.in(EmployeeAbnormalAttendanceDO::getLocationCountry, query.getCountryList());
        }
        if (Objects.nonNull(query.getStartDayId())) {
            queryWrapper.ge(EmployeeAbnormalAttendanceDO::getDayId, query.getStartDayId());
        }
        queryWrapper.in(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.orderByAsc(EmployeeAbnormalAttendanceDO::getCreateDate);
        return this.list(queryWrapper);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return this.listByIds(idList);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalAttendanceByDayIdList(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(EmployeeAbnormalAttendanceDO::getUserId, userIdList);
        wrapper.in(EmployeeAbnormalAttendanceDO::getDayId, dayIdList);
        wrapper.eq(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return this.list(wrapper);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CollUtil.isNotEmpty(userIdList), EmployeeAbnormalAttendanceDO::getUserId, userIdList);
        wrapper.eq(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        wrapper.notIn(EmployeeAbnormalAttendanceDO::getStatus, AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED);
        return this.list(wrapper);
    }

    @Override
//    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public List<EmployeeAbnormalAttendanceDTO> list(EmployeeAbnormalAttendancePageQuery query) {
        return employeeAbnormalAttendanceMapper.list(query);
    }


    @Override
    //    @DS(HrmsProperties.Database.HRMS_RO_URL)
    public Integer selectCount(EmployeeAbnormalAttendancePageQuery query) {
        return employeeAbnormalAttendanceMapper.listCount(query);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserIdAndDayId(Long userId, Long dayId) {
        if (Objects.isNull(userId) || Objects.isNull(dayId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(EmployeeAbnormalAttendanceDO::getDayId, dayId);
        queryWrapper.eq(EmployeeAbnormalAttendanceDO::getUserId, userId);
        return list(queryWrapper);
    }

    @Override
    public List<EmployeeAbnormalAttendanceDO> selectAbnormalByUserId(Long userId, Long startDayId, Long endDayId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EmployeeAbnormalAttendanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EmployeeAbnormalAttendanceDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(EmployeeAbnormalAttendanceDO::getUserId, userId);
        if (startDayId != null) {
            queryWrapper.ge(EmployeeAbnormalAttendanceDO::getDayId, startDayId);
        }
        if (endDayId != null) {
            queryWrapper.le(EmployeeAbnormalAttendanceDO::getDayId, endDayId);
        }
        return list(queryWrapper);
    }
}
