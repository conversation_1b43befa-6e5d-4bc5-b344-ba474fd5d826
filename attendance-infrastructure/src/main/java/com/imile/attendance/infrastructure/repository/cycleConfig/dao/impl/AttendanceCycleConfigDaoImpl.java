package com.imile.attendance.infrastructure.repository.cycleConfig.dao.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.infrastructure.repository.cycleConfig.mapper.AttendanceCycleConfigMapper;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigPageQuery;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.infrastructure.repository.cycleConfig.dao.AttendanceCycleConfigDao;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Component
@RequiredArgsConstructor
public class AttendanceCycleConfigDaoImpl extends ServiceImpl<AttendanceCycleConfigMapper, AttendanceCycleConfigDO> implements AttendanceCycleConfigDao {

    @Override
    public List<AttendanceCycleConfigDO> selectByCondition(AttendanceCycleConfigQuery query) {
        if (ObjectUtil.isNull(query) || ObjectUtil.isEmpty(query.getCountry()) ||
                ObjectUtil.isEmpty(query.getCycleType())) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(ObjectUtil.isNotEmpty(query.getCountry()), AttendanceCycleConfigDO::getCountry, query.getCountry())
                .eq(ObjectUtil.isNotNull(query.getCycleType()), AttendanceCycleConfigDO::getCycleType, query.getCycleType())
                .eq(AttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }

    @Override
    public List<AttendanceCycleConfigDO> selectByCountiesAndCycleType(List<String> countries, Integer cycleType) {
        if (CollectionUtils.isEmpty(countries) || ObjectUtil.isNull(cycleType)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.in(AttendanceCycleConfigDO::getCountry, countries)
                .eq(AttendanceCycleConfigDO::getCycleType, cycleType)
                .eq(AttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode());
        return list(lambdaQuery);
    }

    @Override
    public void saveAndUpdateAttendanceCycleConfig(AttendanceCycleConfigDO oldAttendanceCycleConfig, AttendanceCycleConfigDO model) {
        if (ObjectUtil.isNotNull(oldAttendanceCycleConfig)) {
            updateById(oldAttendanceCycleConfig);
        }
        if (ObjectUtil.isNotNull(model)) {
            save(model);
        }
    }

    @Override
    public List<AttendanceCycleConfigDO> selectListByCondition(AttendanceCycleConfigPageQuery query) {
        if (ObjectUtil.isNull(query)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(ObjectUtil.isNotEmpty(query.getCountry()), AttendanceCycleConfigDO::getCountry, query.getCountry())
                .in(CollectionUtils.isNotEmpty(query.getCountryList()), AttendanceCycleConfigDO::getCountry, query.getCountryList())
                .eq(ObjectUtil.isNotEmpty(query.getStatus()), AttendanceCycleConfigDO::getStatus, query.getStatus());
        return list(lambdaQuery);
    }

    @Override
    public List<AttendanceCycleConfigDO> listByPage(int currentPage, int pageSize) {
        PageInfo<AttendanceCycleConfigDO> pageInfo = PageHelper.startPage(currentPage, pageSize)
                .doSelectPageInfo(() -> this.baseMapper.selectList(null));
        // 返回当前页的数据
        return pageInfo.getList();
    }

    @Override
    public List<AttendanceCycleConfigDO> getAllEnabled() {
        LambdaQueryWrapper<AttendanceCycleConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(AttendanceCycleConfigDO::getIsDelete, IsDeleteEnum.NO.getCode())
                .eq(AttendanceCycleConfigDO::getStatus, StatusEnum.ACTIVE.getCode());
        return list(lambdaQuery);
    }
}
