package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.hermes.RpcHermesEntOcClient;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Service
public class EntOcService {

    @Resource
    private RpcHermesEntOcClient rpcHermesEntOcClient;


    /**
     * 根据网点编码查询网点信息
     */
    public EntOcApiDTO getOcByCode(Long orgId, String ocCode){
        return rpcHermesEntOcClient.getOcByCode(orgId, ocCode);
    }


    /**
     * 根据网点编码查询网点信息
     */
    public List<EntOcApiDTO> getOcByCodes(Long orgId, List<String> ocCodes){
        return rpcHermesEntOcClient.getOcByCodes(orgId, ocCodes);
    }

    /**
     * 根据网点ID查询网点信息
     */
    public EntOcApiDTO getOcById(Long orgId, Long ocId){
        return rpcHermesEntOcClient.getOcById(orgId, ocId);
    }
}
