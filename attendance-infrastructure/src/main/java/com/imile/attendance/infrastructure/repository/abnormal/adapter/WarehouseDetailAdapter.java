package com.imile.attendance.infrastructure.repository.abnormal.adapter;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.hrms.RpcHrWarehouseClient;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.infrastructure.repository.abnormal.adapter.mapstruct.WarehouseDetailMapstruct;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.warehouse.dao.WarehouseDetailDao;
import com.imile.attendance.infrastructure.repository.warehouse.model.WarehouseDetailDO;
import com.imile.hrms.api.warehouse.dto.HrmsWarehouseDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/25
 */
@Slf4j
@Component
public class WarehouseDetailAdapter {

    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;
    @Resource
    private WarehouseDetailDao warehouseDetailDao;
    @Resource
    private RpcHrWarehouseClient hrWarehouseClient;
    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private Executor bizTaskThreadPool;

    public Boolean isDoubleWriteMode() {
        return enableNewAttendanceConfig.getWarehouseDoubleWriteEnabled();
    }

    //=====================dao层适配===============================

    public void saveOrUpdate(WarehouseDetailDO newRecord) {
        warehouseDetailDao.saveOrUpdate(newRecord);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                HrmsWarehouseDetailDTO hrmsWarehouseDetailDTO = convertHrmsWarehouseDetailDTO(newRecord);
                hrWarehouseClient.warehouseBatch(Collections.singletonList(hrmsWarehouseDetailDTO));
            });
        }

    }


    public void saveOrUpdateBatch(List<WarehouseDetailDO> newList) {
        warehouseDetailDao.saveOrUpdateBatch(newList);

        if (isDoubleWriteMode()) {
            bizTaskThreadPool.execute(() -> {
                List<HrmsWarehouseDetailDTO> hrmsWarehouseDetailDTOList = convertHrmsWarehouseDetailDTOList(newList);
                List<List<HrmsWarehouseDetailDTO>> partitionList = Lists.partition(hrmsWarehouseDetailDTOList, 200);
                partitionList.forEach(partition -> hrWarehouseClient.warehouseBatch(partition));
            });
        }
    }

    public HrmsWarehouseDetailDTO convertHrmsWarehouseDetailDTO(WarehouseDetailDO warehouseDetailDO) {
        MappingPunchClassConfigDO mappingPunchClassConfigDO = null;
        if (Objects.nonNull(warehouseDetailDO.getClassesId()) && warehouseDetailDO.getClassesId() > 0) {
            mappingPunchClassConfigDO = mappingPunchClassConfigDao.getByPunchClassConfigId(warehouseDetailDO.getClassesId());
        }
        HrmsWarehouseDetailDTO old = WarehouseDetailMapstruct.INSTANCE.mapToOld(warehouseDetailDO);
        old.setFromNewSystem(BusinessConstant.Y);
        if (Objects.nonNull(mappingPunchClassConfigDO)) {
            old.setClassesId(mappingPunchClassConfigDO.getHrPunchClassId());
        }
        return old;
    }

    public List<HrmsWarehouseDetailDTO> convertHrmsWarehouseDetailDTOList(List<WarehouseDetailDO> warehouseDetailDOList) {
        List<Long> punchClassIdList = warehouseDetailDOList.stream().map(WarehouseDetailDO::getClassesId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Long, Long> hrPunchClassIdMap = mappingPunchClassConfigDao.listByPunchClassConfigIds(punchClassIdList).stream()
                .collect(Collectors.toMap(MappingPunchClassConfigDO::getPunchClassConfigId, MappingPunchClassConfigDO::getHrPunchClassId, (v1, v2) -> v1));

        List<HrmsWarehouseDetailDTO> oldList = WarehouseDetailMapstruct.INSTANCE.mapToOldList(warehouseDetailDOList);
        oldList.forEach(old -> {
            old.setFromNewSystem(BusinessConstant.Y);
            if (Objects.nonNull(old.getClassesId()) && old.getClassesId() > 0) {
                old.setClassesId(hrPunchClassIdMap.getOrDefault(old.getClassesId(), old.getClassesId()));
            }
        });
        return oldList;
    }

}