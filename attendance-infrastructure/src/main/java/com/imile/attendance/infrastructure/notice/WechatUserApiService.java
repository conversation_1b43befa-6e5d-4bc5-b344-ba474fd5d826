package com.imile.attendance.infrastructure.notice;

import com.imile.attendance.hrms.support.RpcUserClientSupport;
import com.imile.attendance.infrastructure.notice.dto.UserWxInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/29 
 * @Description
 */
@Slf4j
@Service
public class WechatUserApiService {

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private RpcUserClientSupport rpcUserClientSupport;


    /**
     * 批量获取用户微信信息
     */
    public List<UserWxInfoDTO> batchGetUserWxIds(List<String> userCodes) {
        if (CollectionUtils.isEmpty(userCodes)) {
            return Collections.emptyList();
        }
        // 获取用户微信ID映射关系
        Map<String, String> userWecomIdMap = rpcUserClientSupport.getUserWecomIdMap(userCodes);

        if (MapUtils.isEmpty(userWecomIdMap)) {
            log.info("batchGetUserWxIds: 未找到用户微信关联关系, userCodes:{}", userCodes);
            return Collections.emptyList();
        }

        // 获取用户基本信息
        List<UserInfoDO> userInfoList = userInfoDao.listByUserCodes(userCodes);
        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("batchGetUserWxIds: 未找到用户信息, userCodes:{}", userCodes);
            return Collections.emptyList();
        }
        return userInfoList.stream()
                .filter(userInfo -> userWecomIdMap.containsKey(userInfo.getUserCode()))
                .map(userInfo -> {
                    UserWxInfoDTO userWxInfo = new UserWxInfoDTO();
                    userWxInfo.setUserCode(userInfo.getUserCode());
                    userWxInfo.setWxId(userWecomIdMap.get(userInfo.getUserCode()));
                    return userWxInfo;
                })
                .collect(Collectors.toList());
    }
}
