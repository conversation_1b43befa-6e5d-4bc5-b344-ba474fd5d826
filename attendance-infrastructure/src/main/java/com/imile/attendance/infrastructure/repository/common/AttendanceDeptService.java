package com.imile.attendance.infrastructure.repository.common;

import com.imile.attendance.hrms.RpcDeptClient;
import com.imile.attendance.hrms.support.RpcDeptClientSupport;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.UserSuperiorDeptOwnerDTO;
import com.imile.attendance.infrastructure.repository.common.mapstruct.CommonMapstruct;
import com.imile.hrms.api.organization.dto.DeptChainNodeDTO;
import com.imile.hrms.api.organization.dto.DeptDTO;
import com.imile.hrms.api.organization.dto.DeptOwnerDTO;
import com.imile.hrms.api.organization.query.DeptConditionParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 考勤使用部门服务类，提供部门相关的数据查询功能
 *
 * <AUTHOR> chen
 * @date 2025/3/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AttendanceDeptService {

    @Resource
    private RpcDeptClient rpcDeptClient;
    @Resource
    private RpcDeptClientSupport rpcDeptClientSupport;
    @Resource
    private AttendanceUserService userService;

    /**
     * 根据部门ID列表查询部门信息
     *
     * @param deptIdList 部门ID列表
     * @return 部门信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendanceDept> listByDeptIds(List<Long> deptIdList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }
        // 去重
        deptIdList = deptIdList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setIdList(deptIdList);
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    public List<AttendanceDept> listByCondition(DeptConditionParam deptConditionParam ) {
        if (Objects.isNull(deptConditionParam)) {
            return Collections.emptyList();
        }
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    /**
     * 根据网点编码查询部门信息
     * 改Rpc接口内部限制只查组织架构上的网点
     *
     * @param ocCodeList 网点编码列表
     * @return 部门信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendanceDept> listByOcCode(List<String> ocCodeList) {
        if (CollectionUtils.isEmpty(ocCodeList)) {
            return Collections.emptyList();
        }

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setOcCodeList(ocCodeList);
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    /**
     * 根据部门ID列表和状态查询部门信息
     * 注意当状态转入为空时,hrms仅返回启用的部门
     *
     * @param deptIdList 部门ID列表
     * @param statusList 状态列表
     * @return 部门信息列表
     */
    public List<AttendanceDept> listByDeptIdsAndStatus(List<Long> deptIdList, List<String> statusList) {
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setIdList(deptIdList);
        deptConditionParam.setStatusList(statusList);
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    /**
     * 根据部门ID列表查询部门信息
     *
     * @param deptIdSet 部门ID列表
     * @return 部门信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendanceDept> listByDeptIds(Set<Long> deptIdSet) {
        if (CollectionUtils.isEmpty(deptIdSet)) {
            return Collections.emptyList();
        }

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setIdList(new ArrayList<>(deptIdSet));
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    public List<AttendanceDept> listByDeptCodes(Set<String> deptCodeSet) {
        if (CollectionUtils.isEmpty(deptCodeSet)) {
            return Collections.emptyList();
        }

        DeptConditionParam deptConditionParam = new DeptConditionParam();
        deptConditionParam.setDeptCodeList(new ArrayList<>(deptCodeSet));
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }


    /**
     * 根据ids获取部门信息,hr这个方法查的时候没有过滤is_delete
     */
    public List<AttendanceDept> selectDeptByIds(List<Long> deptIdList) {
        return listByDeptIds(deptIdList);
    }

    /**
     * 根据部门ID获取部门信息
     *
     * @param deptId 部门ID
     * @return 单个部门信息
     * 注意：当传入null或未找到对应部门时，将返回null
     */
    public AttendanceDept getByDeptId(Long deptId) {
        if (null == deptId) {
            return null;
        }
        List<AttendanceDept> deptList = listByDeptIds(Collections.singletonList(deptId));
        if (CollectionUtils.isEmpty(deptList)) {
            return null;
        }
        return deptList.get(0);
    }

    public AttendanceDept getByDeptIdAndStatus(Long deptId, List<String> statusList) {
        if (null == deptId) {
            return null;
        }
        List<AttendanceDept> deptList = listByDeptIdsAndStatus(Collections.singletonList(deptId), statusList);
        if (CollectionUtils.isEmpty(deptList)) {
            return null;
        }
        return deptList.get(0);
    }

    /**
     * 查询一级部门
     *
     * @param rootDeptId 根部门ID
     * @param isNational 是否为国家
     * @return List<AttendanceDept>
     */
    public List<AttendanceDept> selectFirstDeptByRoot(Long rootDeptId, Boolean isNational) {
        return CommonMapstruct.INSTANCE.mapToDept(
                rpcDeptClientSupport.selectFirstDeptByRoot(rootDeptId, isNational));
    }

    /**
     * 通过地理国获取下面的部门（作废）
     *
     * @param country
     * @return
     */
    public List<AttendanceDept> listByCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            return Collections.emptyList();
        }
        List<AttendanceDept> attendanceDeptList = this.listAllDeptInfo();
        return Optional.ofNullable(attendanceDeptList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(country, item.getCountry()))
                .collect(Collectors.toList());
    }

    /**
     * 查询全量部门信息
     *
     * @return 部门信息列表
     * 注意：当传入空列表时，将返回空集合
     */
    public List<AttendanceDept> listAllDeptInfo() {
        DeptConditionParam deptConditionParam = new DeptConditionParam();
        List<DeptDTO> deptDTOList = rpcDeptClient.listDeptByCondition(deptConditionParam);
        return CommonMapstruct.INSTANCE.mapToDept(deptDTOList);
    }

    /**
     * 获取用户上级部门及HR负责人
     *
     * @param userId 用户id
     * @return UserSuperiorDeptOwnerDTO
     */
    public UserSuperiorDeptOwnerDTO getUserSuperiorDeptHROwner(Long userId) {
        if (Objects.isNull(userId)) {
            return null;
        }
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        // 部门编码=部门ID
        Long deptId = attendanceUser.getDeptId();
        if (deptId == null) {
            log.info("getUserSuperiorDeptOwner | deptId is null, userId:{}", userId);
            return null;
        }
        String deptCode = deptId.toString();
        // 根据所属部门编码查询部门全链路
        Map<String, List<DeptChainNodeDTO>> deptChainNodeListMap =
                rpcDeptClient.getDeptChainNodeListMap(Collections.singletonList(deptCode));
        List<DeptChainNodeDTO> deptChainNodeDTOList = deptChainNodeListMap.getOrDefault(deptCode, Collections.emptyList())
                .stream()
                .filter(deptChainNodeDTO -> StringUtils.isNotEmpty(deptChainNodeDTO.getDeptCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deptChainNodeDTOList)) {
            log.info("getUserSuperiorDeptOwner | deptChainNodeDTOList is empty, userId:{}, deptCode:{}", userId, deptCode);
            return null;
        }
        // 获取用户所属部门在部门链的位置，获取其上一级的部门（如果为1级部门，则取其本身）
        DeptChainNodeDTO userDeptChainNodeDTO = deptChainNodeDTOList.stream()
                .filter(deptChainNodeDTO -> StringUtils.equals(deptChainNodeDTO.getDeptCode(), deptCode))
                .findAny()
                .orElse(null);
        if (userDeptChainNodeDTO == null) {
            log.info("getUserSuperiorDeptOwner | userDeptChainNodeDTO is null, userId:{}, deptCode:{}", userId, deptCode);
            return null;
        }
        // 用户部门的层级
        Integer userDeptLevel = userDeptChainNodeDTO.getDeptLevel();
        // 获取用户的一二级部门，如果为2级或以下，则为1，2级部门，为1级则为1级部门
        List<DeptChainNodeDTO> superiorDeptChainNodeList = deptChainNodeDTOList.stream()
                .filter(deptChainNodeDTO -> {
                    if (userDeptLevel == 1) {
                        // 如果用户部门为1级部门，则取其本身
                        return deptChainNodeDTO.getDeptLevel().equals(userDeptLevel);
                    }
                    //2级或以下，则为1，2级部门
                    return deptChainNodeDTO.getDeptLevel().equals(2) ||
                            deptChainNodeDTO.getDeptLevel().equals(1);
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(superiorDeptChainNodeList)) {
            log.info("getUserSuperiorDeptOwner | superiorDeptChainNodeDTO is null, userId:{}, deptCode:{}", userId, deptCode);
            return null;
        }
        List<String> superiorDeptCodeList = superiorDeptChainNodeList.stream()
                .map(DeptChainNodeDTO::getDeptCode)
                .distinct()
                .collect(Collectors.toList());
        // 获取部门的hr负责人
        Map<String, List<DeptOwnerDTO>> deptHRLeaderOwnerDTOMap = rpcDeptClientSupport.getDeptHRLeaderOwnerDTO(superiorDeptCodeList);
        if (MapUtils.isEmpty(deptHRLeaderOwnerDTOMap)) {
            return null;
        }
        List<DeptOwnerDTO> superiorDeptOwnerList = deptHRLeaderOwnerDTOMap.values().stream()
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        return UserSuperiorDeptOwnerDTO.builder()
                .userId(attendanceUser.getId())
                .userCode(attendanceUser.getUserCode())
                .superiorDeptCode(StringUtils.join(deptHRLeaderOwnerDTOMap.keySet(), ","))
                .superiorDeptOwnerList(superiorDeptOwnerList)
                .build();
    }
}
