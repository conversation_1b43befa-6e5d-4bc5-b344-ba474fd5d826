package com.imile.attendance.infrastructure.repository.shift.dao.migrate.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.shift.dao.migrate.UserShiftConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.shift.dto.DayShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftConfigUpdateToOldDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.mapper.migrate.UserShiftConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工排班配置迁移表DAO实现类
 * 实现对user_shift_config_migrate表的具体数据访问操作
 *
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Service
public class UserShiftConfigMigrateDaoImpl extends ServiceImpl<UserShiftConfigMigrateMapper, UserShiftConfigMigrateDO> 
        implements UserShiftConfigMigrateDao {


    @Override
    public List<UserShiftConfigMigrateDO> selectUserShift(Long userId, Long startDayId, Long endDayId) {
        if (Objects.isNull(userId)) {
            log.debug("查询用户排班信息失败，用户ID不能为空");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.gt(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getUserId, userId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户排班信息完成, userId: {}, dayId范围: {} - {}, 结果数量: {}", 
                userId, startDayId, endDayId, result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectUserShiftByDayIds(Long userId, List<Long> dayIds) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(dayIds)) {
            log.debug("查询用户指定日期排班信息失败，参数不能为空: userId={}, dayIds={}", userId, dayIds);
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigMigrateDO::getUserId, userId);
        queryWrapper.in(UserShiftConfigMigrateDO::getDayId, dayIds);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户指定日期排班信息完成, userId: {}, dayIds数量: {}, 结果数量: {}", 
                userId, dayIds.size(), result.size());
        return result;
    }

    @Override
    @Transactional
    public void updateToDelete(List<DayShiftConfigDTO> dayShiftConfigDTOList) {
        if (CollectionUtils.isEmpty(dayShiftConfigDTOList)) {
            log.debug("逻辑删除排班信息失败，参数列表为空");
            return;
        }

        UserShiftConfigMigrateDO updateModel = new UserShiftConfigMigrateDO();
        BaseDOUtil.fillDOUpdate(updateModel);
        updateModel.setIsDelete(IsDeleteEnum.YES.getCode());

        List<Long> ids = dayShiftConfigDTOList.stream()
                .map(DayShiftConfigDTO::getId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<UserShiftConfigMigrateDO> updateWrapper = Wrappers.lambdaQuery();
        updateWrapper.in(UserShiftConfigMigrateDO::getId, ids);

        this.baseMapper.update(updateModel, updateWrapper);
        log.debug("逻辑删除排班信息完成, 删除数量: {}", ids.size());
    }

    @Override
    public int delete(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            log.debug("物理删除排班信息失败，ID列表为空");
            return 0;
        }

        int deletedCount = this.baseMapper.deleteBatchIds(ids);
        log.debug("物理删除排班信息完成, 删除数量: {}", deletedCount);
        return deletedCount;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectRecordByUserIdList(List<Long> userIdList, Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList)) {
            log.debug("查询用户列表排班信息失败，用户ID列表为空");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigMigrateDO::getUserId, userIdList);
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户列表排班信息完成, userIds数量: {}, dayId范围: {} - {}, 结果数量: {}",
                userIdList.size(), startDayId, endDayId, result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectRecordByUserIdListAndClassId(List<Long> userIdList, Long classId,
                                                                             Long startDayId, Long endDayId) {
        if (CollectionUtils.isEmpty(userIdList) || Objects.isNull(classId)) {
            log.debug("查询用户列表和班次排班信息失败，参数不能为空: userIdList={}, classId={}", userIdList, classId);
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigMigrateDO::getUserId, userIdList);
        queryWrapper.eq(UserShiftConfigMigrateDO::getPunchClassConfigId, classId);
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户列表和班次排班信息完成, userIds数量: {}, classId: {}, dayId范围: {} - {}, 结果数量: {}",
                userIdList.size(), classId, startDayId, endDayId, result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectRecordByDateRange(Long userId, Long startDayId, Long endDayId) {
        if (Objects.isNull(userId)) {
            log.debug("查询用户日期范围排班信息失败，用户ID不能为空");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.lt(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getUserId, userId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户日期范围排班信息完成, userId: {}, dayId范围: {} - {}, 结果数量: {}",
                userId, startDayId, endDayId, result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectRecordByDayList(Long userId, List<Long> dayIdList) {
        if (Objects.isNull(userId) || CollectionUtils.isEmpty(dayIdList)) {
            log.debug("查询用户指定日期列表排班信息失败，参数不能为空: userId={}, dayIdList={}", userId, dayIdList);
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(UserShiftConfigMigrateDO::getUserId, userId);
        queryWrapper.in(UserShiftConfigMigrateDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("查询用户指定日期列表排班信息完成, userId: {}, dayIdList数量: {}, 结果数量: {}",
                userId, dayIdList.size(), result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectBatchUserRecord(List<Long> userIdList, List<Long> dayIdList) {
        if (CollectionUtils.isEmpty(userIdList) || CollectionUtils.isEmpty(dayIdList)) {
            log.debug("批量查询用户排班记录失败，参数不能为空: userIdList={}, dayIdList={}", userIdList, dayIdList);
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(UserShiftConfigMigrateDO::getUserId, userIdList);
        queryWrapper.in(UserShiftConfigMigrateDO::getDayId, dayIdList);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("批量查询用户排班记录完成, userIds数量: {}, dayIds数量: {}, 结果数量: {}",
                userIdList.size(), dayIdList.size(), result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigMigrateDO> selectBatchByDate(Long startDayId, Long endDayId) {
        if (Objects.isNull(endDayId)) {
            log.debug("根据日期范围查询排班记录失败，结束日期不能为空");
            return new ArrayList<>();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(UserShiftConfigMigrateDO::getDayId, endDayId);
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());

        List<UserShiftConfigMigrateDO> result = this.baseMapper.selectList(queryWrapper);
        log.debug("根据日期范围查询排班记录完成, dayId范围: {} - {}, 结果数量: {}",
                startDayId, endDayId, result.size());
        return result;
    }

    @Override
    public List<UserShiftConfigDTO> page(UserShiftConfigQuery userShiftConfigQuery) {
        if (userShiftConfigQuery == null) {
            log.debug("分页查询排班配置失败，查询条件不能为空");
            return Collections.emptyList();
        }

        List<UserShiftConfigDTO> result = this.baseMapper.page(userShiftConfigQuery);
        log.debug("分页查询排班配置完成, 结果数量: {}", result.size());
        return result;
    }

    @Override
    public Long countMigratedDataForRollback(List<Long> punchConfigIds, Long startDayId, Long endDayId, String taskFlagPrefix) {
        if (CollectionUtils.isEmpty(punchConfigIds) || StringUtils.isBlank(taskFlagPrefix)) {
            log.debug("统计回滚数据失败，考勤组ID列表和任务标识前缀不能为空");
            return 0L;
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();

        // 直接通过班次配置ID列表查询
        queryWrapper.in(UserShiftConfigMigrateDO::getPunchClassConfigId, punchConfigIds);

        // 日期范围条件
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);

        // 任务标识条件，识别迁移数据
        queryWrapper.likeRight(UserShiftConfigMigrateDO::getTaskFlag, taskFlagPrefix);

        // 只查询未删除的最新数据
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        Integer count = this.baseMapper.selectCount(queryWrapper);
        log.debug("统计回滚数据完成, punchConfigIds数量: {}, dayId范围: {} - {}, taskFlagPrefix: {}, 结果数量: {}",
                punchConfigIds.size(), startDayId, endDayId, taskFlagPrefix, count);
        return Long.valueOf(count);
    }

    @Override
    public List<Long> selectMigratedDataIdsForRollback(List<Long> punchConfigIds, Long startDayId, Long endDayId,
                                                       String taskFlagPrefix, Integer offset, Integer limit) {
        if (CollectionUtils.isEmpty(punchConfigIds) || StringUtils.isBlank(taskFlagPrefix)) {
            log.debug("查询回滚数据ID失败，考勤组ID列表和任务标识前缀不能为空");
            return Collections.emptyList();
        }

        LambdaQueryWrapper<UserShiftConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();

        // 只查询ID字段，提高查询效率
        queryWrapper.select(UserShiftConfigMigrateDO::getId);

        // 直接通过班次配置ID列表查询
        queryWrapper.in(UserShiftConfigMigrateDO::getPunchClassConfigId, punchConfigIds);

        // 日期范围条件
        queryWrapper.ge(Objects.nonNull(startDayId), UserShiftConfigMigrateDO::getDayId, startDayId);
        queryWrapper.le(Objects.nonNull(endDayId), UserShiftConfigMigrateDO::getDayId, endDayId);

        // 任务标识条件，识别迁移数据
        queryWrapper.likeRight(UserShiftConfigMigrateDO::getTaskFlag, taskFlagPrefix);

        // 只查询未删除的最新数据
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsDelete, IsDeleteEnum.NO.getCode());
        queryWrapper.eq(UserShiftConfigMigrateDO::getIsLatest, BusinessConstant.Y);

        // 按ID排序，确保删除顺序的一致性
        queryWrapper.orderByAsc(UserShiftConfigMigrateDO::getId);

        // 分页查询
        if (Objects.nonNull(offset) && Objects.nonNull(limit)) {
            queryWrapper.last("LIMIT " + offset + ", " + limit);
        }

        List<UserShiftConfigMigrateDO> records = this.baseMapper.selectList(queryWrapper);
        List<Long> ids = records.stream()
                .map(UserShiftConfigMigrateDO::getId)
                .collect(Collectors.toList());

        log.debug("查询回滚数据ID完成, punchConfigIds数量: {}, dayId范围: {} - {}, taskFlagPrefix: {}, offset: {}, limit: {}, 结果数量: {}",
                punchConfigIds.size(), startDayId, endDayId, taskFlagPrefix, offset, limit, ids.size());
        return ids;
    }
}
