package com.imile.attendance.infrastructure.repository.report.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.infrastructure.repository.report.query.UserDayReportQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
public interface AttendanceDayReportDao extends IService<AttendanceDayReportDO> {

    AttendanceDayReportDO selectByUserCodeAndDayId(String userCode, Long dayId);

    /**
     * 日报关联用户查询
     */
    List<AttendanceDayReportDO> selectDayReportList(UserDayReportQuery query);

    /**
     * 批量查询用户的日报
     */
    List<AttendanceDayReportDO> selectByUserIdsAndDayIds(List<Long> userIds, List<Long> dayIds);


    /**
     * 月报分页查询人员(带考勤周期)
     */
    List<UserMonthReportBaseDTO> page(MonthReportListQuery monthReportListQuery);
}
